{"name": "lm-studio", "productName": "LM Studio", "version": "0.3.16+8", "description": "Discover, download, and run LLMs locally", "main": ".webpack/main/index.js", "private": true, "linux": {"target": ["AppImage"], "files": ["build/icons/png"]}, "bin": {"tm": "./build-scripts/taskmaster/bootstrap/bootstrap.mjs", "taskmaster": "./build-scripts/taskmaster/bootstrap/bootstrap.mjs"}, "author": {"name": "LM Studio <<EMAIL>>", "url": "https://lmstudio.ai", "email": "<EMAIL>"}, "homepage": "https://lmstudio.ai", "license": "other", "dependencies": {"@apidevtools/json-schema-ref-parser": "^11.7.2", "@aws-sdk/client-s3": "^3.716.0", "@electron/asar": "^3.2.4", "@electron/osx-sign": "^1.0.4", "@emotion/is-prop-valid": "^1.3.0", "@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.9.1", "@huggingface/gguf": "^0.1.10", "@huggingface/hub": "^0.15.1", "@huggingface/jinja": "^0.3.2", "@lmstudio/immer-with-plugins": "^10.1.1", "@lmstudio/lms-common": "^0.7.0", "@lmstudio/sdk": "^0.3.0", "@modelcontextprotocol/sdk": "^1.11.3", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@react-icons/all-files": "^4.1.0", "@tailwindcss/container-queries": "^0.1.1", "@types/marked": "^4.3.0", "ajv": "^8.12.0", "allotment": "^1.20.3", "autoprefixer": "^10.4.14", "better-sqlite3": "^11.2.1", "bindings": "^1.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.1.3", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "electron-updater": "^5.3.0", "embla-carousel-react": "^8.1.6", "esbuild": "^0.24.0", "express": "^4.18.2", "follow-redirects": "^1.15.5", "framer-motion": "^11.3.28", "fuzzy": "^0.1.3", "html-webpack-plugin": "^5.6.3", "i18next": "^23.11.5", "ignore": "^6.0.2", "inquirer": "^9.2.14", "jsdom": "^22.1.0", "jszip": "^3.10.1", "marked": "^5.0.1", "minimatch": "^9.0.3", "node-abi": "^3.71.0", "node-addon-api": "^6.1.0", "nsfw": "^2.2.5", "partial-json": "^0.1.6", "patch-package": "^8.0.0", "pdf2json": "^3.1.3", "postcss": "^8.4.23", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.1", "react-i18next": "^14.1.2", "react-icons": "^4.12.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.22.3", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "rehype-highlight": "^7.0.0", "rehype-katex": "^7.0.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sharp": "^0.33.5", "showdown": "^2.1.0", "strip-ansi": "^7.1.0", "superjson": "^1.13.3", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7", "tar": "^7.4.3", "tree-kill": "^1.2.2", "update-electron-app": "^2.0.1", "use-effect-event": "^1.0.2", "vaul": "^0.9.1", "yaml": "^2.5.0", "yargs": "^17.7.2", "yarn": "^1.22.19", "zod": "^3.24.1"}, "overrides": {"node-pty": "npm:@homebridge/node-pty-prebuilt-multiarch"}, "resolutions": {"zod": "3.24.1", "**/zod": "3.24.1", "**/**/zod": "3.24.1"}, "workspaces": ["../ryuko/packages/*", "../ryuko/publish/*", "vendor/amphibian-apps/apps/docling"]}