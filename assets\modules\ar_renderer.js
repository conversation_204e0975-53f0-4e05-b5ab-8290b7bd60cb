// وحدة العرض ثلاثي الأبعاد والواقع المعزز
// 3D/AR/VR Rendering Module

class AR3DRenderer {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentModel = null;
        this.isVRMode = false;
        this.isARMode = false;
        this.annotations = [];
    }

    // تهيئة العرض ثلاثي الأبعاد
    initialize() {
        try {
            console.log('🎮 تهيئة العرض ثلاثي الأبعاد...');
            
            // إنشاء المشهد
            this.scene = new THREE.Scene();
            this.scene.background = new THREE.Color(0xf0f0f0);

            // إنشاء الكاميرا
            this.camera = new THREE.PerspectiveCamera(
                75, 
                window.innerWidth / window.innerHeight, 
                0.1, 
                1000
            );
            this.camera.position.set(5, 5, 5);

            // إنشاء المُعرِض
            this.renderer = new THREE.WebGLRenderer({ antialias: true });
            this.renderer.setSize(400, 300);
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // إضافة الإضاءة
            this.setupLighting();

            // إضافة التحكم بالكاميرا
            this.setupControls();

            // إضافة نموذج افتراضي
            this.loadDefaultModel();

            console.log('✅ تم تهيئة العرض ثلاثي الأبعاد بنجاح');
            return true;

        } catch (error) {
            console.error('❌ خطأ في تهيئة العرض ثلاثي الأبعاد:', error);
            return false;
        }
    }

    // إعداد الإضاءة
    setupLighting() {
        // إضاءة محيطية
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // إضاءة اتجاهية
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // إضاءة نقطية
        const pointLight = new THREE.PointLight(0xffffff, 0.5);
        pointLight.position.set(-10, 10, -10);
        this.scene.add(pointLight);
    }

    // إعداد التحكم
    setupControls() {
        // التحكم بالمدار (OrbitControls)
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.25;
            this.controls.enableZoom = true;
        }
    }

    // تحميل نموذج افتراضي
    loadDefaultModel() {
        // إنشاء مكعب ملون كنموذج افتراضي
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const materials = [
            new THREE.MeshLambertMaterial({ color: 0xff0000 }), // أحمر
            new THREE.MeshLambertMaterial({ color: 0x00ff00 }), // أخضر
            new THREE.MeshLambertMaterial({ color: 0x0000ff }), // أزرق
            new THREE.MeshLambertMaterial({ color: 0xffff00 }), // أصفر
            new THREE.MeshLambertMaterial({ color: 0xff00ff }), // بنفسجي
            new THREE.MeshLambertMaterial({ color: 0x00ffff })  // سماوي
        ];

        this.currentModel = new THREE.Mesh(geometry, materials);
        this.currentModel.castShadow = true;
        this.currentModel.receiveShadow = true;
        this.scene.add(this.currentModel);

        // إضافة أرضية
        const floorGeometry = new THREE.PlaneGeometry(20, 20);
        const floorMaterial = new THREE.MeshLambertMaterial({ color: 0xcccccc });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = -2;
        floor.receiveShadow = true;
        this.scene.add(floor);

        // إضافة تسميات توضيحية
        this.addAnnotations();
    }

    // إضافة تسميات توضيحية
    addAnnotations() {
        const annotations = [
            { position: [2, 2, 2], text: 'الوجه الأمامي', color: '#ff0000' },
            { position: [-2, 2, 2], text: 'الوجه الجانبي', color: '#00ff00' },
            { position: [0, 4, 0], text: 'الوجه العلوي', color: '#0000ff' }
        ];

        annotations.forEach(annotation => {
            this.createAnnotation(annotation.position, annotation.text, annotation.color);
        });
    }

    // إنشاء تسمية توضيحية
    createAnnotation(position, text, color) {
        // إنشاء كرة صغيرة كنقطة
        const pointGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const pointMaterial = new THREE.MeshBasicMaterial({ color: color });
        const point = new THREE.Mesh(pointGeometry, pointMaterial);
        point.position.set(...position);
        this.scene.add(point);

        // حفظ معلومات التسمية
        this.annotations.push({
            point: point,
            text: text,
            position: position,
            color: color
        });
    }

    // عرض المشهد ثلاثي الأبعاد
    display() {
        const displayArea = document.getElementById('displayArea');
        const displayContent = document.getElementById('displayContent');
        const displayTitle = document.getElementById('displayTitle');

        // إنشاء حاوية العرض
        const container = document.createElement('div');
        container.className = '3d-viewer-container';
        container.style.position = 'relative';

        // إضافة canvas العرض
        container.appendChild(this.renderer.domElement);

        // إنشاء لوحة التحكم
        const controlPanel = this.createControlPanel();
        container.appendChild(controlPanel);

        // إنشاء لوحة المعلومات
        const infoPanel = this.createInfoPanel();
        container.appendChild(infoPanel);

        // تحديث منطقة العرض
        displayTitle.textContent = 'العرض ثلاثي الأبعاد';
        displayContent.innerHTML = '';
        displayContent.appendChild(container);
        displayArea.style.display = 'flex';

        // بدء حلقة العرض
        this.startRenderLoop();

        addMessageToChat('assistant', 'تم تفعيل العرض ثلاثي الأبعاد! يمكنك التفاعل مع النموذج باستخدام الماوس.');
    }

    // إنشاء لوحة التحكم
    createControlPanel() {
        const panel = document.createElement('div');
        panel.className = 'control-panel';
        panel.style.position = 'absolute';
        panel.style.top = '10px';
        panel.style.left = '10px';
        panel.style.background = 'rgba(255, 255, 255, 0.9)';
        panel.style.padding = '10px';
        panel.style.borderRadius = '8px';
        panel.style.fontSize = '0.8rem';

        const controls = [
            { text: '🔄 دوران تلقائي', action: () => this.toggleAutoRotation() },
            { text: '🎨 تغيير الألوان', action: () => this.changeColors() },
            { text: '💡 تبديل الإضاءة', action: () => this.toggleLighting() },
            { text: '📐 عرض الشبكة', action: () => this.toggleGrid() },
            { text: '🔍 تكبير', action: () => this.zoomIn() },
            { text: '🔍 تصغير', action: () => this.zoomOut() },
            { text: '🏠 إعادة تعيين', action: () => this.resetView() }
        ];

        controls.forEach(control => {
            const button = document.createElement('button');
            button.textContent = control.text;
            button.style.display = 'block';
            button.style.width = '100%';
            button.style.margin = '2px 0';
            button.style.padding = '5px';
            button.style.border = 'none';
            button.style.borderRadius = '4px';
            button.style.background = '#667eea';
            button.style.color = 'white';
            button.style.cursor = 'pointer';
            button.onclick = control.action;
            panel.appendChild(button);
        });

        return panel;
    }

    // إنشاء لوحة المعلومات
    createInfoPanel() {
        const panel = document.createElement('div');
        panel.className = 'info-panel';
        panel.style.position = 'absolute';
        panel.style.bottom = '10px';
        panel.style.right = '10px';
        panel.style.background = 'rgba(0, 0, 0, 0.8)';
        panel.style.color = 'white';
        panel.style.padding = '10px';
        panel.style.borderRadius = '8px';
        panel.style.fontSize = '0.8rem';
        panel.style.maxWidth = '200px';

        panel.innerHTML = `
            <div><strong>التحكم:</strong></div>
            <div>• اسحب للدوران</div>
            <div>• عجلة الماوس للتكبير</div>
            <div>• انقر بالزر الأيمن للتحريك</div>
            <div><strong>النموذج:</strong> مكعب تعليمي</div>
            <div><strong>المضلعات:</strong> 12</div>
            <div><strong>الرؤوس:</strong> 8</div>
        `;

        return panel;
    }

    // بدء حلقة العرض
    startRenderLoop() {
        const animate = () => {
            requestAnimationFrame(animate);

            // تحديث التحكم
            if (this.controls) {
                this.controls.update();
            }

            // دوران تلقائي إذا كان مفعلاً
            if (this.autoRotate && this.currentModel) {
                this.currentModel.rotation.y += 0.01;
            }

            // عرض المشهد
            this.renderer.render(this.scene, this.camera);
        };

        animate();
    }

    // تبديل الدوران التلقائي
    toggleAutoRotation() {
        this.autoRotate = !this.autoRotate;
        addMessageToChat('assistant', `تم ${this.autoRotate ? 'تفعيل' : 'إيقاف'} الدوران التلقائي`);
    }

    // تغيير الألوان
    changeColors() {
        if (this.currentModel && this.currentModel.material) {
            const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff];
            this.currentModel.material.forEach((material, index) => {
                material.color.setHex(colors[Math.floor(Math.random() * colors.length)]);
            });
            addMessageToChat('assistant', 'تم تغيير ألوان النموذج');
        }
    }

    // تبديل الإضاءة
    toggleLighting() {
        this.scene.children.forEach(child => {
            if (child instanceof THREE.Light && child.type !== 'AmbientLight') {
                child.visible = !child.visible;
            }
        });
        addMessageToChat('assistant', 'تم تبديل الإضاءة');
    }

    // عرض/إخفاء الشبكة
    toggleGrid() {
        let grid = this.scene.getObjectByName('grid');
        if (grid) {
            this.scene.remove(grid);
            addMessageToChat('assistant', 'تم إخفاء الشبكة');
        } else {
            grid = new THREE.GridHelper(20, 20);
            grid.name = 'grid';
            grid.position.y = -2;
            this.scene.add(grid);
            addMessageToChat('assistant', 'تم عرض الشبكة');
        }
    }

    // تكبير العرض
    zoomIn() {
        this.camera.position.multiplyScalar(0.9);
        addMessageToChat('assistant', 'تم تكبير العرض');
    }

    // تصغير العرض
    zoomOut() {
        this.camera.position.multiplyScalar(1.1);
        addMessageToChat('assistant', 'تم تصغير العرض');
    }

    // إعادة تعيين العرض
    resetView() {
        this.camera.position.set(5, 5, 5);
        this.camera.lookAt(0, 0, 0);
        if (this.controls) {
            this.controls.reset();
        }
        addMessageToChat('assistant', 'تم إعادة تعيين العرض');
    }

    // تحميل نموذج خارجي
    loadExternalModel(url) {
        // محاكاة تحميل نموذج خارجي
        addMessageToChat('assistant', `جاري تحميل النموذج من: ${url}`);
        
        setTimeout(() => {
            // إنشاء نموذج أكثر تعقيداً
            const group = new THREE.Group();
            
            // إضافة عدة أشكال
            const shapes = [
                { geometry: new THREE.SphereGeometry(1, 16, 16), color: 0xff6b6b, position: [0, 2, 0] },
                { geometry: new THREE.CylinderGeometry(0.5, 0.5, 2, 16), color: 0x4ecdc4, position: [0, 0, 0] },
                { geometry: new THREE.ConeGeometry(0.8, 1.5, 8), color: 0x45b7d1, position: [0, -2, 0] }
            ];

            shapes.forEach(shape => {
                const material = new THREE.MeshLambertMaterial({ color: shape.color });
                const mesh = new THREE.Mesh(shape.geometry, material);
                mesh.position.set(...shape.position);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                group.add(mesh);
            });

            // إزالة النموذج القديم
            if (this.currentModel) {
                this.scene.remove(this.currentModel);
            }

            // إضافة النموذج الجديد
            this.currentModel = group;
            this.scene.add(this.currentModel);
            
            addMessageToChat('assistant', 'تم تحميل النموذج الجديد بنجاح!');
        }, 2000);
    }

    // تصدير المشهد كصورة
    exportAsImage() {
        const canvas = this.renderer.domElement;
        const link = document.createElement('a');
        link.download = `مشهد_ثلاثي_الأبعاد_${new Date().toISOString().split('T')[0]}.png`;
        link.href = canvas.toDataURL();
        link.click();
        
        addMessageToChat('assistant', 'تم تصدير المشهد كصورة');
    }

    // تنظيف الموارد
    dispose() {
        if (this.renderer) {
            this.renderer.dispose();
        }
        if (this.scene) {
            this.scene.clear();
        }
        console.log('🧹 تم تنظيف موارد العرض ثلاثي الأبعاد');
    }
}

// إنشاء مثيل العارض ثلاثي الأبعاد
const ar3dRenderer = new AR3DRenderer();

// تصدير العارض للاستخدام العام
window.ar3dRenderer = ar3dRenderer;
