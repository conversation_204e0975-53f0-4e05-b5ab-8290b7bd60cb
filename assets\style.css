/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

/* الشريط العلوي */
.top-bar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-section i {
    font-size: 2rem;
    color: #667eea;
    animation: pulse 2s infinite;
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: blink 1.5s infinite;
}

.status-dot.online {
    background: #4CAF50;
}

.status-dot.offline {
    background: #ff4757;
}

/* تسجيل صوتي */
.recording {
    background: #ff4757 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* المساعد الصوتي المرئي */
.visual-assistant-container {
    position: relative;
    border: 3px solid #4CAF50;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.3);
}

.live-indicator {
    animation: pulse 2s infinite;
    z-index: 10;
}

.analysis-overlay {
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.analysis-overlay:hover {
    background: rgba(0, 0, 0, 0.9) !important;
}

/* مؤشرات المحادثة الصوتية */
.listening-indicator {
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
}

.listening-indicator.active {
    animation: pulse 1.5s infinite;
    background: rgba(76, 175, 80, 1) !important;
}

/* النص المؤقت */
#interimText {
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* مؤشر التفكير */
#thinkingIndicator {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
}

/* تحسين زر التسجيل */
.recording {
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    animation: pulse 1s infinite, glow 2s infinite alternate;
    box-shadow: 0 0 20px rgba(255, 71, 87, 0.5);
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(255, 71, 87, 0.5); }
    to { box-shadow: 0 0 30px rgba(255, 71, 87, 0.8); }
}

/* مؤشر الكتابة المتقدم */
.advanced-typing {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.thinking-text {
    font-size: 14px;
    color: #666;
    font-style: italic;
    animation: fadeInOut 2s infinite;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* تنسيق رسائل المساعد المحسن */
.message-content h3.message-heading {
    color: #2196F3;
    margin: 10px 0 8px 0;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 2px solid #2196F3;
    padding-bottom: 4px;
}

.message-content .inline-code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #d63384;
    border: 1px solid #e0e0e0;
}

.code-block {
    margin: 15px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.code-header {
    background: #2d3748;
    color: white;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.code-lang {
    font-weight: bold;
    text-transform: uppercase;
}

.copy-code-btn {
    background: #4a5568;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background 0.3s;
}

.copy-code-btn:hover {
    background: #2d3748;
}

.code-block pre {
    background: #1a202c;
    color: #e2e8f0;
    padding: 15px;
    margin: 0;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.emoji-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.emoji-list-item {
    padding: 5px 0;
    border-left: 3px solid #4CAF50;
    padding-left: 10px;
    margin: 5px 0;
    background: rgba(76, 175, 80, 0.05);
    border-radius: 0 4px 4px 0;
}

.message-link {
    color: #2196F3;
    text-decoration: none;
    border-bottom: 1px dotted #2196F3;
    transition: all 0.3s;
}

.message-link:hover {
    color: #1976D2;
    border-bottom: 1px solid #1976D2;
}

/* تحسين عرض الرسائل */
.message.assistant .message-content {
    line-height: 1.6;
}

.message.assistant .message-content p {
    margin: 8px 0;
}

.message.assistant .message-content strong {
    color: #1976D2;
    font-weight: 600;
}

.message.assistant .message-content em {
    color: #666;
    font-style: italic;
}

/* الكتابة التفاعلية المباشرة */
#writingOverlay {
    animation: glow 2s infinite alternate;
    transition: all 0.3s ease;
}

#writingOverlay:hover {
    transform: scale(1.02);
    box-shadow: 0 0 25px rgba(0, 255, 0, 0.5);
}

#interactiveWritingPanel {
    animation: slideUp 0.5s ease-out;
}

#liveTextDisplay {
    transition: all 0.3s ease;
    line-height: 1.4;
}

#liveTextDisplay:hover {
    border-color: #00ff88;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

/* مؤشر الكتابة */
.cursor {
    animation: blink 1s infinite;
    color: #00ff00;
    font-weight: bold;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين الأزرار السريعة */
#interactiveWritingPanel button {
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

#interactiveWritingPanel button:hover {
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    transform: translateY(-2px);
}

#interactiveWritingPanel button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* تحسين منطقة العرض */
#analysisOverlay {
    transition: all 0.3s ease;
}

#analysisOverlay:hover {
    background: rgba(33, 150, 243, 1) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

/* تأثيرات الكتابة المباشرة */
.live-writing-effect {
    background: linear-gradient(90deg, #00ff00, #00ff88, #00ff00);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* تأثيرات العرض ثلاثي الأبعاد */
@keyframes rotate3d {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
}

@keyframes orbit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    100% { box-shadow: 0 0 20px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.8); }
}

/* تحسينات محلل الفيديو */
.video-analyzer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.video-upload-area {
    transition: all 0.3s ease;
    border: 2px dashed rgba(255,255,255,0.5);
}

.video-upload-area:hover {
    border-color: rgba(255,255,255,0.8);
    background: rgba(255,255,255,0.1);
    transform: scale(1.02);
}

/* تحسينات المترجم */
.translator {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.translation-input {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.translation-input:focus {
    border-color: rgba(255,255,255,0.5);
    box-shadow: 0 0 15px rgba(255,255,255,0.3);
    outline: none;
}

/* تحسينات العرض ثلاثي الأبعاد */
.presenter-3d {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.canvas-3d {
    background: linear-gradient(45deg, #1e3c72, #2a5298);
    border: 2px solid rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.canvas-3d:hover {
    border-color: rgba(255,255,255,0.6);
    box-shadow: 0 0 25px rgba(255,255,255,0.2);
}

.topic-button-3d {
    transition: all 0.3s ease;
    cursor: pointer;
}

.topic-button-3d:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 5px 20px rgba(0,0,0,0.3);
}

/* تأثيرات الأزرار المحسنة */
.enhanced-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.enhanced-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.enhanced-button:hover::before {
    left: 100%;
}

.enhanced-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

/* تحسينات النتائج */
.results-container {
    animation: slideInUp 0.5s ease-out;
    border-radius: 10px;
    overflow: hidden;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات التحميل */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

/* تحسينات الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(76, 175, 80, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 10000;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسينات الأيقونات */
.icon-3d {
    display: inline-block;
    transition: all 0.3s ease;
}

.icon-3d:hover {
    transform: rotateY(180deg) scale(1.1);
}

/* تحسينات النصوص */
.gradient-text {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* تحسينات الخلفيات */
.animated-background {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* زر المحادثة الصوتية الخالصة */
.pure-voice-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    position: relative;
    overflow: hidden;
}

.pure-voice-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.pure-voice-btn:hover::before {
    left: 100%;
}

.pure-voice-btn.active {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
    box-shadow: 0 0 20px rgba(17, 153, 142, 0.5);
    animation: pulse 2s infinite;
}

.pure-voice-btn.active::after {
    content: '🔊';
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    animation: bounce 1s infinite;
}

/* تحسينات للوضع الصوتي الخالص */
.pure-voice-mode .chat-container {
    display: none !important;
}

.pure-voice-mode .input-area {
    opacity: 0.5;
    pointer-events: none;
}

.voice-only-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 10000;
}

.voice-only-indicator h2 {
    margin: 0 0 15px 0;
    font-size: 24px;
}

.voice-only-indicator p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.voice-wave {
    display: inline-block;
    width: 60px;
    height: 60px;
    margin: 20px 0;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

/* الحاوية الرئيسية */
.main-container {
    display: flex;
    height: calc(100vh - 80px);
    gap: 20px;
    padding: 20px;
}

/* الشريط الجانبي */
.sidebar {
    width: 250px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.5) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.5);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.7);
}

.tool-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.1rem;
    text-align: center;
}

.tool-btn {
    width: 100%;
    padding: 15px;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.tool-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.tool-btn i {
    font-size: 1.2rem;
}

/* Bug Bounty Mode Button Styling */
.bug-bounty-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: 2px solid #c0392b;
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.bug-bounty-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.bug-bounty-btn:hover::before {
    left: 100%;
}

.bug-bounty-btn:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    border-color: #a93226;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.bug-bounty-btn i {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.bug-bounty-btn span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Bug Bounty Active State */
.bug-bounty-btn.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-color: #27ae60;
    animation: pulse 2s infinite;
}

.bug-bounty-btn.active:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
    border-color: #229954;
}

/* File Creator Button Styling */
.file-creator-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border: 2px solid #8e44ad;
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.file-creator-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.file-creator-btn:hover::before {
    left: 100%;
}

.file-creator-btn:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    border-color: #7d3c98;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
}

.file-creator-btn i {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.file-creator-btn span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* File Creator Active State */
.file-creator-btn.active {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border-color: #f39c12;
    animation: pulse 2s infinite;
}

.file-creator-btn.active:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    border-color: #e67e22;
}

/* Pulse Animation for Active States */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

/* Voice Settings Panel Styles */
.voice-settings-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    color: white;
}

.voice-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 15px 15px 0 0;
}

.voice-settings-header h3 {
    margin: 0;
    font-size: 1.4rem;
    color: white;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.voice-settings-body {
    padding: 25px;
}

.setting-group {
    margin-bottom: 25px;
}

.setting-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 1.1rem;
}

.voice-select {
    width: 100%;
    padding: 12px 15px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
    cursor: pointer;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.checkbox-label:hover {
    background: rgba(255, 255, 255, 0.1);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slider-container input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.3);
    outline: none;
    cursor: pointer;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.slider-value {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.test-voice-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.test-voice-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.save-btn, .reset-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.save-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.reset-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* Voice Settings Button Styling */
.voice-settings-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: 2px solid #f39c12;
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.voice-settings-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.voice-settings-btn:hover::before {
    left: 100%;
}

.voice-settings-btn:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    border-color: #e67e22;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.voice-settings-btn i {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.voice-settings-btn span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* AI Self-Improvement Button Styling */
.ai-improve-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border: 2px solid #9b59b6;
    color: #ffffff !important;
    position: relative;
    overflow: hidden;
    display: flex !important;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 50px;
    font-weight: 600;
}

.ai-improve-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.ai-improve-btn:hover::before {
    left: 100%;
}

.ai-improve-btn:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #732d91 100%);
    border-color: #8e44ad;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
}

.ai-improve-btn.active {
    background: linear-gradient(135deg, #732d91 0%, #5b2c6f 100%);
    border-color: #732d91;
    box-shadow: 0 0 20px rgba(155, 89, 182, 0.6);
    animation: pulse-purple 2s infinite;
}

@keyframes pulse-purple {
    0% {
        box-shadow: 0 0 0 0 rgba(155, 89, 182, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(155, 89, 182, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(155, 89, 182, 0);
    }
}

.ai-improve-btn i {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.ai-improve-btn span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* منطقة المحادثة */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chat-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.welcome-message {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    animation: fadeInUp 0.6s ease;
}

.avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.message-content {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    border-top-right-radius: 5px;
    max-width: 80%;
}

.message-content h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.message-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 8px;
}

/* منطقة الإدخال */
.input-area {
    padding: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.input-container {
    display: flex;
    gap: 10px;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.voice-record-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.voice-record-btn:hover {
    transform: scale(1.1);
}

.voice-record-btn.recording {
    background: #ff4757;
    animation: pulse 1s infinite;
}

/* AI Improve Input Button */
.ai-improve-input-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-right: 10px;
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
    position: relative;
    overflow: hidden;
}

.ai-improve-input-btn:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #732d91 100%);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

.ai-improve-input-btn.active {
    background: linear-gradient(135deg, #732d91 0%, #5b2c6f 100%);
    animation: pulse-purple 2s infinite;
    box-shadow: 0 0 20px rgba(155, 89, 182, 0.6);
}

.ai-improve-input-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.ai-improve-input-btn:hover::before {
    left: 100%;
}

@keyframes pulse-purple {
    0% {
        box-shadow: 0 0 0 0 rgba(155, 89, 182, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(155, 89, 182, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(155, 89, 182, 0);
    }
}

/* API Config Input Button */
.api-config-input-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-right: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.api-config-input-btn:hover {
    background: linear-gradient(135deg, #764ba2 0%, #5a67d8 100%);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.api-config-input-btn.active {
    background: linear-gradient(135deg, #5a67d8 0%, #4c63d2 100%);
    animation: pulse-blue 2s infinite;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
}

.api-config-input-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.api-config-input-btn:hover::before {
    left: 100%;
}

@keyframes pulse-blue {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    padding: 15px 20px;
    font-size: 1rem;
    background: transparent;
}

.send-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.send-btn:hover {
    transform: scale(1.1);
}

/* منطقة السحب والإفلات */
.file-drop-zone {
    margin-top: 15px;
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.5);
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.file-drop-zone i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
}

/* منطقة العرض */
.display-area {
    width: 400px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.display-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-display {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.close-display:hover {
    color: #ff4757;
}

.display-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.setting-group select,
.setting-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

/* الرسائل */
.message {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    animation: fadeInUp 0.4s ease;
}

.message.user {
    flex-direction: row-reverse;
}

.message.user .message-content {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-top-left-radius: 5px;
    border-top-right-radius: 15px;
}

/* الحركات */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* مؤشر الكتابة */
.typing-indicator .typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
        padding: 10px;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .display-area {
        width: 100%;
    }

    .tool-btn {
        padding: 10px;
        font-size: 0.8rem;
    }
}

/* أنماط زر Hugging Face */
.hf-config-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
    border: 2px solid #ff6b6b;
    color: white;
    position: relative;
    overflow: hidden;
}

.hf-config-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.hf-config-btn:hover::before {
    left: 100%;
}

.hf-config-btn:hover {
    background: linear-gradient(135deg, #ffa500 0%, #ff6b6b 100%);
    border-color: #ffa500;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.hf-config-btn i {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.hf-config-btn span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Hugging Face Active State */
.hf-config-btn.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-color: #27ae60;
    animation: pulse-hf 2s infinite;
}

.hf-config-btn.active:hover {
    background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
    border-color: #229954;
}

@keyframes pulse-hf {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
}

/* Hugging Face Input Button */
.hf-config-input-btn {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-right: 10px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.hf-config-input-btn:hover {
    background: linear-gradient(135deg, #ffa500 0%, #ff6b6b 100%);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.hf-config-input-btn.active {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    animation: pulse-hf 2s infinite;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.6);
}

.hf-config-input-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.hf-config-input-btn:hover::before {
    left: 100%;
}
