<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد التقني الذكي الشامل</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/responsivevoice@1.8.1/responsivevoice.js"></script>
</head>
<body>
    <!-- شريط علوي -->
    <header class="top-bar">
        <div class="logo-section">
            <i class="fas fa-robot"></i>
            <h1>المساعد التقني الذكي الشامل</h1>
        </div>
        <div class="status-indicator">
            <span class="status-dot online"></span>
            <span>متصل</span>
        </div>
    </header>

    <!-- الحاوية الرئيسية -->
    <div class="main-container">
        <!-- شريط الأدوات الجانبي -->
        <aside class="sidebar">
            <div class="tool-section">
                <h3>الأدوات</h3>
                <button class="tool-btn" id="screenShareBtn" title="مشاركة الشاشة">
                    <i class="fas fa-desktop"></i>
                    <span>مشاركة الشاشة</span>
                </button>
                <button class="tool-btn" id="videoUploadBtn" title="تحميل فيديو">
                    <i class="fas fa-video"></i>
                    <span>تحميل فيديو</span>
                </button>
                <button class="tool-btn" id="videoAnalyzeBtn" title="تحليل فيديو">
                    <i class="fas fa-chart-line"></i>
                    <span>تحليل فيديو</span>
                </button>
                <button class="tool-btn" id="ar3dBtn" title="عرض ثلاثي الأبعاد">
                    <i class="fas fa-cube"></i>
                    <span>عرض 3D</span>
                </button>
                <button class="tool-btn" id="summaryBtn" title="توليد ملخص">
                    <i class="fas fa-file-alt"></i>
                    <span>ملخص</span>
                </button>
                <button class="tool-btn" id="voiceBtn" title="التفاعل الصوتي">
                    <i class="fas fa-microphone"></i>
                    <span>صوتي</span>
                </button>
                <button class="tool-btn pure-voice-btn" id="pureVoiceBtn" title="محادثة صوتية خالصة مثل ChatGPT">
                    <i class="fas fa-comments"></i>
                    <span>محادثة خالصة</span>
                </button>
                <button class="tool-btn bug-bounty-btn" id="bugBountyBtn" title="Bug Bounty Mode - فحص الثغرات الأمنية المتقدم">
                    <i class="fas fa-shield-alt"></i>
                    <span>Bug Bounty Mode</span>
                </button>
                <button class="tool-btn file-creator-btn" id="fileCreatorBtn" title="File Creator - إنشاء ملفات احترافية">
                    <i class="fas fa-file-plus"></i>
                    <span>File Creator</span>
                </button>
                <button class="tool-btn voice-settings-btn" id="voiceSettingsBtn" title="إعدادات الصوت المتقدمة">
                    <i class="fas fa-microphone-alt"></i>
                    <span>إعدادات الصوت</span>
                </button>
                <button class="tool-btn ai-improve-btn" id="aiImproveBtn" title="التحسين الذاتي بالذكاء الاصطناعي - غير نشط" style="display: flex !important;">
                    <i class="fas fa-robot"></i>
                    <span>التحسين الذاتي</span>
                </button>
                <button class="tool-btn api-config-btn" id="apiConfigBtn" title="تكوين API للنماذج الخارجية">
                    <i class="fas fa-plug"></i>
                    <span>تكوين API</span>
                </button>
                <button class="tool-btn hf-config-btn" id="hfConfigBtn" title="تكوين Hugging Face - نماذج الذكاء الاصطناعي">
                    <i class="fas fa-brain"></i>
                    <span>🤗 Hugging Face</span>
                </button>
            </div>
        </aside>

        <!-- منطقة المحادثة -->
        <main class="chat-area">
            <div class="chat-container" id="chatContainer">
                <div class="welcome-message">
                    <div class="avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <h2>أهلاً وسهلاً بك!</h2>
                        <p>أنا المساعد التقني الذكي، جاهز لمساعدتك في جميع احتياجاتك التقنية</p>
                        <p>يمكنك التحدث معي صوتياً أو كتابياً، وأستطيع تحليل الفيديوهات وعرض المحتوى ثلاثي الأبعاد</p>
                    </div>
                </div>
            </div>

            <!-- منطقة الإدخال -->
            <div class="input-area">
                <div class="input-container">
                    <button class="voice-record-btn" id="voiceRecordBtn" title="اضغط للتحدث">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="ai-improve-input-btn" id="aiImproveInputBtn" title="التحسين الذاتي بالذكاء الاصطناعي - غير نشط">
                        <i class="fas fa-robot"></i>
                    </button>
                    <button class="api-config-input-btn" id="apiConfigInputBtn" title="تكوين API للنماذج الخارجية - غير مفعل">
                        <i class="fas fa-plug"></i>
                    </button>
                    <button class="hf-config-input-btn" id="hfConfigInputBtn" title="تكوين Hugging Face - غير مفعل">
                        <i class="fas fa-brain"></i>
                    </button>
                    <input type="text" id="messageInput" placeholder="اكتب رسالتك هنا..." />
                    <button class="send-btn" id="sendBtn" title="إرسال">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="file-drop-zone" id="fileDropZone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>اسحب وأفلت الملفات هنا أو انقر للتحديد</p>
                    <input type="file" id="fileInput" multiple accept="video/*,audio/*,image/*,.pdf,.txt,.doc,.docx" hidden>
                </div>
            </div>
        </main>

        <!-- منطقة العرض الإضافية -->
        <section class="display-area" id="displayArea" style="display: none;">
            <div class="display-header">
                <h3 id="displayTitle">منطقة العرض</h3>
                <button class="close-display" id="closeDisplay">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="display-content" id="displayContent">
                <!-- محتوى ديناميكي -->
            </div>
        </section>
    </div>

    <!-- نافذة منبثقة للإعدادات -->
    <div class="modal" id="settingsModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإعدادات</h3>
                <button class="close-modal" id="closeSettings">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>اللغة الصوتية:</label>
                    <select id="voiceLanguage">
                        <option value="ar-SA">العربية</option>
                        <option value="ar-IQ">العراقية</option>
                        <option value="en-US">الإنجليزية</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>سرعة الكلام:</label>
                    <input type="range" id="speechRate" min="0.5" max="2" step="0.1" value="1">
                </div>
                <div class="setting-group">
                    <label>مستوى الصوت:</label>
                    <input type="range" id="speechVolume" min="0" max="1" step="0.1" value="0.8">
                </div>
            </div>
        </div>
    </div>

    <!-- Bug Bounty Mode Scripts -->
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>

    <!-- File Creator Scripts -->
    <script src="assets/modules/fileCreator/InternetIntegration.js"></script>
    <script src="assets/modules/fileCreator/FileCreatorCore.js"></script>

    <!-- Advanced Voice Engine -->
    <script src="assets/modules/voice/AdvancedVoiceEngine.js"></script>
    <script src="assets/modules/voice/VoiceSettings.js"></script>

    <!-- AI Self-Improvement System -->
    <script src="assets/modules/ai_self_improve/SuggestionPresenter.js"></script>
    <script src="assets/modules/ai_self_improve/AISelfImprove.js"></script>
    <link rel="stylesheet" href="assets/modules/ai_self_improve/ai_self_improve.css">

    <!-- API Integration System -->
    <script src="assets/modules/api_integration/APIManager.js"></script>
    <script src="assets/modules/api_integration/APIConfigInterface.js"></script>
    <link rel="stylesheet" href="assets/modules/api_integration/api_integration.css">

    <!-- OpenRouter Integration System -->
    <script src="assets/modules/openrouter/OpenRouterManager.js"></script>
    <script src="assets/modules/openrouter/OpenRouterInterface.js"></script>
    <script src="assets/modules/openrouter/OpenRouterIntegration.js"></script>
    <link rel="stylesheet" href="assets/modules/openrouter/openrouter.css">

    <!-- تحميل الملفات الأساسية بالترتيب الصحيح -->
    <script src="assets/assistant-core.js"></script>
    <script src="assets/modules/huggingface_integration/HuggingFaceManager.js"></script>
    <script src="assets/modules/huggingface_integration/HuggingFaceSettings.js"></script>
    <script src="assets/script.js"></script>

    <!-- ملف إدارة أحداث الأزرار - يجب أن يكون بعد assistant-core.js -->
    <script src="assets/event-handlers.js"></script>

    <script>
        // سيتم ربط الأزرار من assistant-core.js
        console.log('🔗 انتظار تحميل assistant-core.js لربط الأزرار...');

        // تهيئة المساعد عند تحميل الصفحة - مبسطة لتجنب التضارب
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تهيئة التطبيق من HTML...');

            // انتظار تحميل assistant-core.js ثم التهيئة
            setTimeout(() => {
                console.log('🔍 فحص الوظائف المطلوبة...');

                const requiredFunctions = [
                    'initializeApp', 'bindEvents', 'sendMessage',
                    'togglePureVoiceMode', 'handleScreenShare'
                ];

                let allFunctionsReady = true;
                requiredFunctions.forEach(funcName => {
                    const isReady = typeof window[funcName] === 'function';
                    console.log(`${funcName}: ${isReady ? '✅ متاح' : '❌ مفقود'}`);
                    if (!isReady) allFunctionsReady = false;
                });

                if (allFunctionsReady) {
                    console.log('✅ جميع الوظائف جاهزة - التهيئة ستتم من assistant-core.js');

                    // تهيئة التطبيق فقط
                    if (window.initializeApp) {
                        initializeApp();
                        console.log('✅ تم تهيئة التطبيق');
                    }
                } else {
                    console.warn('⚠️ بعض الوظائف غير جاهزة - انتظار...');
                }

            }, 1000);

            // فحص نهائي للأزرار بعد 3 ثوان
            setTimeout(() => {
                console.log('🔍 فحص نهائي للأزرار...');

                const criticalButtons = [
                    { id: 'sendBtn', name: 'الإرسال' },
                    { id: 'pureVoiceBtn', name: 'المحادثة الخالصة' },
                    { id: 'screenShareBtn', name: 'مشاركة الشاشة' },
                    { id: 'videoUploadBtn', name: 'تحميل الفيديو' }
                ];

                let workingButtons = 0;
                criticalButtons.forEach(({ id, name }) => {
                    const btn = document.getElementById(id);
                    if (btn && btn.onclick) {
                        workingButtons++;
                        console.log(`✅ ${name}: يعمل`);
                    } else {
                        console.warn(`⚠️ ${name}: لا يعمل`);
                    }
                });

                console.log(`📊 الأزرار العاملة: ${workingButtons}/${criticalButtons.length}`);

                // اختبار مباشر للأزرار
                console.log('🧪 اختبار مباشر للأزرار...');

                // ربط طارئ مباشر
                const sendBtn = document.getElementById('sendBtn');
                if (sendBtn) {
                    sendBtn.onclick = function() {
                        console.log('🔥 DIRECT: تم النقر على زر الإرسال!');
                        alert('زر الإرسال يعمل!');
                        if (typeof window.sendMessage === 'function') {
                            window.sendMessage();
                        }
                    };
                    console.log('🔧 تم ربط زر الإرسال مباشرة');
                }

                const pureVoiceBtn = document.getElementById('pureVoiceBtn');
                if (pureVoiceBtn) {
                    pureVoiceBtn.onclick = function() {
                        console.log('🔥 DIRECT: تم النقر على زر المحادثة الخالصة!');
                        alert('زر المحادثة الخالصة يعمل!');
                        if (typeof window.togglePureVoiceMode === 'function') {
                            window.togglePureVoiceMode();
                        }
                    };
                    console.log('🔧 تم ربط زر المحادثة الخالصة مباشرة');
                }

                const screenShareBtn = document.getElementById('screenShareBtn');
                if (screenShareBtn) {
                    screenShareBtn.onclick = function() {
                        console.log('🔥 DIRECT: تم النقر على زر مشاركة الشاشة!');
                        alert('زر مشاركة الشاشة يعمل!');
                        if (typeof window.handleScreenShare === 'function') {
                            window.handleScreenShare();
                        }
                    };
                    console.log('🔧 تم ربط زر مشاركة الشاشة مباشرة');
                }

                // اختبار شامل للنقر
                setTimeout(() => {
                    console.log('🎯 اختبار النقر التلقائي...');

                    // محاكاة النقر على زر الإرسال
                    const testSendBtn = document.getElementById('sendBtn');
                    if (testSendBtn) {
                        console.log('🤖 محاكاة النقر على زر الإرسال...');
                        testSendBtn.click();
                    }

                }, 2000);

            }, 3000);




        });
    </script>
</body>
</html>
