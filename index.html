<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد التقني الذكي الشامل</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/responsivevoice@1.8.1/responsivevoice.js"></script>
</head>
<body>
    <!-- شريط علوي -->
    <header class="top-bar">
        <div class="logo-section">
            <i class="fas fa-robot"></i>
            <h1>المساعد التقني الذكي الشامل</h1>
        </div>
        <div class="status-indicator">
            <span class="status-dot online"></span>
            <span>متصل</span>
        </div>
    </header>

    <!-- الحاوية الرئيسية -->
    <div class="main-container">
        <!-- شريط الأدوات الجانبي -->
        <aside class="sidebar">
            <div class="tool-section">
                <h3>الأدوات</h3>
                <button class="tool-btn" id="screenShareBtn" title="مشاركة الشاشة">
                    <i class="fas fa-desktop"></i>
                    <span>مشاركة الشاشة</span>
                </button>
                <button class="tool-btn" id="videoUploadBtn" title="تحميل فيديو">
                    <i class="fas fa-video"></i>
                    <span>تحميل فيديو</span>
                </button>
                <button class="tool-btn" id="videoAnalyzeBtn" title="تحليل فيديو">
                    <i class="fas fa-chart-line"></i>
                    <span>تحليل فيديو</span>
                </button>
                <button class="tool-btn" id="ar3dBtn" title="عرض ثلاثي الأبعاد">
                    <i class="fas fa-cube"></i>
                    <span>عرض 3D</span>
                </button>
                <button class="tool-btn" id="summaryBtn" title="توليد ملخص">
                    <i class="fas fa-file-alt"></i>
                    <span>ملخص</span>
                </button>
                <button class="tool-btn" id="voiceBtn" title="التفاعل الصوتي">
                    <i class="fas fa-microphone"></i>
                    <span>صوتي</span>
                </button>
                <button class="tool-btn pure-voice-btn" id="pureVoiceBtn" title="محادثة صوتية خالصة مثل ChatGPT">
                    <i class="fas fa-comments"></i>
                    <span>محادثة خالصة</span>
                </button>
                <button class="tool-btn bug-bounty-btn" id="bugBountyBtn" title="Bug Bounty Mode - فحص الثغرات الأمنية المتقدم">
                    <i class="fas fa-shield-alt"></i>
                    <span>Bug Bounty Mode</span>
                </button>
                <button class="tool-btn file-creator-btn" id="fileCreatorBtn" title="File Creator - إنشاء ملفات احترافية">
                    <i class="fas fa-file-plus"></i>
                    <span>File Creator</span>
                </button>
                <button class="tool-btn voice-settings-btn" id="voiceSettingsBtn" title="إعدادات الصوت المتقدمة">
                    <i class="fas fa-microphone-alt"></i>
                    <span>إعدادات الصوت</span>
                </button>
                <button class="tool-btn ai-improve-btn" id="aiImproveBtn" title="التحسين الذاتي بالذكاء الاصطناعي - غير نشط" style="display: flex !important;">
                    <i class="fas fa-robot"></i>
                    <span>التحسين الذاتي</span>
                </button>
                <button class="tool-btn api-config-btn" id="apiConfigBtn" title="تكوين API للنماذج الخارجية">
                    <i class="fas fa-plug"></i>
                    <span>تكوين API</span>
                </button>
                <button class="tool-btn hf-config-btn" id="hfConfigBtn" title="تكوين Hugging Face - نماذج الذكاء الاصطناعي">
                    <i class="fas fa-brain"></i>
                    <span>🤗 Hugging Face</span>
                </button>
            </div>
        </aside>

        <!-- منطقة المحادثة -->
        <main class="chat-area">
            <div class="chat-container" id="chatContainer">
                <div class="welcome-message">
                    <div class="avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <h2>أهلاً وسهلاً بك!</h2>
                        <p>أنا المساعد التقني الذكي، جاهز لمساعدتك في جميع احتياجاتك التقنية</p>
                        <p>يمكنك التحدث معي صوتياً أو كتابياً، وأستطيع تحليل الفيديوهات وعرض المحتوى ثلاثي الأبعاد</p>
                    </div>
                </div>
            </div>

            <!-- منطقة الإدخال -->
            <div class="input-area">
                <div class="input-container">
                    <button class="voice-record-btn" id="voiceRecordBtn" title="اضغط للتحدث">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button class="ai-improve-input-btn" id="aiImproveInputBtn" title="التحسين الذاتي بالذكاء الاصطناعي - غير نشط">
                        <i class="fas fa-robot"></i>
                    </button>
                    <button class="api-config-input-btn" id="apiConfigInputBtn" title="تكوين API للنماذج الخارجية - غير مفعل">
                        <i class="fas fa-plug"></i>
                    </button>
                    <button class="hf-config-input-btn" id="hfConfigInputBtn" title="تكوين Hugging Face - غير مفعل">
                        <i class="fas fa-brain"></i>
                    </button>
                    <input type="text" id="messageInput" placeholder="اكتب رسالتك هنا..." />
                    <button class="send-btn" id="sendBtn" title="إرسال">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="file-drop-zone" id="fileDropZone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>اسحب وأفلت الملفات هنا أو انقر للتحديد</p>
                    <input type="file" id="fileInput" multiple accept="video/*,audio/*,image/*,.pdf,.txt,.doc,.docx" hidden>
                </div>
            </div>
        </main>

        <!-- منطقة العرض الإضافية -->
        <section class="display-area" id="displayArea" style="display: none;">
            <div class="display-header">
                <h3 id="displayTitle">منطقة العرض</h3>
                <button class="close-display" id="closeDisplay">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="display-content" id="displayContent">
                <!-- محتوى ديناميكي -->
            </div>
        </section>
    </div>

    <!-- نافذة منبثقة للإعدادات -->
    <div class="modal" id="settingsModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإعدادات</h3>
                <button class="close-modal" id="closeSettings">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>اللغة الصوتية:</label>
                    <select id="voiceLanguage">
                        <option value="ar-SA">العربية</option>
                        <option value="ar-IQ">العراقية</option>
                        <option value="en-US">الإنجليزية</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>سرعة الكلام:</label>
                    <input type="range" id="speechRate" min="0.5" max="2" step="0.1" value="1">
                </div>
                <div class="setting-group">
                    <label>مستوى الصوت:</label>
                    <input type="range" id="speechVolume" min="0" max="1" step="0.1" value="0.8">
                </div>
            </div>
        </div>
    </div>

    <!-- Bug Bounty Mode Scripts -->
    <script src="assets/modules/bugBounty/BugBountyCore.js"></script>

    <!-- File Creator Scripts -->
    <script src="assets/modules/fileCreator/InternetIntegration.js"></script>
    <script src="assets/modules/fileCreator/FileCreatorCore.js"></script>

    <!-- Screen Share Scripts -->
    <script src="assets/modules/screenShare.js"></script>

    <!-- Advanced Voice Engine -->
    <script src="assets/modules/voice/AdvancedVoiceEngine.js"></script>
    <script src="assets/modules/voice/VoiceSettings.js"></script>

    <!-- AI Self-Improvement System -->
    <script src="assets/modules/ai_self_improve/SuggestionPresenter.js"></script>
    <script src="assets/modules/ai_self_improve/AISelfImprove.js"></script>
    <link rel="stylesheet" href="assets/modules/ai_self_improve/ai_self_improve.css">

    <!-- API Integration System -->
    <script src="assets/modules/api_integration/APIManager.js"></script>
    <script src="assets/modules/api_integration/APIConfigInterface.js"></script>
    <link rel="stylesheet" href="assets/modules/api_integration/api_integration.css">

    <!-- OpenRouter Integration System -->
    <script src="assets/modules/openrouter/OpenRouterManager.js"></script>
    <script src="assets/modules/openrouter/OpenRouterInterface.js"></script>
    <script src="assets/modules/openrouter/OpenRouterIntegration.js"></script>
    <link rel="stylesheet" href="assets/modules/openrouter/openrouter.css">

    <!-- تحميل الملفات الأساسية بالترتيب الصحيح -->
    <script src="assets/assistant-core.js"></script>
    <script src="assets/modules/huggingface_integration/HuggingFaceManager.js"></script>
    <script src="assets/modules/huggingface_integration/HuggingFaceSettings.js"></script>
    <script src="assets/script.js"></script>

    <!-- ملف إدارة أحداث الأزرار - يجب أن يكون بعد assistant-core.js -->
    <script src="assets/event-handlers.js"></script>

    <script>
        // ربط الأزرار بوظائف event-handlers.js
        console.log('🔗 ربط الأزرار بوظائف event-handlers.js...');

        // متغيرات حفظ حالة الأزرار
        let bugBountyActive = false;
        let fileCreatorActive = false;

        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تهيئة التطبيق...');

            // فحص فوري للملفات المحملة
            console.log('📋 فحص فوري للملفات المحملة:');
            console.log('  - BugBountyCore:', typeof window.BugBountyCore);
            console.log('  - FileCreatorCore:', typeof window.FileCreatorCore);
            console.log('  - ScreenShareManager:', typeof window.ScreenShareManager);
            console.log('  - executeFunction:', typeof window.executeFunction);
            console.log('  - toggleBugBountyMode:', typeof window.toggleBugBountyMode);
            console.log('  - toggleFileCreatorMode:', typeof window.toggleFileCreatorMode);
            console.log('  - handleScreenShare:', typeof window.handleScreenShare);

            // انتظار تحميل event-handlers.js ثم ربط الأزرار
            setTimeout(() => {
                console.log('🔗 ربط جميع الأزرار...');

                // تشخيص شامل للملفات المحملة
                console.log('📋 تشخيص شامل للملفات المحملة:');
                console.log('🔧 الملفات الأساسية:');
                console.log('  - BugBountyCore.js محمل:', typeof window.BugBountyCore !== 'undefined');
                console.log('  - FileCreatorCore.js محمل:', typeof window.FileCreatorCore !== 'undefined');
                console.log('  - screenShare.js محمل:', typeof window.ScreenShareManager !== 'undefined');
                console.log('  - event-handlers.js محمل:', typeof window.toggleBugBountyMode !== 'undefined');

                console.log('🔧 المثيلات التلقائ<|im_start|>:');
                console.log('  - window.bugBountyInstance:', typeof window.bugBountyInstance);
                console.log('  - window.fileCreatorInstance:', typeof window.fileCreatorInstance);
                console.log('  - window.screenShareInstance:', typeof window.screenShareInstance);

                console.log('🔧 الوظائف المطلوبة:');
                console.log('  - toggleBugBountyMode:', typeof window.toggleBugBountyMode);
                console.log('  - toggleFileCreatorMode:', typeof window.toggleFileCreatorMode);
                console.log('  - handleScreenShare:', typeof window.handleScreenShare);
                console.log('  - executeFunction:', typeof window.executeFunction);

                // فحص شامل للوظائف المطلوبة
                const requiredFunctions = [
                    'executeFunction', 'sendMessage', 'togglePureVoiceMode',
                    'handleScreenShare', 'toggleBugBountyMode', 'toggleFileCreatorMode'
                ];

                // فحص المثيلات التلقائ<|im_start|>
                console.log('🔍 فحص المثيلات التلقائ<|im_start|>:');
                console.log('  🛡️ window.bugBountyInstance:', typeof window.bugBountyInstance);
                console.log('  📁 window.fileCreatorInstance:', typeof window.fileCreatorInstance);
                console.log('  🛡️ window.BugBountyCore:', typeof window.BugBountyCore);
                console.log('  📁 window.FileCreatorCore:', typeof window.FileCreatorCore);
                console.log('  🔧 window.toggleBugBountyMode:', typeof window.toggleBugBountyMode);
                console.log('  🔧 window.toggleFileCreatorMode:', typeof window.toggleFileCreatorMode);

                let missingFunctions = [];
                requiredFunctions.forEach(funcName => {
                    if (typeof window[funcName] !== 'function') {
                        missingFunctions.push(funcName);
                        console.error(`❌ ${funcName} غير متاحة`);
                    } else {
                        console.log(`✅ ${funcName} متاحة`);
                    }
                });

                if (missingFunctions.length > 0) {
                    console.error('❌ وظائف مفقودة:', missingFunctions);
                    alert(`❌ خطأ: وظائف مفقودة: ${missingFunctions.join(', ')}`);
                    return;
                } else {
                    console.log('✅ جميع الوظائف المطلوبة متاحة');
                }

                // اختبار مباشر للأزرار
                console.log('🧪 اختبار مباشر للأزرار...');

                // اختبار executeFunction مباشرة
                try {
                    console.log('🧪 اختبار executeFunction مباشرة...');
                    // لا نستدعي الوظيفة فعلياً، فقط نتحقق من وجودها
                    if (typeof executeFunction === 'function') {
                        console.log('✅ executeFunction قابلة للاستدعاء');
                    } else {
                        console.error('❌ executeFunction غير قابلة للاستدعاء');
                    }
                } catch (error) {
                    console.error('❌ خطأ في اختبار executeFunction:', error);
                }

                // اختبار مباشر للوظائف المطلوبة
                console.log('🧪 اختبار مباشر للوظائف:');
                try {
                    if (typeof window.toggleBugBountyMode === 'function') {
                        console.log('✅ toggleBugBountyMode جاهزة للاستدعاء');
                    } else {
                        console.error('❌ toggleBugBountyMode غير متاحة');
                    }

                    if (typeof window.toggleFileCreatorMode === 'function') {
                        console.log('✅ toggleFileCreatorMode جاهزة للاستدعاء');
                    } else {
                        console.error('❌ toggleFileCreatorMode غير متاحة');
                    }

                    if (typeof window.handleScreenShare === 'function') {
                        console.log('✅ handleScreenShare جاهزة للاستدعاء');
                    } else {
                        console.error('❌ handleScreenShare غير متاحة');
                    }
                } catch (error) {
                    console.error('❌ خطأ في اختبار الوظائف:', error);
                }

                // ربط جميع الأزرار بوظائف event-handlers.js
                console.log('🔗 ربط الأزرار بوظائف event-handlers.js...');

                // زر الإرسال
                const sendBtn = document.getElementById('sendBtn');
                if (sendBtn) {
                    sendBtn.onclick = () => {
                        console.log('🔥 تم النقر على زر الإرسال');
                        executeFunction('sendMessage', 'إرسال الرسالة');
                    };
                    console.log('✅ تم ربط زر الإرسال');
                } else {
                    console.error('❌ زر الإرسال غير موجود');
                }

                // زر المحادثة الصوتية الخالصة
                const pureVoiceBtn = document.getElementById('pureVoiceBtn');
                if (pureVoiceBtn) {
                    pureVoiceBtn.onclick = () => {
                        console.log('🔥 تم النقر على زر المحادثة الخالصة');
                        executeFunction('togglePureVoiceMode', 'المحادثة الصوتية الخالصة');
                    };
                    console.log('✅ تم ربط زر المحادثة الخالصة');
                } else {
                    console.error('❌ زر المحادثة الخالصة غير موجود');
                }

                // زر مشاركة الشاشة
                const screenShareBtn = document.getElementById('screenShareBtn');
                if (screenShareBtn) {
                    screenShareBtn.onclick = () => {
                        console.log('🔥 تم النقر على زر مشاركة الشاشة');
                        console.log('🔍 تشخيص Screen Share (للمقارنة):');
                        console.log('  - window.ScreenShareManager:', typeof window.ScreenShareManager);
                        console.log('  - window.screenShareInstance:', typeof window.screenShareInstance);
                        console.log('  - window.handleScreenShare:', typeof window.handleScreenShare);
                        console.log('  - executeFunction:', typeof executeFunction);

                        // استدعاء مباشر بدلاً من executeFunction
                        if (typeof window.handleScreenShare === 'function') {
                            console.log('✅ استدعاء handleScreenShare مباشرة');
                            window.handleScreenShare();
                        } else {
                            console.error('❌ handleScreenShare غير متاحة، استخدام executeFunction');
                            executeFunction('handleScreenShare', 'مشاركة الشاشة');
                        }
                    };
                    console.log('✅ تم ربط زر مشاركة الشاشة');
                } else {
                    console.error('❌ زر مشاركة الشاشة غير موجود');
                }

                // زر تحميل الفيديو
                const videoUploadBtn = document.getElementById('videoUploadBtn');
                if (videoUploadBtn) {
                    videoUploadBtn.onclick = () => {
                        console.log('🔥 تم النقر على زر تحميل الفيديو');
                        executeFunction('handleVideoUpload', 'تحميل الفيديو');
                    };
                    console.log('✅ تم ربط زر تحميل الفيديو');
                } else {
                    console.error('❌ زر تحميل الفيديو غير موجود');
                }

                // زر تحليل الفيديو
                const videoAnalyzeBtn = document.getElementById('videoAnalyzeBtn');
                if (videoAnalyzeBtn) {
                    videoAnalyzeBtn.onclick = () => executeFunction('handleVideoAnalyze', 'تحليل الفيديو');
                    console.log('✅ تم ربط زر تحليل الفيديو');
                }

                // زر العرض ثلاثي الأبعاد
                const ar3dBtn = document.getElementById('ar3dBtn');
                if (ar3dBtn) {
                    ar3dBtn.onclick = () => executeFunction('handle3DDisplay', 'العرض ثلاثي الأبعاد');
                    console.log('✅ تم ربط زر العرض ثلاثي الأبعاد');
                }

                // زر الملخص
                const summaryBtn = document.getElementById('summaryBtn');
                if (summaryBtn) {
                    summaryBtn.onclick = () => executeFunction('generateSummary', 'توليد الملخص');
                    console.log('✅ تم ربط زر الملخص');
                }

                // زر الصوت العادي
                const voiceBtn = document.getElementById('voiceBtn');
                if (voiceBtn) {
                    voiceBtn.onclick = () => executeFunction('toggleVoiceConversation', 'المحادثة الصوتية');
                    console.log('✅ تم ربط زر الصوت العادي');
                }

                // زر Bug Bounty
                const bugBountyBtn = document.getElementById('bugBountyBtn');
                if (bugBountyBtn) {
                    bugBountyBtn.onclick = () => {
                        try {
                            console.log('🛡️ حالة Bug Bounty الحالية:', bugBountyActive);

                            // فحص حالة التفعيل باستخدام المتغير
                            if (bugBountyActive) {
                                // إلغاء التفعيل
                                console.log('🔴 إلغاء تفعيل Bug Bounty Mode...');

                                if (window.bugBountyInstance && typeof window.bugBountyInstance.deactivate === 'function') {
                                    window.bugBountyInstance.deactivate();
                                } else if (window.bugBountyInstance) {
                                    window.bugBountyInstance.isActive = false;
                                }

                                bugBountyActive = false; // تحديث المتغير
                                alert('❌ تم إلغاء تفعيل Bug Bounty Mode');

                                // تحديث نص الزر وإزالة الحالة النشطة
                                bugBountyBtn.innerHTML = '<i class="fas fa-shield-alt"></i><span>Bug Bounty Mode</span>';
                                bugBountyBtn.classList.remove('active');
                                bugBountyBtn.style.background = '';

                                // إضافة رسالة للمحادثة
                                if (typeof addMessageToChat === 'function') {
                                    addMessageToChat('system', '❌ تم إلغاء تفعيل Bug Bounty Mode');
                                }

                            } else {
                                // التفعيل
                                console.log('🟢 تفعيل Bug Bounty Mode...');

                                // إنشاء المثيل إذا لم يوجد
                                if (!window.bugBountyInstance && window.BugBountyCore) {
                                    console.log('🔧 إنشاء مثيل Bug Bounty جديد...');
                                    window.bugBountyInstance = new window.BugBountyCore();
                                }

                                if (window.bugBountyInstance && typeof window.bugBountyInstance.activate === 'function') {
                                    window.bugBountyInstance.activate();
                                } else if (window.bugBountyInstance) {
                                    window.bugBountyInstance.isActive = true;
                                }

                                bugBountyActive = true; // تحديث المتغير
                                alert('✅ تم تفعيل Bug Bounty Mode');

                                // تحديث نص الزر وإضافة الحالة النشطة
                                bugBountyBtn.innerHTML = '<i class="fas fa-shield-check"></i><span>إيقاف Bug Bounty</span>';
                                bugBountyBtn.classList.add('active');
                                bugBountyBtn.style.background = '#27ae60';

                                // إضافة رسالة للمحادثة
                                if (typeof addMessageToChat === 'function') {
                                    addMessageToChat('system', '✅ تم تفعيل Bug Bounty Mode - جاهز لفحص الثغرات الأمنية');
                                }
                            }

                        } catch (error) {
                            alert('❌ خطأ في التنفيذ: ' + error.message);
                        }
                    };
                    console.log('✅ تم ربط زر Bug Bounty');
                } else {
                    console.error('❌ زر Bug Bounty غير موجود في HTML');
                }

                // زر File Creator
                const fileCreatorBtn = document.getElementById('fileCreatorBtn');
                if (fileCreatorBtn) {
                    fileCreatorBtn.onclick = () => {
                        try {
                            console.log('📁 حالة File Creator الحالية:', fileCreatorActive);

                            // فحص حالة التفعيل باستخدام المتغير
                            if (fileCreatorActive) {
                                // إلغاء التفعيل
                                console.log('🔴 إلغاء تفعيل File Creator Mode...');

                                if (window.fileCreatorInstance && typeof window.fileCreatorInstance.deactivate === 'function') {
                                    window.fileCreatorInstance.deactivate();
                                } else if (window.fileCreatorInstance) {
                                    window.fileCreatorInstance.isActive = false;
                                }

                                fileCreatorActive = false; // تحديث المتغير
                                alert('❌ تم إلغاء تفعيل File Creator Mode');

                                // تحديث نص الزر وإزالة الحالة النشطة
                                fileCreatorBtn.innerHTML = '<i class="fas fa-file-plus"></i><span>File Creator</span>';
                                fileCreatorBtn.classList.remove('active');
                                fileCreatorBtn.style.background = '';

                                // إضافة رسالة للمحادثة
                                if (typeof addMessageToChat === 'function') {
                                    addMessageToChat('system', '❌ تم إلغاء تفعيل File Creator Mode');
                                }

                            } else {
                                // التفعيل
                                console.log('🟢 تفعيل File Creator Mode...');

                                // إنشاء المثيل إذا لم يوجد
                                if (!window.fileCreatorInstance && window.FileCreatorCore) {
                                    console.log('🔧 إنشاء مثيل File Creator جديد...');
                                    window.fileCreatorInstance = new window.FileCreatorCore();
                                }

                                if (window.fileCreatorInstance && typeof window.fileCreatorInstance.activate === 'function') {
                                    window.fileCreatorInstance.activate();
                                } else if (window.fileCreatorInstance) {
                                    window.fileCreatorInstance.isActive = true;
                                }

                                fileCreatorActive = true; // تحديث المتغير
                                alert('✅ تم تفعيل File Creator Mode');

                                // تحديث نص الزر وإضافة الحالة النشطة
                                fileCreatorBtn.innerHTML = '<i class="fas fa-file-check"></i><span>إيقاف File Creator</span>';
                                fileCreatorBtn.classList.add('active');
                                fileCreatorBtn.style.background = '#3498db';

                                // إضافة رسالة للمحادثة
                                if (typeof addMessageToChat === 'function') {
                                    addMessageToChat('system', '✅ تم تفعيل File Creator Mode - جاهز لإنشاء الملفات الاحترافية');
                                }
                            }
                        } catch (error) {
                            alert('❌ خطأ في التنفيذ: ' + error.message);
                        }
                    };
                    console.log('✅ تم ربط زر File Creator');
                }

                // زر إعدادات الصوت
                const voiceSettingsBtn = document.getElementById('voiceSettingsBtn');
                if (voiceSettingsBtn) {
                    voiceSettingsBtn.onclick = () => executeFunction('openVoiceSettings', 'إعدادات الصوت');
                    console.log('✅ تم ربط زر إعدادات الصوت');
                }

                // زر التحسين الذاتي
                const aiImproveBtn = document.getElementById('aiImproveBtn');
                if (aiImproveBtn) {
                    aiImproveBtn.onclick = () => executeFunction('toggleAIImprove', 'التحسين الذاتي');
                    console.log('✅ تم ربط زر التحسين الذاتي');
                }

                // زر تكوين API
                const apiConfigBtn = document.getElementById('apiConfigBtn');
                if (apiConfigBtn) {
                    apiConfigBtn.onclick = () => executeFunction('openAPIConfig', 'تكوين API');
                    console.log('✅ تم ربط زر تكوين API');
                }

                // زر Hugging Face
                const hfConfigBtn = document.getElementById('hfConfigBtn');
                if (hfConfigBtn) {
                    hfConfigBtn.onclick = () => executeFunction('openHFConfig', 'تكوين Hugging Face');
                    console.log('✅ تم ربط زر Hugging Face');
                }

                // أزرار منطقة الإدخال
                const voiceRecordBtn = document.getElementById('voiceRecordBtn');
                if (voiceRecordBtn) {
                    voiceRecordBtn.onclick = () => executeFunction('startVoiceRecording', 'التسجيل الصوتي');
                    console.log('✅ تم ربط زر التسجيل الصوتي');
                }

                const aiImproveInputBtn = document.getElementById('aiImproveInputBtn');
                if (aiImproveInputBtn) {
                    aiImproveInputBtn.onclick = () => executeFunction('toggleAIImprove', 'التحسين الذاتي');
                    console.log('✅ تم ربط زر التحسين الذاتي (الإدخال)');
                }

                const apiConfigInputBtn = document.getElementById('apiConfigInputBtn');
                if (apiConfigInputBtn) {
                    apiConfigInputBtn.onclick = () => executeFunction('openAPIConfig', 'تكوين API');
                    console.log('✅ تم ربط زر تكوين API (الإدخال)');
                }

                const hfConfigInputBtn = document.getElementById('hfConfigInputBtn');
                if (hfConfigInputBtn) {
                    hfConfigInputBtn.onclick = () => executeFunction('openHFConfig', 'تكوين Hugging Face');
                    console.log('✅ تم ربط زر Hugging Face (الإدخال)');
                }

                // اختبار الأزرار المربوطة
                setTimeout(() => {
                    console.log('🎯 اختبار الأزرار المربوطة...');

                    const allButtons = [
                        'sendBtn', 'pureVoiceBtn', 'screenShareBtn', 'videoUploadBtn',
                        'videoAnalyzeBtn', 'ar3dBtn', 'summaryBtn', 'voiceBtn',
                        'bugBountyBtn', 'fileCreatorBtn', 'voiceSettingsBtn',
                        'aiImproveBtn', 'apiConfigBtn', 'hfConfigBtn', 'voiceRecordBtn'
                    ];

                    // فحص خاص للأزرار المشكوك فيها
                    console.log('🔍 فحص خاص للأزرار المشكوك فيها:');
                    const bugBtn = document.getElementById('bugBountyBtn');
                    const fileBtn = document.getElementById('fileCreatorBtn');
                    const screenBtn = document.getElementById('screenShareBtn');

                    console.log('🛡️ bugBountyBtn موجود:', !!bugBtn);
                    console.log('📁 fileCreatorBtn موجود:', !!fileBtn);
                    console.log('🖥️ screenShareBtn موجود:', !!screenBtn);

                    if (bugBtn) console.log('🛡️ bugBountyBtn.onclick:', typeof bugBtn.onclick);
                    if (fileBtn) console.log('📁 fileCreatorBtn.onclick:', typeof fileBtn.onclick);
                    if (screenBtn) console.log('🖥️ screenShareBtn.onclick:', typeof screenBtn.onclick);

                    let connectedButtons = 0;
                    allButtons.forEach(btnId => {
                        const btn = document.getElementById(btnId);
                        if (btn && btn.onclick) {
                            connectedButtons++;
                            console.log(`✅ ${btnId}: مربوط`);
                        } else {
                            console.warn(`❌ ${btnId}: غير مربوط`);
                        }
                    });

                    console.log(`📊 إجمالي الأزرار المربوطة: ${connectedButtons}/${allButtons.length}`);

                    if (connectedButtons === allButtons.length) {
                        console.log('🎉 جميع الأزرار مربوطة بنجاح!');
                    } else {
                        console.warn('⚠️ بعض الأزرار غير مربوطة');
                    }

                    // تسجيل حالة الأزرار الأساسية
                    console.log('✅ جميع الأزرار تم ربطها بنجاح');

                }, 1000);

                // إضافة دعم Enter للإرسال
                const messageInput = document.getElementById('messageInput');
                if (messageInput) {
                    messageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            executeFunction('sendMessage', 'إرسال الرسالة');
                        }
                    });
                    console.log('✅ تم ربط مفتاح Enter للإرسال');
                }

            }, 3000);

        });
    </script>
</body>
</html>
