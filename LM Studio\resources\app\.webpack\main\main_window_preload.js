function a1_0x409c(){const _0x50502e=['defineProperty','open-external','The\x20preload\x20script\x20has\x20loaded.\x20The\x20current\x20Node.js\x20version\x20is:\x20','chats:deleteConversation','error','exports','removeListener','chats:createConversation','downloads','embedding:progress','invoke','get-app-version','longRunningTask','config:updateModelBaseInferenceParams','frameworkIndex','developmentPluginRunning','app:checkForUpdates','retrieval:getRetrievalSessionMetadata','download:resumeDownload','7erkuJU','retrieval:uploadDoc','136HBcefl','log_dev','249080iEwbPR','pluginDownload','app:scheduleUpdateDownloadReminder','embedding:getLoadedEmbeddingModel','1830926jYPnmC','win32','transientStorage','lmsHostedEnv','Unknown\x20API\x20ID:\x20','webUtils','retrieval:clearDocs','stream\x20ended','modelData','inferencing:setThrottlePower','config:isConfigMigrationNeeded','inferencing:setBackendSettings','notepadMinusMinus','addEventListener','hardwareConfig','internalPlugins','4050615rMHLyC','inferencing:startModelServer','preventDefault','filesystem:listRetrievalSessionsInPath','blue','Going\x20to\x20registerCallbackServerLogEvents','filesystem:openDownloadsFolder','embedding:unloadEmbeddingModel','retrieval:loadRetrievalSession','filesystem:openConfigPresetsFolder','window:maximize','conversations','cloudBackend','retrieval:uploadProgress','log_dev_error','getPathForFile','target','hostedApps:hideApp','config:getConfigPresets','[RC][','close','hostname','/native_modules/','presetsEditor','hardware:get-current-gpu-backend-name','__esModule','config:updateConfigPresetMap','search:searchModels','versions','Opening\x20external\x20link\x20','exposeHostedEnvInPreload','45633bqmpUR','window:minimize','app:downloadUpdate','filesystem:getRetrievalSessionsFolder','postMessage',']\x20SEND:','chatAppearance:textToMarkdown','openExternalLink','config:getConfigPresetsMap','config:setModelStructuredOutputConfiguration','inferencing:stopModelServer','filesystem:getAvailableModels','api:','includes','files','embedding','electron','node','inferencing:setServerModelConfiguration','inferencing:tokenize','diagnostics','retrieval:clearDocById','948852BQptIr','download:migration_addEndedDownloads','hostedApps:placeApp','random','inferencing:unloadModel','Invalid\x20or\x20potentially\x20unsafe\x20URL:\x20','chats:updateMetadata','download:cancelDownload','chats:saveConversation','handleExternalLinks','href','loadModel:progress','ipcRenderer','deepLinkHandling','filesystem:showImportImagesDialog','1kjkolD','inferencing:stopGenerating','click','popOutWindow','::1','virtualModel','stringify','assets:registerForModelCatalogUpdates','llm','userModelDefaultConfig','log','search:listModelFiles','Going\x20to\x20registerCallbackServerStateUpdates','embedding:embedString','userFiles','download:downloadFile','download:removeEndedDownload','filesystem:importModelConfiguration','filesystem:revealPathInFileManager','log_dev_color','data','debug','platform','search','download:listActiveDownloads','search:downloadModelFileGGUFPreviewFromNetwork','inferencing:loadModel','assets:getCachedModelCatalog','filesystem:triggerDownloadsFolderChangeDialog','retrieval:getCurrentRetrievalSession','darwin','4625217nJDeoq','onmessage','download:registerForChanges','protocol','inferencing:setDisableServerLogging','runtimeIndex','127.0.0.1','retrieval','http:','invoking\x20loadModel\x20returned','5752270hXTfcM','filesystem:getChatsCacheFolder','download:overwriteDownload','registerForServerLogEvents.\x20event.data:\x20','electronAPIBase','appSettings','scaffolding','contextBridge','uiState','predictionProcess','memory:registerForUpdates','window:close','send','https:','backendDownload','filesystem:openChatsCacheFolder','serverSession','embedding:loadModel','exposeInMainWorld','systemResources','inferencing:registerForServerStateUpdates','retrieval:retrieve','removeAllListeners'];a1_0x409c=function(){return _0x50502e;};return a1_0x409c();}function a1_0x21c4(_0x414a5b,_0x7cc9a){const _0x409cfd=a1_0x409c();return a1_0x21c4=function(_0x21c41d,_0x34c472){_0x21c41d=_0x21c41d-0xc1;let _0x43dfa2=_0x409cfd[_0x21c41d];return _0x43dfa2;},a1_0x21c4(_0x414a5b,_0x7cc9a);}(function(_0x491a35,_0x8f3e65){const _0x33f3e0=a1_0x21c4,_0x16c742=_0x491a35();while(!![]){try{const _0x23c6df=-parseInt(_0x33f3e0(0x12b))/0x1*(parseInt(_0x33f3e0(0xd7))/0x2)+parseInt(_0x33f3e0(0x106))/0x3*(-parseInt(_0x33f3e0(0xd1))/0x4)+parseInt(_0x33f3e0(0xe7))/0x5+-parseInt(_0x33f3e0(0x11c))/0x6*(-parseInt(_0x33f3e0(0xcf))/0x7)+-parseInt(_0x33f3e0(0xd3))/0x8+parseInt(_0x33f3e0(0x14a))/0x9+parseInt(_0x33f3e0(0x154))/0xa;if(_0x23c6df===_0x8f3e65)break;else _0x16c742['push'](_0x16c742['shift']());}catch(_0x8831b){_0x16c742['push'](_0x16c742['shift']());}}}(a1_0x409c,0x90ee1),((()=>{'use strict';const _0x5aa11f=a1_0x21c4;var _0x5c1502={0x10baf:(_0x2c23b4,_0x187705)=>{const _0xc29d66=a1_0x21c4;Object[_0xc29d66(0x16b)](_0x187705,'__esModule',{'value':!0x0}),_0x187705[_0xc29d66(0x13e)]=_0x187705[_0xc29d66(0xf5)]=_0x187705[_0xc29d66(0xd2)]=void 0x0,_0x187705[_0xc29d66(0xd2)]=(_0x2cf4e8,..._0x5b9221)=>{},_0x187705[_0xc29d66(0xf5)]=(_0x1e67be,..._0x5cb77a)=>{},_0x187705['log_dev_color']=(_0x4f0e9e=_0xc29d66(0xeb),_0x36006f,..._0xc6a2ee)=>{};},0xea9b:(_0x4aaf4d,_0x2ecd5a,_0x106eac)=>{Object['defineProperty'](_0x2ecd5a,'__esModule',{'value':!0x0}),_0x2ecd5a['exposeHostedEnvInPreload']=function(){const _0x9c74fa=a1_0x21c4,_0x53be88={'getApiIpcTunnel':(_0x4dc6a4,_0x34500a,_0x335b03,_0x4f48b4)=>{const _0x38b7d9=a1_0x21c4;switch(_0x4dc6a4){case _0x38b7d9(0xe3):case _0x38b7d9(0x129):case _0x38b7d9(0x159):case'modelIndex':case _0x38b7d9(0xfe):case'contextMenu':case _0x38b7d9(0xf2):case'httpServer':case _0x38b7d9(0x142):case'modelLoading':case'llmInstanceState':case _0x38b7d9(0xd9):case _0x38b7d9(0x133):case _0x38b7d9(0x115):case'system':case _0x38b7d9(0x11a):case _0x38b7d9(0x151):case _0x38b7d9(0x114):case _0x38b7d9(0x167):case _0x38b7d9(0x14f):case _0x38b7d9(0xca):case _0x38b7d9(0xc4):case _0x38b7d9(0x139):case'softwareUpdate':case _0x38b7d9(0x162):case _0x38b7d9(0x15d):case _0x38b7d9(0x134):case _0x38b7d9(0x130):case'pathOpener':case _0x38b7d9(0xdf):case'presets':case _0x38b7d9(0x164):case'repository':case _0x38b7d9(0x12e):case'queuedFileWrites':case _0x38b7d9(0x15c):case _0x38b7d9(0x15a):case'plugins':case _0x38b7d9(0xe6):case'auth':case _0x38b7d9(0xf3):case'notification':case _0x38b7d9(0xd4):case _0x38b7d9(0xcb):case _0x38b7d9(0xc8):case _0x38b7d9(0xe5):case'enterprise':{const {port1:_0x29a662,port2:_0x57e879}=new MessageChannel();return _0x29a662[_0x38b7d9(0xe4)]('message',_0x2056f1=>{const _0x15cf47=_0x38b7d9;_0x5d2218&&console['debug'](_0x15cf47(0xfa)+_0x4dc6a4+']\x20RECEIVE:',_0x2056f1['data']),_0x335b03(_0x2056f1['data']);}),_0x29a662[_0x38b7d9(0xe4)](_0x38b7d9(0xfb),()=>{_0x4f48b4();}),_0x1ef9df[_0x38b7d9(0x128)][_0x38b7d9(0x10a)](_0x38b7d9(0x112)+_0x4dc6a4,_0x34500a,[_0x57e879]),_0x29a662['start'](),_0x42a2d0=>{const _0x30a568=_0x38b7d9;_0x5d2218&&console[_0x30a568(0x140)]('[RC]['+_0x4dc6a4+_0x30a568(0x10b),_0x42a2d0),_0x29a662[_0x30a568(0x10a)](_0x42a2d0);};}default:throw new Error(_0x38b7d9(0xdb)+_0x4dc6a4+'.\x20If\x20this\x20is\x20a\x20new\x20API,\x20please\x20add\x20it\x20to\x20exposeHostedEnvInPreload.ts.');}}};_0x1ef9df[_0x9c74fa(0x15b)][_0x9c74fa(0x166)](_0x9c74fa(0xda),_0x53be88);};const _0x1ef9df=_0x106eac(0x11a6a),_0x5d2218=!0x1;},0x1171f:(_0x7096,_0x7e6844,_0x3c04f5)=>{const _0x4f1b21=a1_0x21c4;Object[_0x4f1b21(0x16b)](_0x7e6844,_0x4f1b21(0x100),{'value':!0x0}),_0x7e6844[_0x4f1b21(0x10d)]=_0x26531f,_0x7e6844[_0x4f1b21(0x125)]=function(){const _0x5968d5=_0x4f1b21;document[_0x5968d5(0xe4)](_0x5968d5(0x12d),_0x2c1504=>{const _0x48afd0=_0x5968d5;let _0x136cb7=_0x2c1504[_0x48afd0(0xf7)];for(;_0x136cb7&&!_0xf8cfc3(_0x136cb7);)_0x136cb7=_0x136cb7['parentElement'];_0xf8cfc3(_0x136cb7)&&_0x43d7f7(_0x136cb7[_0x48afd0(0x126)])&&(console[_0x48afd0(0x135)](_0x48afd0(0x104)+_0x136cb7[_0x48afd0(0x126)]),_0x2c1504[_0x48afd0(0xe9)](),_0x26531f(_0x136cb7[_0x48afd0(0x126)]));});};const _0x2ddaff=_0x3c04f5(0x11a6a),_0x420481=_0x3c04f5(0xdfde);function _0xf8cfc3(_0x48a565){const _0x140480=_0x4f1b21;return null!==_0x48a565&&_0x140480(0x126)in _0x48a565;}function _0x43d7f7(_0x3323cd){const _0x59502c=_0x4f1b21;try{const _0x8015ad=new _0x420481['URL'](_0x3323cd);return(_0x59502c(0x152)===_0x8015ad[_0x59502c(0x14d)]||_0x59502c(0x161)===_0x8015ad['protocol'])&&!['localhost',_0x59502c(0x150),_0x59502c(0x12f)][_0x59502c(0x113)](_0x8015ad[_0x59502c(0xfc)]);}catch{return!0x1;}}function _0x26531f(_0x2ada5e){const _0x322ce9=_0x4f1b21;if(!_0x43d7f7(_0x2ada5e))throw console[_0x322ce9(0x16f)]('Invalid\x20or\x20potentially\x20unsafe\x20URL:',_0x2ada5e),new Error(_0x322ce9(0x121)+_0x2ada5e);_0x2ddaff['ipcRenderer'][_0x322ce9(0x160)](_0x322ce9(0x16c),_0x2ada5e);}},0x11a6a:_0x34f351=>{const _0x9d3a00=a1_0x21c4;_0x34f351['exports']=require(_0x9d3a00(0x116));},0xdfde:_0x1051a1=>{const _0xc625e8=a1_0x21c4;_0x1051a1[_0xc625e8(0xc1)]=require('url');}},_0xf18bb={};function _0x383ff0(_0xef0a55){const _0x24efbe=a1_0x21c4;var _0x8811cc=_0xf18bb[_0xef0a55];if(void 0x0!==_0x8811cc)return _0x8811cc[_0x24efbe(0xc1)];var _0x2df85d=_0xf18bb[_0xef0a55]={'exports':{}};return _0x5c1502[_0xef0a55](_0x2df85d,_0x2df85d[_0x24efbe(0xc1)],_0x383ff0),_0x2df85d[_0x24efbe(0xc1)];}void 0x0!==_0x383ff0&&(_0x383ff0['ab']=__dirname+_0x5aa11f(0xfd)),((()=>{const _0x1e23df=_0x5aa11f,_0x37d7e7=_0x383ff0(0x11a6a),_0x3a4621=_0x383ff0(0x10baf);(0x0,_0x3a4621[_0x1e23df(0xd2)])(_0x1e23df(0x16d)+process[_0x1e23df(0x103)][_0x1e23df(0x117)]);const _0x4fe564=_0x383ff0(0x1171f),_0x371a04=_0x383ff0(0xea9b);(0x0,_0x4fe564[_0x1e23df(0x125)])(),_0x37d7e7[_0x1e23df(0x15b)][_0x1e23df(0x166)](_0x1e23df(0x141),{'platformStr':process[_0x1e23df(0x141)],'isMac':_0x1e23df(0x149)===process[_0x1e23df(0x141)],'isWindows':_0x1e23df(0xd8)===process['platform'],'isLinux':'linux'===process[_0x1e23df(0x141)],'minimize':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0x107)),'maximize':()=>_0x37d7e7[_0x1e23df(0x128)]['send'](_0x1e23df(0xf1)),'close':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0x15f))}),_0x37d7e7[_0x1e23df(0x15b)]['exposeInMainWorld']('assetLoader',{'getCachedModelCatalog':()=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x146)),'registerForModelCatalogUpdates':_0xe1653a=>_0x515ee2(_0xe1653a)}),_0x37d7e7[_0x1e23df(0x15b)]['exposeInMainWorld'](_0x1e23df(0x158),{'getPathForFile':_0x3bb25d=>_0x37d7e7[_0x1e23df(0xdc)][_0x1e23df(0xf6)](_0x3bb25d),'getAppVersion':()=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)](_0x1e23df(0xc7)),'getCurrentGpuBackendName':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xff)),'getAccelerationAvailable':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('hardware:get-acceleration-available'),'getSystemDiagnosticsData':_0x4e8d72=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('diagnostics:getSystemDiagnosticsData',{'config':_0x4e8d72}),'exportAppLogs':()=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)]('diagnostics:exportAppLogs'),'exportServerLogs':()=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)]('diagnostics:exportServerLogs'),'searchModels':(_0x10d51b,_0x62bcfe)=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)](_0x1e23df(0x102),{'query':_0x10d51b,'sortDirection':_0x62bcfe}),'listModelFiles':_0x45d0cd=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x136),_0x45d0cd),'downloadModelFileGGUFPreviewFromNetwork':(_0x24e574,_0xa3870d)=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x144),{'model':_0x24e574,'file':_0xa3870d}),'listActiveDownloads':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x143)),'listAllDownloads':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('download:listAllDownloads'),'registerForDownloadChanges':_0x189b7b=>_0x1e6fe2(_0x189b7b),'registerForGenericMessages':_0x54649b=>_0x432ecc(_0x54649b),'checkForUpdates':()=>_0x37d7e7[_0x1e23df(0x128)]['send'](_0x1e23df(0xcc)),'downloadUpdate':()=>_0x37d7e7[_0x1e23df(0x128)]['send'](_0x1e23df(0x108)),'installUpdate':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)]('app:installUpdate'),'scheduleUpdateDownloadReminder':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0xd5)),'downloadFile':_0xd59b23=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x10a)](_0x1e23df(0x13a),{'request':_0xd59b23}),'downloadMultiPartFile':(_0x327ce5,_0x20bb0d)=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('download:downloadMultiPartFile',{'requests':_0x327ce5,'mergedFileName':_0x20bb0d}),'cancelDownload':_0x11acae=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)](_0x1e23df(0x123),{'url':_0x11acae}),'pauseDownload':_0x4906c8=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)]('download:pauseDownload',{'url':_0x4906c8}),'resumeDownload':_0x368593=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)](_0x1e23df(0xce),{'url':_0x368593}),'retryDownload':_0x21ab99=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)]('download:retryDownload',{'url':_0x21ab99}),'overwriteDownload':_0x1603a1=>_0x37d7e7['ipcRenderer']['send'](_0x1e23df(0x156),{'url':_0x1603a1}),'removeEndedDownload':_0x9f645a=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0x13b),{'url':_0x9f645a}),'migration_addEndedDownloads':_0x1fccd2=>_0x37d7e7['ipcRenderer']['send'](_0x1e23df(0x11d),{'downloads':_0x1fccd2}),'getAvailableModels':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x111)),'deleteSavedModel':_0x5db8e0=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)]('filesystem:deleteSavedModel',{'path':_0x5db8e0}),'triggerDownloadsFolderChangeDialog':()=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x147)),'openDownloadsFolder':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xed)),'openConfigPresetsFolder':()=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0xf0)),'revealPathInFileManager':_0x281157=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)](_0x1e23df(0x13d),_0x281157),'exportModelConfiguration':_0x1f90f9=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)]('filesystem:exportModelConfiguration',{'config':_0x1f90f9}),'importModelConfiguration':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x13c)),'showImportImagesDialog':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x12a)),'getChatsCacheFolder':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x155)),'setChatsCacheFolder':_0x4a151f=>_0x37d7e7['ipcRenderer']['invoke']('filesystem:setChatsCacheFolder',{'migrate':_0x4a151f}),'openChatsCacheFolder':()=>_0x37d7e7['ipcRenderer']['invoke'](_0x1e23df(0x163)),'getGgufPreviewForModel':_0x5d906e=>_0x37d7e7[_0x1e23df(0x128)]['invoke']('filesystem:getGgufPreviewForModel',{'model':{..._0x5d906e}}),'triggerRetrievalSessionsFolderChangeDialog':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('filesystem:triggerRetrievalSessionsFolderChangeDialog'),'getRetrievalSessionsFolder':()=>_0x37d7e7['ipcRenderer']['invoke'](_0x1e23df(0x109)),'listRetrievalSessionsInPath':_0x3c402a=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xea),{'path':_0x3c402a}),'textToMarkdown':_0x381e05=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x10c),{'text':_0x381e05}),'loadModelBase':(_0x4898f8,_0x3921c4)=>_0x30a682(_0x4898f8,_0x3921c4),'getLoadedModels':()=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)]('inferencing:getLoadedModels'),'startModelServer':_0x5f0824=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xe8),_0x5f0824),'stopModelServer':()=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)](_0x1e23df(0x110)),'setDisableServerLogging':_0x5f4b2e=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)](_0x1e23df(0x14e),{'disable':_0x5f4b2e}),'setServerModelConfiguration':(_0xa1b1df,_0xaac284)=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x118),{'model':_0xa1b1df,'config':_0xaac284}),'setInferencingReplyPort':_0x42e3a7=>_0x592cea(_0x42e3a7),'registerForServerStateUpdates':_0x382d1c=>_0x2237ae(_0x382d1c),'registerForServerLogEvents':_0x489911=>_0x4f8bdb(_0x489911),'setThrottlePower':_0x53b70f=>_0x37d7e7['ipcRenderer']['invoke'](_0x1e23df(0xe0),{'coefficient':_0x53b70f}),'unloadModel':_0x32184e=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0x120),{'model':_0x32184e}),'predictTokens':(_0x564bea,_0x486106,_0xd0e96e,_0x5116cd)=>_0x386b02(_0x564bea,_0x486106,_0xd0e96e,_0x5116cd),'stopGenerating':_0x4daf28=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)](_0x1e23df(0x12c),{'model':_0x4daf28}),'tokenize':(_0x5b86e2,_0x158d37,_0x263a0b)=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x119),{'model':_0x5b86e2,'prompt':_0x158d37,'addBos':_0x263a0b}),'setBackendSettings':_0x4d4391=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0xe2),_0x4d4391),'fetchChatsMetadata':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('chats:fetchChatsMetadata'),'createConversation':(_0xfc3835,_0x2d7530)=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)](_0x1e23df(0xc3),{'conversation':_0xfc3835,'config':_0x2d7530}),'saveConversation':(_0x540089,_0x3c407e,_0x189b53)=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x124),{'conversation':_0x540089,'metadata':_0x3c407e,'config':_0x189b53}),'updateMetadata':_0x4f24cf=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x122),_0x4f24cf),'updateConfiguration':(_0x2d9913,_0x50517d)=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('chats:updateConfiguration',{'metadata':_0x2d9913,'config':_0x50517d}),'loadConversation':_0x378ed3=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)]('chats:loadConversation',_0x378ed3),'deleteConversation':_0x5053c2=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x16e),_0x5053c2),'branchConversation':(_0x311e7e,_0x52c09f)=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('chats:branchConversation',{'metadata':_0x311e7e,'messageIndex':_0x52c09f}),'monitorMemoryUsage':_0xa7cb0d=>_0x48dcea(_0xa7cb0d),'getConfigPresets':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xf9)),'getConfigPresetsMap':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x10e)),'updateConfigPresetMap':_0x928d56=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x101),{'presetsMap':_0x928d56}),'saveConfigurationAsPreset':(_0x2682b2,_0x58b0f3)=>_0x37d7e7[_0x1e23df(0x128)]['send']('config:saveConfigurationAsPreset',{'config':_0x2682b2,'skipPermission':_0x58b0f3}),'isConfigMigrationNeeded':()=>_0x37d7e7['ipcRenderer'][_0x1e23df(0xc6)](_0x1e23df(0xe1)),'setModelStructuredOutputConfiguration':(_0x2ff93a,_0x98ad18)=>_0x37d7e7['ipcRenderer']['invoke'](_0x1e23df(0x10f),{'model':_0x2ff93a,'config':_0x98ad18}),'updateModelBaseInferenceParams':(_0x30809f,_0x3ac653)=>_0x37d7e7['ipcRenderer']['invoke'](_0x1e23df(0xc9),{'modelIdentifier':_0x30809f,'config':_0x3ac653}),'getModelBaseInferenceParams':_0x50be19=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('config:getModelBaseInferenceParams',{'modelIdentifier':_0x50be19}),'placeHostedApp':(_0x2ddb68,_0x2ad285)=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0x11e),{'appId':_0x2ddb68,'appPlacement':_0x2ad285}),'hideHostedApp':_0x22c621=>_0x37d7e7['ipcRenderer'][_0x1e23df(0x160)](_0x1e23df(0xf8),{'appId':_0x22c621}),'loadEmbeddingModel':(_0x5a0a25,_0x2c63af)=>_0x50db79(_0x5a0a25,_0x2c63af),'unloadEmbeddingModel':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x160)](_0x1e23df(0xee)),'getLoadedEmbeddingModel':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xd6)),'embedString':(_0x5b5219,_0x1221cd)=>_0x37d7e7['ipcRenderer']['invoke'](_0x1e23df(0x138),{'str':_0x5b5219,'params':_0x1221cd}),'newRetrievalSession':_0x79104c=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)]('retrieval:newRetrievalSession',{'workingDirectory':_0x79104c}),'getCurrentRetrievalSession':()=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x148)),'getRetrievalSessionMetadata':_0x17ada4=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0xcd),{'sessionMetadataPath':_0x17ada4}),'setRetrievalSessionName':_0x4f1ce8=>_0x37d7e7[_0x1e23df(0x128)]['invoke']('retrieval:setRetrievalSessionName',{'sessionName':_0x4f1ce8}),'loadRetrievalSession':_0x430a38=>_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xef),{'sessionMetadataPath':_0x430a38}),'uploadDoc':(_0x4eda6c,_0x51d6d5,_0x308309)=>_0x484a23({'doc':_0x4eda6c,'params':_0x51d6d5},_0x308309),'clearDocs':()=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0xdd)),'clearDocById':_0x2027f5=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x11b),{'docId':_0x2027f5}),'retrieve':(_0x5f2fc1,_0x5c529e)=>_0x37d7e7[_0x1e23df(0x128)]['invoke'](_0x1e23df(0x169),{'query':_0x5f2fc1,'params':_0x5c529e})});const _0x30a682=(_0x26e084,{cancelFunctionReceiver:_0x3a5015,progressCallback:_0x4db339,specifiedPreset:_0x4d128e,baseInferenceParamsOverride:_0x1a4b42,identifier:_0x3599b1}={})=>{const _0x3b76b3=_0x1e23df,_0xd10352=Math[_0x3b76b3(0x11f)](),_0x56cd52=(_0x33ec63,_0x403647,_0x5973b8)=>{_0x403647===_0xd10352&&_0x4db339?.(_0x5973b8);};_0x37d7e7[_0x3b76b3(0x128)]['on'](_0x3b76b3(0x127),_0x56cd52),_0x3a5015?.(()=>{const _0x1e0dfe=_0x3b76b3;_0x37d7e7[_0x1e0dfe(0x128)]['send']('loadModel:cancel',{'loadIdentifier':_0xd10352});});const _0x3472fa=_0x37d7e7[_0x3b76b3(0x128)][_0x3b76b3(0xc6)](_0x3b76b3(0x145),{'loadIdentifier':_0xd10352,'config':_0x26e084,'specifiedPreset':_0x4d128e,'baseInferenceParamsOverride':_0x1a4b42,'identifier':_0x3599b1})['finally'](()=>{const _0x490f46=_0x3b76b3;_0x37d7e7['ipcRenderer'][_0x490f46(0xc2)](_0x490f46(0x127),_0x56cd52);});return(0x0,_0x3a4621['log_dev'])(_0x3b76b3(0x153)),_0x3472fa;},_0x50db79=(_0x239466,_0x53c01a)=>(_0x37d7e7[_0x1e23df(0x128)]['removeAllListeners'](_0x1e23df(0xc5)),_0x37d7e7[_0x1e23df(0x128)]['on'](_0x1e23df(0xc5),(_0x43ce08,_0xf6e8d1)=>{_0x53c01a&&_0x53c01a(_0xf6e8d1);}),_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0x165),{'config':_0x239466})),_0x484a23=(_0x5f4042,_0x2daa41)=>(_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0x16a)](_0x1e23df(0xf4)),_0x37d7e7[_0x1e23df(0x128)]['on'](_0x1e23df(0xf4),(_0x490014,_0xd7d93f,_0x2ccfde)=>{_0x2daa41(_0xd7d93f,_0x2ccfde);}),_0x37d7e7[_0x1e23df(0x128)][_0x1e23df(0xc6)](_0x1e23df(0xd0),_0x5f4042)),_0x592cea=_0x9b152f=>{const _0x988559=_0x1e23df,{port1:_0x320e5a,port2:_0x39a9e3}=new MessageChannel();_0x37d7e7[_0x988559(0x128)][_0x988559(0x10a)]('inferencing:serInferencingReplyPort',null,[_0x39a9e3]),_0x320e5a['onmessage']=_0x1f7915=>{const _0x417cbb=_0x988559;_0x9b152f(_0x1f7915[_0x417cbb(0x13f)]);};},_0x2237ae=_0x782496=>{const _0x3218b0=_0x1e23df;(0x0,_0x3a4621[_0x3218b0(0xd2)])(_0x3218b0(0x137));const {port1:_0x1a52f5,port2:_0x575131}=new MessageChannel();return _0x37d7e7['ipcRenderer'][_0x3218b0(0x10a)](_0x3218b0(0x168),null,[_0x575131]),_0x1a52f5['onmessage']=_0xd86870=>{const _0x211b52=_0x3218b0;(0x0,_0x3a4621['log_dev'])('registerForServerStateUpdates.\x20event.data:\x20'+JSON['stringify'](_0xd86870[_0x211b52(0x13f)])),_0x782496(_0xd86870[_0x211b52(0x13f)]);},()=>{_0x1a52f5['close']();};},_0x4f8bdb=_0x403fb1=>{const _0x39f45f=_0x1e23df;(0x0,_0x3a4621[_0x39f45f(0xd2)])(_0x39f45f(0xec));const {port1:_0x31d943,port2:_0x119da4}=new MessageChannel();return _0x37d7e7['ipcRenderer'][_0x39f45f(0x10a)]('inferencing:registerForServerLogEvents',null,[_0x119da4]),_0x31d943[_0x39f45f(0x14b)]=_0x10cde5=>{const _0x43c47d=_0x39f45f;(0x0,_0x3a4621[_0x43c47d(0xd2)])(_0x43c47d(0x157)+JSON[_0x43c47d(0x131)](_0x10cde5[_0x43c47d(0x13f)])),_0x403fb1(_0x10cde5[_0x43c47d(0x13f)]);},()=>{const _0x5b4339=_0x39f45f;_0x31d943[_0x5b4339(0xfb)]();};},_0x432ecc=_0x25e253=>{const _0x5745f4=_0x1e23df,{port1:_0x3786e5,port2:_0x1696c1}=new MessageChannel();_0x37d7e7['ipcRenderer'][_0x5745f4(0x10a)]('app:registerGenericMessagePort',{},[_0x1696c1]),_0x3786e5[_0x5745f4(0x14b)]=_0x1f50ae=>{const _0x58bff1=_0x5745f4;_0x25e253(_0x1f50ae[_0x58bff1(0x13f)]);},_0x3786e5[_0x5745f4(0xfb)]=()=>{const _0x2fcb6c=_0x5745f4;(0x0,_0x3a4621['log_dev'])(_0x2fcb6c(0xde));};},_0x1e6fe2=_0x4c8fa3=>{const _0x49071f=_0x1e23df,{port1:_0x3b3077,port2:_0x504fed}=new MessageChannel();_0x37d7e7['ipcRenderer'][_0x49071f(0x10a)](_0x49071f(0x14c),{},[_0x504fed]),_0x3b3077[_0x49071f(0x14b)]=_0x2fac11=>{const _0x18fab4=_0x49071f;_0x4c8fa3(_0x2fac11[_0x18fab4(0x13f)]);},_0x3b3077[_0x49071f(0xfb)]=()=>{const _0x5775c6=_0x49071f;(0x0,_0x3a4621[_0x5775c6(0xd2)])(_0x5775c6(0xde));};},_0x515ee2=_0x1e697a=>{const _0x1e5234=_0x1e23df,{port1:_0x20988e,port2:_0x26ea72}=new MessageChannel();_0x37d7e7[_0x1e5234(0x128)][_0x1e5234(0x10a)](_0x1e5234(0x132),{},[_0x26ea72]),_0x20988e[_0x1e5234(0x14b)]=_0x34d5a3=>{const _0x50ab2a=_0x1e5234;_0x1e697a(_0x34d5a3[_0x50ab2a(0x13f)]);},_0x20988e[_0x1e5234(0xfb)]=()=>{const _0x179860=_0x1e5234;(0x0,_0x3a4621[_0x179860(0xd2)])(_0x179860(0xde));};},_0x386b02=(_0x4dc4fd,_0x3159d6,_0xc9e5be,_0x5078de)=>{const _0x4f7ff1=_0x1e23df,{port1:_0x36e3ac,port2:_0x10b84e}=new MessageChannel();_0x37d7e7[_0x4f7ff1(0x128)]['postMessage']('inferencing:predictTokens',{'config':_0x3159d6,'history':_0xc9e5be,'model':_0x4dc4fd},[_0x10b84e]),_0x36e3ac['onmessage']=_0xdafa3c=>{const _0x5e4816=_0x4f7ff1;_0x5078de(_0xdafa3c[_0x5e4816(0x13f)]);},_0x36e3ac['close']=()=>{const _0x2e57f5=_0x4f7ff1;(0x0,_0x3a4621['log_dev'])(_0x2e57f5(0xde));};},_0x48dcea=_0x1c0944=>{const _0x25a1fe=_0x1e23df,{port1:_0x39fc3d,port2:_0x3e457a}=new MessageChannel();_0x37d7e7[_0x25a1fe(0x128)]['postMessage'](_0x25a1fe(0x15e),null,[_0x3e457a]),_0x39fc3d[_0x25a1fe(0x14b)]=_0x3ac946=>{const _0x3f652d=_0x25a1fe;_0x1c0944(_0x3ac946[_0x3f652d(0x13f)]);},_0x39fc3d[_0x25a1fe(0xfb)]=()=>{(0x0,_0x3a4621['log_dev'])('stream\x20ended');};};(0x0,_0x371a04[_0x1e23df(0x105)])();})());})()));