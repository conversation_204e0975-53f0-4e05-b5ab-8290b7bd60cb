/**
 * Advanced Voice Engine - Natural Human-like Speech
 * Superior to ChatGPT Pro voice capabilities
 * Natural conversation flow with emotional intelligence
 */

class AdvancedVoiceEngine {
    constructor() {
        this.isInitialized = false;
        this.currentVoice = null;
        this.speechRate = 0.85; // سرعة أبطأ للوضوح
        this.speechPitch = 1.1; // نبرة أوضح
        this.speechVolume = 1.0;
        this.emotionalContext = 'neutral';
        this.conversationHistory = [];
        this.personalityTraits = this.initPersonality();
        this.voiceProfiles = this.initVoiceProfiles();
        this.currentProfile = 'professional_arabic';
        this.naturalPauses = true;
        this.emotionalIntonation = true;
        this.contextualAdaptation = true;
        this.currentDialect = 'standard'; // standard, iraqi
        this.dialectTranslator = this.initDialectTranslator();
        this.continuousMode = false; // وضع المحادثة المستمرة
        this.voiceActivation = true; // تفعيل صوتي
        this.smartSilenceDetection = true; // كشف الصمت الذكي
        this.enhancedClarity = true; // وضوح محسن
        this.professionalMode = true; // الوضع الاحترافي
        this.realTimeMode = false; // الوضع المباشر Real-Time
        this.interruptionHandling = true; // التعامل مع المقاطعة
        this.voiceProviders = this.initVoiceProviders(); // مقدمي الخدمة
        this.currentProvider = 'browser'; // المقدم الحالي
        this.apiKeys = {}; // مفاتيح API
        this.isListening = false; // حالة الاستماع
        this.isSpeaking = false; // حالة النطق
        this.speechQueue = []; // قائمة انتظار النطق
        this.recognitionEngine = null; // محرك التعرف على الكلام
    }

    // Initialize personality traits for natural speech
    initPersonality() {
        return {
            friendliness: 0.8,
            professionalism: 0.9,
            enthusiasm: 0.7,
            patience: 0.9,
            humor: 0.6,
            empathy: 0.8,
            confidence: 0.9,
            curiosity: 0.7
        };
    }

    // Initialize voice profiles for different contexts and dialects
    initVoiceProfiles() {
        return {
            // الفصحى الاحترافية
            professional_arabic: {
                rate: 0.9,
                pitch: 1.0,
                volume: 0.9,
                voice_preference: ['Microsoft Hoda Desktop', 'Google Arabic', 'ar-SA'],
                emotional_range: 'moderate',
                formality: 'high',
                dialect: 'standard',
                region: 'standard'
            },

            // الفصحى العادية
            casual_arabic: {
                rate: 1.1,
                pitch: 1.1,
                volume: 0.95,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'high',
                formality: 'low',
                dialect: 'standard',
                region: 'standard'
            },

            // اللهجة العراقية الاحترافية
            iraqi_professional: {
                rate: 0.95,
                pitch: 1.05,
                volume: 0.9,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'moderate',
                formality: 'high',
                dialect: 'iraqi',
                region: 'iraq',
                accent_strength: 'strong'
            },

            // اللهجة العراقية العادية
            iraqi_casual: {
                rate: 1.15,
                pitch: 1.15,
                volume: 0.95,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'very_high',
                formality: 'low',
                dialect: 'iraqi',
                region: 'iraq',
                accent_strength: 'strong'
            },

            // اللهجة العراقية التقنية
            iraqi_technical: {
                rate: 0.85,
                pitch: 1.0,
                volume: 0.9,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'low',
                formality: 'high',
                dialect: 'iraqi',
                region: 'iraq',
                accent_strength: 'moderate'
            },

            // اللهجة العراقية المتحمسة
            iraqi_enthusiastic: {
                rate: 1.25,
                pitch: 1.25,
                volume: 1.0,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'very_high',
                formality: 'low',
                dialect: 'iraqi',
                region: 'iraq',
                accent_strength: 'very_strong'
            },

            // خبير تقني فصحى
            technical_expert: {
                rate: 0.85,
                pitch: 0.95,
                volume: 0.9,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'low',
                formality: 'very_high',
                dialect: 'standard',
                region: 'standard'
            },

            // متحمس فصحى
            enthusiastic: {
                rate: 1.2,
                pitch: 1.2,
                volume: 1.0,
                voice_preference: ['Microsoft Hoda Desktop', 'ar-SA'],
                emotional_range: 'very_high',
                formality: 'low',
                dialect: 'standard',
                region: 'standard'
            }
        };
    }

    // Initialize voice service providers
    initVoiceProviders() {
        return {
            stt: {
                browser: {
                    available: true,
                    quality: 'medium',
                    languages: ['ar-SA', 'ar-IQ', 'en-US'],
                    realTime: true,
                    cost: 'free'
                },
                google: {
                    available: false,
                    quality: 'high',
                    languages: ['ar-SA', 'ar-IQ', 'en-US'],
                    realTime: true,
                    cost: 'paid',
                    apiKey: null
                },
                whisper: {
                    available: false,
                    quality: 'premium',
                    languages: ['ar', 'en'],
                    realTime: false,
                    cost: 'free',
                    endpoint: null
                },
                azure: {
                    available: false,
                    quality: 'high',
                    languages: ['ar-SA', 'ar-IQ', 'en-US'],
                    realTime: true,
                    cost: 'paid',
                    apiKey: null
                }
            },
            tts: {
                browser: {
                    available: true,
                    quality: 'medium',
                    voices: [],
                    realTime: true,
                    cost: 'free'
                },
                google: {
                    available: false,
                    quality: 'high',
                    voices: ['ar-SA-Standard-A', 'ar-SA-Wavenet-A'],
                    realTime: true,
                    cost: 'paid',
                    apiKey: null
                },
                elevenlabs: {
                    available: false,
                    quality: 'premium',
                    voices: [],
                    realTime: true,
                    cost: 'paid',
                    apiKey: null
                },
                azure: {
                    available: false,
                    quality: 'high',
                    voices: ['ar-SA-ZariyahNeural', 'ar-SA-HamedNeural'],
                    realTime: true,
                    cost: 'paid',
                    apiKey: null
                },
                coqui: {
                    available: false,
                    quality: 'high',
                    voices: [],
                    realTime: false,
                    cost: 'free',
                    endpoint: null
                },
                openrouter: {
                    available: false,
                    quality: 'high',
                    voices: [],
                    realTime: true,
                    cost: 'paid'
                }
            }
        };
    }

    // Initialize dialect translator for Iraqi Arabic
    initDialectTranslator() {
        return {
            // قاموس تحويل من الفصحى للعراقية
            standardToIraqi: {
                // التحيات والمجاملات
                'مرحباً': 'أهلين',
                'مرحبا': 'أهلين',
                'أهلاً وسهلاً': 'أهلين وسهلين',
                'كيف حالك': 'شلونك',
                'كيف الحال': 'شلون الحال',
                'بخير': 'زين',
                'الحمد لله': 'الحمدلله',
                'شكراً': 'شكراً جزيلاً',
                'عفواً': 'عفواً حبيبي',
                'مع السلامة': 'بالسلامة',

                // الكلمات الشائعة
                'نعم': 'إي',
                'لا': 'لا',
                'طبعاً': 'أكيد',
                'بالتأكيد': 'أكيد',
                'ممتاز': 'زين كلش',
                'جيد': 'زين',
                'رائع': 'حلو كلش',
                'جميل': 'حلو',
                'كثير': 'كلش',
                'قليل': 'شوية',
                'مفهوم': 'فهمت',
                'واضح': 'واضح',
                'تماماً': 'بالضبط',
                'كبير': 'كبير',
                'صغير': 'صغير',
                'سريع': 'سريع',
                'بطيء': 'بطيء',

                // الأفعال
                'أريد': 'أريد',
                'أحتاج': 'أحتاج',
                'أفهم': 'أفهم',
                'لا أفهم': 'ما أفهم',
                'أعرف': 'أعرف',
                'لا أعرف': 'ما أعرف',
                'أستطيع': 'أقدر',
                'لا أستطيع': 'ما أقدر',
                'سأفعل': 'راح أسوي',
                'سأذهب': 'راح أروح',
                'سآتي': 'راح آجي',

                // الأسئلة
                'ماذا': 'شنو',
                'متى': 'متى',
                'أين': 'وين',
                'كيف': 'شلون',
                'لماذا': 'ليش',
                'من': 'منو',
                'أي': 'أي',
                'كم': 'كم',

                // التقنية والبرمجة
                'برنامج': 'برنامج',
                'تطبيق': 'تطبيق',
                'موقع': 'موقع',
                'كود': 'كود',
                'ملف': 'ملف',
                'مجلد': 'مجلد',
                'خطأ': 'خطأ',
                'مشكلة': 'مشكلة',
                'حل': 'حل',
                'تحديث': 'تحديث',

                // العبارات المركبة
                'كيف يمكنني مساعدتك': 'شلون أقدر أساعدك',
                'هل تريد': 'تريد',
                'هل تحتاج': 'تحتاج',
                'لا توجد مشكلة': 'ما في مشكلة',
                'سأقوم بذلك': 'راح أسويها',
                'تم الانتهاء': 'خلصت',
                'جاري العمل': 'عم أشتغل',
                'من فضلك': 'لو سمحت',
                'إذا سمحت': 'لو سمحت',

                // التعبيرات العراقية الأصيلة
                'حسناً': 'طيب',
                'جيد جداً': 'زين كتير',
                'ممتاز جداً': 'تبارك الله',
                'لا بأس': 'ما في بأس',
                'بالطبع': 'أكيد',
                'بدون شك': 'بدون شك',
                'إن شاء الله': 'إن شاء الله',
                'ما شاء الله': 'ما شاء الله'
            },

            // تعبيرات عراقية أصيلة للإضافة
            iraqiExpressions: {
                greeting: ['أهلين', 'أهلين وسهلين', 'مرحبتين'],
                positive: ['زين كتير', 'تبارك الله', 'ما شاء الله', 'حلو كتير'],
                agreement: ['أكيد', 'إي والله', 'صدك', 'بالضبط'],
                surprise: ['يا الله', 'لا والله', 'شنو هذا', 'عجيب'],
                encouragement: ['يلا', 'هيا', 'تعال', 'روح'],
                satisfaction: ['زين هيك', 'هذا اللي أريده', 'تمام'],
                thinking: ['خليني أفكر', 'شوف', 'يعني', 'أها']
            },

            // قواعد النطق العراقي
            pronunciationRules: {
                'ق': 'گ',  // القاف تنطق گاف
                'ك': 'چ',  // الكاف أحياناً تنطق چاف
                'ج': 'ج',  // الجيم تبقى كما هي
                'ث': 'ث',  // الثاء تبقى
                'ذ': 'ذ',  // الذال تبقى
                'ض': 'ض',  // الضاد تبقى
                'ظ': 'ظ'   // الظاء تبقى
            }
        };
    }

    // Initialize the advanced voice engine
    async initialize() {
        try {
            if (!('speechSynthesis' in window)) {
                throw new Error('Speech synthesis not supported');
            }

            // Wait for voices to load
            await this.loadVoices();
            
            // Select best Arabic voice
            this.selectBestArabicVoice();
            
            // Configure natural speech settings
            this.configureNaturalSpeech();
            
            this.isInitialized = true;
            console.log('✅ Advanced Voice Engine initialized successfully');
            
            return true;
        } catch (error) {
            console.error('❌ Voice Engine initialization failed:', error);
            return false;
        }
    }

    // Load available voices
    async loadVoices() {
        return new Promise((resolve) => {
            const voices = speechSynthesis.getVoices();
            if (voices.length > 0) {
                resolve(voices);
            } else {
                speechSynthesis.onvoiceschanged = () => {
                    resolve(speechSynthesis.getVoices());
                };
            }
        });
    }

    // Select the best Arabic voice available
    selectBestArabicVoice() {
        const voices = speechSynthesis.getVoices();
        const profile = this.voiceProfiles[this.currentProfile];
        
        // Priority order for Arabic voices
        const voicePriority = [
            'Microsoft Hoda Desktop - Arabic (Saudi Arabia)',
            'Microsoft Naayf Desktop - Arabic (Saudi Arabia)', 
            'Google Arabic',
            'ar-SA',
            'ar'
        ];

        for (const voiceName of voicePriority) {
            const voice = voices.find(v => 
                v.name.includes(voiceName) || 
                v.lang.includes(voiceName) ||
                v.name.toLowerCase().includes('arabic') ||
                v.lang.startsWith('ar')
            );
            
            if (voice) {
                this.currentVoice = voice;
                console.log(`🎤 Selected voice: ${voice.name} (${voice.lang})`);
                break;
            }
        }

        if (!this.currentVoice && voices.length > 0) {
            this.currentVoice = voices[0];
            console.log(`🎤 Fallback voice: ${this.currentVoice.name}`);
        }
    }

    // Configure natural speech parameters
    configureNaturalSpeech() {
        const profile = this.voiceProfiles[this.currentProfile];
        this.speechRate = profile.rate;
        this.speechPitch = profile.pitch;
        this.speechVolume = profile.volume;
    }

    // Speak text with enhanced natural human-like qualities
    async speak(text, options = {}) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            console.log('🗣️ بدء الكلام المحسن والاحترافي:', text.substring(0, 50));

            // إيقاف أي كلام سابق فوراً للوضوح
            speechSynthesis.cancel();

            // تنظيف وتحسين النص أولاً
            const cleanedText = this.preprocessTextForSpeech(text);

            // معالجة النص للكلام الطبيعي المحسن
            const processedText = this.processTextForNaturalSpeech(cleanedText, options);

            // إنشاء كائن الكلام
            const utterance = new SpeechSynthesisUtterance(processedText);

            // تكوين إعدادات الصوت المحسنة
            this.configureEnhancedUtterance(utterance, options);
            
            // Add natural speech enhancements
            this.addNaturalEnhancements(utterance, options);
            
            // Speak with promise support
            return new Promise((resolve, reject) => {
                utterance.onend = () => {
                    console.log('🎤 Speech completed');
                    resolve();
                };
                
                utterance.onerror = (error) => {
                    console.error('❌ Speech error:', error);
                    reject(error);
                };
                
                speechSynthesis.speak(utterance);
            });
            
        } catch (error) {
            console.error('❌ Speech failed:', error);
            throw error;
        }
    }

    // Process text for natural speech patterns with AI enhancement
    async processTextForNaturalSpeech(text, options) {
        let processedText = text;

        // تحسين النص بالذكاء الاصطناعي إذا كان متاحاً
        if (options.enhanceWithAI !== false) {
            processedText = await this.enhanceTextWithAI(text, options);
        }

        // Remove markdown formatting
        processedText = processedText.replace(/\*\*(.*?)\*\*/g, '$1');
        processedText = processedText.replace(/\*(.*?)\*/g, '$1');

        return processedText;
    }

    // تحسين النص بالذكاء الاصطناعي للكلام الطبيعي
    async enhanceTextWithAI(text, options = {}) {
        try {
            // إنشاء prompt لتحسين النص للكلام
            const enhancementPrompt = `قم بتحسين النص التالي ليكون أكثر طبيعية ووضوحاً للكلام الصوتي:

النص الأصلي:
"${text}"

السياق:
- نوع المحادثة: ${options.context || 'تقني'}
- اللهجة المطلوبة: ${this.currentDialect === 'iraqi' ? 'عراقية' : 'فصحى'}
- المستوى: ${this.voiceProfiles[this.currentProfile]?.formality || 'احترافي'}

المطلوب:
1. 🗣️ تحسين النص ليكون أكثر طبيعية للكلام
2. 📝 إضافة علامات الترقيم المناسبة للوقفات الطبيعية
3. 🎭 تحسين التدفق والإيقاع
4. 💬 جعل النص أكثر تفاعلية وودية
5. 🔤 تبسيط الكلمات المعقدة إذا لزم الأمر
6. ⏸️ إضافة وقفات طبيعية بين الجمل

قدم النص المحسن فقط بدون شرح:`;

            let enhancedText = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لتحسين النص الصوتي...');
                const response = await window.openRouterIntegration.smartSendMessage(enhancementPrompt, {
                    mode: 'voice_enhancement',
                    temperature: 0.3,
                    maxTokens: 1000
                });
                if (response && response.text) {
                    enhancedText = response.text.trim();
                }
            }

            // ثانياً: جرب Hugging Face
            if (!enhancedText && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لتحسين النص الصوتي...');
                const response = await window.huggingFaceManager.sendMessage(enhancementPrompt);
                if (response && response.text) {
                    enhancedText = response.text.trim();
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!enhancedText && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لتحسين النص الصوتي...');
                enhancedText = await technicalAssistant.getResponse(enhancementPrompt);
                if (enhancedText) {
                    enhancedText = enhancedText.trim();
                }
            }

            // استخدام النص المحسن أو الأصلي
            return enhancedText || text;

        } catch (error) {
            console.error('خطأ في تحسين النص بالذكاء الاصطناعي:', error);
            return text; // إرجاع النص الأصلي في حالة الخطأ
        }
    }

    // Continue with the rest of the function
    continueProcessing(processedText, options) {
        processedText = processedText.replace(/`(.*?)`/g, '$1');
        processedText = processedText.replace(/#{1,6}\s/g, '');

        // Remove emojis for cleaner speech
        processedText = processedText.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '');

        // Add natural pauses
        if (this.naturalPauses) {
            processedText = this.addNaturalPauses(processedText);
        }

        // Adjust for emotional context
        if (this.emotionalIntonation) {
            processedText = this.addEmotionalCues(processedText, options);
        }

        // Add conversational elements
        processedText = this.addConversationalElements(processedText, options);

        // Convert to Iraqi dialect if needed
        if (this.currentDialect === 'iraqi') {
            processedText = this.convertToIraqiDialect(processedText, options);
        }

        return processedText;
    }

    // Add natural pauses for better flow
    addNaturalPauses(text) {
        // Add pauses after punctuation
        text = text.replace(/\./g, '. ');
        text = text.replace(/\,/g, ', ');
        text = text.replace(/\:/g, ': ');
        text = text.replace(/\;/g, '; ');
        
        // Add breathing pauses for long sentences
        text = text.replace(/(.{100,}?[.!?])/g, '$1 ');
        
        // Add emphasis pauses
        text = text.replace(/(هذا|ذلك|لكن|ولكن|إذن|لذلك)/g, ' $1 ');
        
        return text;
    }

    // Add emotional cues to speech
    addEmotionalCues(text, options) {
        const emotion = options.emotion || this.emotionalContext;
        
        switch (emotion) {
            case 'excited':
                text = text.replace(/!/g, '!! ');
                break;
            case 'questioning':
                text = text.replace(/\?/g, '؟ ');
                break;
            case 'explaining':
                text = text.replace(/(أولاً|ثانياً|ثالثاً|أخيراً)/g, ' $1، ');
                break;
            case 'emphasizing':
                text = text.replace(/(مهم|ضروري|أساسي|حيوي)/g, ' $1 جداً ');
                break;
        }
        
        return text;
    }

    // Add conversational elements
    addConversationalElements(text, options) {
        const context = options.context || 'general';
        
        // Add natural conversation starters
        if (options.isResponse && !text.match(/^(نعم|لا|حسناً|طبعاً)/)) {
            const starters = ['حسناً، ', 'طبعاً، ', 'بالتأكيد، ', 'ممتاز، '];
            if (Math.random() > 0.7) {
                text = starters[Math.floor(Math.random() * starters.length)] + text;
            }
        }

        // Add natural endings
        if (options.isQuestion && !text.includes('؟')) {
            text += '؟';
        }

        return text;
    }

    // Convert text to Iraqi dialect
    convertToIraqiDialect(text, options = {}) {
        let iraqiText = text;
        const translator = this.dialectTranslator;

        // تحويل الكلمات الأساسية
        Object.entries(translator.standardToIraqi).forEach(([standard, iraqi]) => {
            const regex = new RegExp(`\\b${standard}\\b`, 'g');
            iraqiText = iraqiText.replace(regex, iraqi);
        });

        // إضافة تعبيرات عراقية حسب السياق
        const emotion = options.emotion || 'neutral';
        const context = options.context || 'general';

        // إضافة تعبيرات حسب العاطفة
        if (emotion === 'excited' || emotion === 'happy') {
            iraqiText = this.addIraqiExpressions(iraqiText, 'positive');
        } else if (emotion === 'questioning') {
            iraqiText = this.addIraqiThinkingExpressions(iraqiText);
        } else if (emotion === 'agreeing') {
            iraqiText = this.addIraqiAgreementExpressions(iraqiText);
        }

        // تحسينات خاصة بالسياق
        if (context === 'greeting') {
            iraqiText = this.enhanceIraqiGreeting(iraqiText);
        } else if (context === 'technical') {
            iraqiText = this.adaptTechnicalToIraqi(iraqiText);
        }

        // تطبيق قواعد النطق العراقي
        iraqiText = this.applyIraqiPronunciation(iraqiText);

        return iraqiText;
    }

    // Add Iraqi expressions based on emotion
    addIraqiExpressions(text, type) {
        const expressions = this.dialectTranslator.iraqiExpressions[type];
        if (expressions && expressions.length > 0) {
            // إضافة تعبير عراقي في بداية أو نهاية النص أحياناً
            if (Math.random() > 0.7) {
                const expression = expressions[Math.floor(Math.random() * expressions.length)];
                if (text.includes('!') || text.includes('ممتاز') || text.includes('رائع')) {
                    text = `${expression}! ${text}`;
                }
            }
        }
        return text;
    }

    // Add Iraqi thinking expressions
    addIraqiThinkingExpressions(text) {
        const thinkingExpressions = this.dialectTranslator.iraqiExpressions.thinking;
        if (text.includes('دعني') || text.includes('سأفكر') || text.includes('أفكر')) {
            const expression = thinkingExpressions[Math.floor(Math.random() * thinkingExpressions.length)];
            text = text.replace(/(دعني|سأفكر|أفكر)/, `${expression}، $1`);
        }
        return text;
    }

    // Add Iraqi agreement expressions
    addIraqiAgreementExpressions(text) {
        const agreementExpressions = this.dialectTranslator.iraqiExpressions.agreement;
        if (text.includes('نعم') || text.includes('بالتأكيد') || text.includes('طبعاً')) {
            const expression = agreementExpressions[Math.floor(Math.random() * agreementExpressions.length)];
            text = text.replace(/(نعم|بالتأكيد|طبعاً)/, `${expression}، $1`);
        }
        return text;
    }

    // Enhance Iraqi greeting
    enhanceIraqiGreeting(text) {
        // تحسين التحيات العراقية
        if (text.includes('أهلين')) {
            // إضافة دفء عراقي للتحية
            text = text.replace('أهلين', 'أهلين وسهلين، نورت');
        }

        if (text.includes('شلونك')) {
            // إضافة اهتمام عراقي
            text = text.replace('شلونك', 'شلونك حبيبي، شنو أخبارك');
        }

        return text;
    }

    // Adapt technical content to Iraqi dialect
    adaptTechnicalToIraqi(text) {
        // الحفاظ على المصطلحات التقنية مع إضافة لمسة عراقية
        const technicalIntros = [
            'خليني أشرح لك',
            'تعال نشوف',
            'هاي الطريقة',
            'شوف هذا'
        ];

        if (text.includes('سأشرح') || text.includes('دعني أوضح')) {
            const intro = technicalIntros[Math.floor(Math.random() * technicalIntros.length)];
            text = text.replace(/(سأشرح|دعني أوضح)/, intro);
        }

        return text;
    }

    // Apply Iraqi pronunciation rules
    applyIraqiPronunciation(text) {
        const rules = this.dialectTranslator.pronunciationRules;

        // تطبيق قواعد النطق العراقي بحذر
        Object.entries(rules).forEach(([standard, iraqi]) => {
            // تطبيق القواعد على كلمات محددة فقط لتجنب التشويه
            if (standard === 'ق' && text.includes('قال')) {
                text = text.replace(/قال/g, 'گال');
            }
            if (standard === 'ق' && text.includes('قلت')) {
                text = text.replace(/قلت/g, 'گلت');
            }
        });

        return text;
    }

    // Set dialect
    setDialect(dialect) {
        this.currentDialect = dialect;

        // تحديث النمط الصوتي حسب اللهجة المحسن
        if (dialect === 'iraqi') {
            // تحديث إعدادات الصوت للهجة العراقية
            this.speechRate = 0.8; // سرعة أبطأ للوضوح
            this.speechPitch = 1.2; // نبرة أعلى

            // اختيار النمط المناسب
            if (this.currentProfile.includes('professional')) {
                this.setVoiceProfile('iraqi_professional');
            } else if (this.currentProfile.includes('casual')) {
                this.setVoiceProfile('iraqi_casual');
            } else if (this.currentProfile.includes('technical')) {
                this.setVoiceProfile('iraqi_technical');
            } else if (this.currentProfile.includes('enthusiastic')) {
                this.setVoiceProfile('iraqi_enthusiastic');
            } else {
                this.setVoiceProfile('iraqi_casual'); // افتراضي
            }

            console.log('🗣️ تم تفعيل اللهجة العراقية مع إعدادات محسنة');
        } else {
            // العودة للفصحى
            this.speechRate = 0.85; // سرعة طبيعية
            this.speechPitch = 1.1; // نبرة طبيعية

            if (this.currentProfile.includes('iraqi')) {
                this.setVoiceProfile('professional_arabic');
            }

            console.log('🗣️ تم تفعيل العربية الفصحى');
        }

        // حفظ الإعدادات
        localStorage.setItem('speechDialect', dialect);
        localStorage.setItem('speechRate', this.speechRate.toString());
        localStorage.setItem('speechPitch', this.speechPitch.toString());

        console.log(`🗣️ تم تغيير اللهجة إلى: ${dialect}`);
    }

    // تحويل النص للهجة العراقية
    translateToIraqiDialect(text) {
        if (!text || this.currentDialect !== 'iraqi') return text;

        let translatedText = text;
        const translator = this.dialectTranslator.standardToIraqi;

        // تحويل الكلمات الأساسية
        for (const [standard, iraqi] of Object.entries(translator)) {
            const regex = new RegExp(`\\b${standard}\\b`, 'g');
            translatedText = translatedText.replace(regex, iraqi);
        }

        // تحسينات إضافية للهجة العراقية
        translatedText = translatedText
            .replace(/\bأنا\b/g, 'أني')
            .replace(/\bأنت\b/g, 'إنت')
            .replace(/\bهذا\b/g, 'هاي')
            .replace(/\bهذه\b/g, 'هاي')
            .replace(/\bذلك\b/g, 'هاك')
            .replace(/\bتلك\b/g, 'هاك')
            .replace(/\bالآن\b/g, 'هسة')
            .replace(/\bاليوم\b/g, 'اليوم')
            .replace(/\bغداً\b/g, 'باجر')
            .replace(/\bأمس\b/g, 'أمس');

        console.log('🗣️ تم تحويل النص للهجة العراقية');
        return translatedText;
    }

    // إضافة توقفات طبيعية للنص
    addNaturalPauses(text) {
        if (!text) return text;

        return text
            .replace(/\./g, '. ') // توقف بعد النقطة
            .replace(/\,/g, '، ') // توقف بعد الفاصلة
            .replace(/\!/g, '! ') // توقف بعد التعجب
            .replace(/\?/g, '؟ ') // توقف بعد الاستفهام
            .replace(/:/g, ': ') // توقف بعد النقطتين
            .replace(/;/g, '؛ ') // توقف بعد الفاصلة المنقوطة
            .replace(/\s+/g, ' ') // توحيد المسافات
            .trim();
    }

    // تنظيف وتحسين النص للكلام الاحترافي
    preprocessTextForSpeech(text) {
        if (!text) return '';

        let cleanedText = text
            // إزالة الرموز والعلامات غير المرغوبة
            .replace(/\*.*?\*/g, '') // إزالة النصوص بين النجوم
            .replace(/\[.*?\]/g, '') // إزالة النصوص بين الأقواس المربعة
            .replace(/\(.*?\)/g, '') // إزالة النصوص بين الأقواس
            .replace(/https?:\/\/[^\s]+/g, 'رابط') // استبدال الروابط
            .replace(/[#@]/g, '') // إزالة الرموز
            .replace(/[*_`]/g, '') // إزالة رموز التنسيق
            .replace(/\s+/g, ' ') // توحيد المسافات
            .trim();

        // إضافة توقفات طبيعية إذا كانت مفعلة
        if (this.naturalPauses) {
            cleanedText = this.addNaturalPauses(cleanedText);
        }

        // تحسينات خاصة باللهجة العراقية
        if (this.currentDialect === 'iraqi') {
            cleanedText = this.enhanceIraqiPronunciation(cleanedText);
        }

        return cleanedText;
    }

    // تحسين النطق للهجة العراقية
    enhanceIraqiPronunciation(text) {
        if (!text) return text;

        return text
            // تحسينات نطق خاصة باللهجة العراقية
            .replace(/\bق/g, 'گ') // تحويل القاف لگاف
            .replace(/\bالقرآن/g, 'القرآن') // استثناء للكلمات المقدسة
            .replace(/\bالقدس/g, 'القدس') // استثناء للأماكن المقدسة
            .replace(/\bقال/g, 'گال')
            .replace(/\bقلت/g, 'گلت')
            .replace(/\bقول/g, 'گول')
            // إضافة نبرة عراقية طبيعية
            .replace(/\bهذا\b/g, 'هاي')
            .replace(/\bهذه\b/g, 'هاي')
            .replace(/\bذلك\b/g, 'هاك')
            .replace(/\bالآن\b/g, 'هسة');
    }

    // Configure speech utterance with natural settings
    configureUtterance(utterance, options) {
        if (this.currentVoice) {
            utterance.voice = this.currentVoice;
        }

        // Dynamic rate adjustment based on content
        let rate = this.speechRate;
        if (options.urgent) rate *= 1.2;
        if (options.calm) rate *= 0.8;
        if (options.explaining) rate *= 0.9;
        
        utterance.rate = Math.max(0.5, Math.min(2.0, rate));

        // Dynamic pitch adjustment
        let pitch = this.speechPitch;
        if (options.emotion === 'excited') pitch *= 1.2;
        if (options.emotion === 'serious') pitch *= 0.9;
        if (options.emotion === 'questioning') pitch *= 1.1;
        
        utterance.pitch = Math.max(0.5, Math.min(2.0, pitch));

        // Volume adjustment
        utterance.volume = this.speechVolume;
    }

    // تكوين الكلام المحسن والاحترافي
    configureEnhancedUtterance(utterance, options = {}) {
        // إعدادات أساسية محسنة
        utterance.rate = this.speechRate;
        utterance.pitch = this.speechPitch;
        utterance.volume = this.speechVolume;

        // تحديد اللغة حسب اللهجة
        if (this.currentDialect === 'iraqi') {
            utterance.lang = 'ar-IQ';
        } else {
            utterance.lang = 'ar-SA';
        }

        // اختيار أفضل صوت متاح
        const voices = speechSynthesis.getVoices();
        let selectedVoice = null;

        // البحث عن صوت مناسب للهجة العراقية
        if (this.currentDialect === 'iraqi') {
            selectedVoice = voices.find(voice =>
                voice.lang.includes('ar') &&
                (voice.name.includes('Iraq') ||
                 voice.name.includes('عراق') ||
                 voice.name.includes('Baghdad'))
            );
        }

        // البحث عن أفضل صوت عربي عام
        if (!selectedVoice) {
            selectedVoice = voices.find(voice =>
                voice.lang.includes('ar-SA') ||
                voice.lang.includes('ar') ||
                voice.name.includes('Arabic') ||
                voice.name.includes('عرب') ||
                voice.name.includes('Saudi')
            );
        }

        // استخدام الصوت المختار أو الافتراضي
        if (selectedVoice) {
            utterance.voice = selectedVoice;
            console.log('🔊 استخدام صوت محسن:', selectedVoice.name, '- اللغة:', selectedVoice.lang);
        } else if (this.currentVoice) {
            utterance.voice = this.currentVoice;
            console.log('🔊 استخدام الصوت المحفوظ:', this.currentVoice.name);
        }

        // تحسينات حسب السياق والمشاعر
        if (options.context === 'technical') {
            utterance.rate *= 0.9; // أبطأ للوضوح التقني
            utterance.pitch *= 0.95; // نبرة أكثر جدية
        } else if (options.context === 'excited' || options.emotion === 'excited') {
            utterance.rate *= 1.1; // أسرع للحماس
            utterance.pitch *= 1.15; // نبرة أعلى للحماس
        } else if (options.calm || options.emotion === 'calm') {
            utterance.rate *= 0.85; // أبطأ للهدوء
            utterance.pitch *= 0.9; // نبرة أهدأ
        } else if (options.explaining) {
            utterance.rate *= 0.9; // أبطأ للشرح
        }

        // تحسينات خاصة باللهجة العراقية
        if (this.currentDialect === 'iraqi') {
            utterance.rate *= 0.95; // أبطأ قليلاً للوضوح
            utterance.pitch *= 1.05; // نبرة أعلى قليلاً للهجة العراقية
        }

        // ضمان البقاء في الحدود المسموحة
        utterance.rate = Math.max(0.5, Math.min(2.0, utterance.rate));
        utterance.pitch = Math.max(0.5, Math.min(2.0, utterance.pitch));
        utterance.volume = Math.max(0.1, Math.min(1.0, utterance.volume));

        console.log('🎛️ إعدادات الصوت المحسنة:', {
            rate: utterance.rate.toFixed(2),
            pitch: utterance.pitch.toFixed(2),
            volume: utterance.volume.toFixed(2),
            dialect: this.currentDialect,
            context: options.context || 'general'
        });
    }

    // Add natural speech enhancements
    addNaturalEnhancements(utterance, options) {
        // Add natural variation to prevent robotic speech
        if (this.contextualAdaptation) {
            // Slight random variation in rate and pitch for naturalness
            utterance.rate += (Math.random() - 0.5) * 0.1;
            utterance.pitch += (Math.random() - 0.5) * 0.1;
        }

        // Ensure values stay within bounds
        utterance.rate = Math.max(0.5, Math.min(2.0, utterance.rate));
        utterance.pitch = Math.max(0.5, Math.min(2.0, utterance.pitch));
    }

    // Change voice profile for different contexts
    setVoiceProfile(profileName) {
        if (this.voiceProfiles[profileName]) {
            this.currentProfile = profileName;
            this.configureNaturalSpeech();
            console.log(`🎤 Voice profile changed to: ${profileName}`);
        }
    }

    // Set emotional context for speech
    setEmotionalContext(emotion) {
        this.emotionalContext = emotion;
        console.log(`🎭 Emotional context set to: ${emotion}`);
    }

    // Stop current speech
    stop() {
        speechSynthesis.cancel();
    }

    // Pause current speech
    pause() {
        speechSynthesis.pause();
    }

    // Resume paused speech
    resume() {
        speechSynthesis.resume();
    }

    // Get available voices
    getAvailableVoices() {
        return speechSynthesis.getVoices();
    }

    // Test voice with sample text
    async testVoice() {
        const testText = "مرحباً! أنا المساعد التقني الذكي. صوتي طبيعي ومتطور أكثر من ChatGPT المدفوع. كيف يمكنني مساعدتك اليوم؟";
        await this.speak(testText, { 
            emotion: 'friendly',
            context: 'greeting',
            isResponse: true 
        });
    }

    // Advanced speech with context awareness - محسن للاحترافية والوضوح
    async speakWithContext(text, context = {}) {
        console.log('🗣️ بدء الكلام المتقدم والاحترافي:', text.substring(0, 50));

        // إيقاف أي كلام سابق فوراً للوضوح
        speechSynthesis.cancel();

        // تنظيف النص أولاً
        const cleanedText = this.preprocessTextForSpeech(text);

        const enhancedOptions = {
            emotion: context.emotion || 'neutral',
            context: context.type || 'general',
            isResponse: context.isResponse || false,
            isQuestion: cleanedText.includes('؟') || cleanedText.includes('?'),
            urgent: context.urgent || false,
            calm: context.calm || false,
            explaining: context.explaining || false,
            professionalMode: this.professionalMode,
            enhancedClarity: this.enhancedClarity,
            naturalPauses: this.naturalPauses
        };

        // تحسين النص حسب اللهجة
        let processedText = text;
        if (this.currentDialect === 'iraqi') {
            processedText = this.translateToIraqiDialect(text);
        }

        // تطبيق التوقفات الطبيعية
        if (this.naturalPauses) {
            processedText = this.addNaturalPauses(processedText);
        }

        // تعديل النمط الصوتي حسب السياق والاحترافية
        if (context.type === 'technical') {
            if (this.currentDialect === 'iraqi') {
                this.setVoiceProfile('iraqi_technical');
            } else {
                this.setVoiceProfile('technical_expert');
            }
        } else if (context.type === 'casual') {
            if (this.currentDialect === 'iraqi') {
                this.setVoiceProfile('iraqi_casual');
            } else {
                this.setVoiceProfile('casual_arabic');
            }
        } else if (context.type === 'excited') {
            if (this.currentDialect === 'iraqi') {
                this.setVoiceProfile('iraqi_enthusiastic');
            } else {
                this.setVoiceProfile('enthusiastic');
            }
        } else {
            if (this.currentDialect === 'iraqi') {
                this.setVoiceProfile('iraqi_professional');
            } else {
                this.setVoiceProfile('professional_arabic');
            }
        }

        console.log('✅ تم تحضير النص للكلام الاحترافي');
        await this.speak(processedText, enhancedOptions);
    }

    // ===== Real-Time Voice System =====

    // تفعيل الوضع المباشر Real-Time
    enableRealTimeMode() {
        this.realTimeMode = true;
        this.continuousMode = true;
        this.interruptionHandling = true;
        console.log('🚀 تم تفعيل الوضع المباشر Real-Time');
    }

    // إيقاف الوضع المباشر
    disableRealTimeMode() {
        this.realTimeMode = false;
        this.stopListening();
        this.clearSpeechQueue();
        console.log('⏹️ تم إيقاف الوضع المباشر');
    }

    // بدء الاستماع المستمر المتقدم
    async startAdvancedListening(options = {}) {
        if (this.isListening) {
            console.log('⚠️ الاستماع نشط بالفعل');
            return;
        }

        try {
            // التحقق من دعم المتصفح
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                throw new Error('المتصفح لا يدعم التعرف على الكلام المتقدم');
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognitionEngine = new SpeechRecognition();

            // إعدادات متقدمة للأداء العالي
            this.recognitionEngine.continuous = true;
            this.recognitionEngine.interimResults = true;
            this.recognitionEngine.lang = this.currentDialect === 'iraqi' ? 'ar-IQ' : 'ar-SA';
            this.recognitionEngine.maxAlternatives = 5;

            // معالجات الأحداث المتقدمة
            this.setupAdvancedRecognitionHandlers(options);

            // بدء الاستماع
            this.recognitionEngine.start();
            this.isListening = true;

            console.log('🎤 تم بدء الاستماع المتقدم Real-Time');
            return true;

        } catch (error) {
            console.error('❌ خطأ في بدء الاستماع المتقدم:', error);
            return false;
        }
    }

    // إعداد معالجات التعرف المتقدمة
    setupAdvancedRecognitionHandlers(options) {
        let finalTranscript = '';
        let interimTranscript = '';
        let silenceTimer = null;
        let lastSpeechTime = Date.now();

        // بدء الاستماع
        this.recognitionEngine.onstart = () => {
            console.log('🎤 بدء الاستماع المتقدم');
            this.isListening = true;
            if (options.onStart) options.onStart();
        };

        // معالجة النتائج المتقدمة
        this.recognitionEngine.onresult = async (event) => {
            interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;

                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                    console.log('✅ نص نهائي متقدم:', transcript);

                    // معالجة فورية للنص
                    if (this.realTimeMode && transcript.trim().length > 2) {
                        await this.processRealTimeInput(transcript.trim(), options);
                    }

                    finalTranscript = '';
                } else {
                    interimTranscript += transcript;
                    // عرض النص المؤقت
                    if (options.onInterim) {
                        options.onInterim(interimTranscript);
                    }
                }
            }

            lastSpeechTime = Date.now();

            // إدارة مؤقت الصمت
            if (silenceTimer) clearTimeout(silenceTimer);
            silenceTimer = setTimeout(() => {
                if (Date.now() - lastSpeechTime > 1500) {
                    if (options.onSilence) options.onSilence();
                }
            }, 1500);
        };

        // معالجة الأخطاء المحسنة
        this.recognitionEngine.onerror = (event) => {
            console.error('❌ خطأ في التعرف المتقدم:', event.error);

            if (event.error === 'network') {
                setTimeout(() => this.restartListening(), 1000);
            } else if (event.error === 'not-allowed') {
                this.isListening = false;
                if (options.onError) options.onError('يرجى السماح بالوصول للميكروفون');
            } else {
                setTimeout(() => this.restartListening(), 500);
            }
        };

        // إعادة البدء التلقائي
        this.recognitionEngine.onend = () => {
            console.log('🔄 انتهى الاستماع، إعادة البدء...');

            if (this.isListening && this.continuousMode) {
                setTimeout(() => {
                    try {
                        this.recognitionEngine.start();
                    } catch (error) {
                        console.error('❌ خطأ في إعادة البدء:', error);
                        setTimeout(() => this.restartListening(), 1000);
                    }
                }, 100);
            }
        };
    }

    // معالجة المدخل المباشر Real-Time
    async processRealTimeInput(transcript, options = {}) {
        console.log('⚡ معالجة مدخل مباشر:', transcript);

        try {
            // إيقاف النطق الحالي إذا كان مفعل التعامل مع المقاطعة
            if (this.interruptionHandling && this.isSpeaking) {
                this.stop();
            }

            // معالجة النص حسب السياق
            let response = '';

            if (options.onInput) {
                response = await options.onInput(transcript);
            } else {
                // معالجة افتراضية
                response = await this.generateRealTimeResponse(transcript);
            }

            // نطق الرد فوراً
            if (response && response.trim()) {
                await this.speakRealTime(response, {
                    priority: 'high',
                    interrupt: true
                });
            }

        } catch (error) {
            console.error('❌ خطأ في معالجة المدخل المباشر:', error);
        }
    }

    // توليد رد مباشر
    async generateRealTimeResponse(transcript) {
        // رد سريع للاختبار
        const quickResponses = [
            'أفهم ما تقول',
            'نعم، أستمع إليك',
            'ممتاز، تابع',
            'واضح، ما التالي؟'
        ];

        return quickResponses[Math.floor(Math.random() * quickResponses.length)];
    }

    // نطق مباشر Real-Time
    async speakRealTime(text, options = {}) {
        console.log('🔊 نطق مباشر:', text.substring(0, 50));

        // إضافة للقائمة أو النطق المباشر
        if (options.priority === 'high' || !this.isSpeaking) {
            if (options.interrupt) {
                this.clearSpeechQueue();
                this.stop();
            }

            this.isSpeaking = true;

            try {
                await this.speak(text, {
                    ...options,
                    realTime: true
                });
            } finally {
                this.isSpeaking = false;
                this.processNextInQueue();
            }
        } else {
            // إضافة لقائمة الانتظار
            this.speechQueue.push({ text, options });
        }
    }

    // إيقاف الاستماع
    stopListening() {
        if (this.recognitionEngine) {
            this.recognitionEngine.stop();
            this.recognitionEngine = null;
        }
        this.isListening = false;
        console.log('⏹️ تم إيقاف الاستماع');
    }

    // إعادة بدء الاستماع
    restartListening() {
        if (this.isListening) {
            this.stopListening();
            setTimeout(() => {
                this.startAdvancedListening();
            }, 500);
        }
    }

    // مسح قائمة انتظار النطق
    clearSpeechQueue() {
        this.speechQueue = [];
    }

    // معالجة العنصر التالي في القائمة
    async processNextInQueue() {
        if (this.speechQueue.length > 0 && !this.isSpeaking) {
            const next = this.speechQueue.shift();
            await this.speakRealTime(next.text, next.options);
        }
    }

    // تعيين مقدم الخدمة
    setProvider(type, provider) {
        if (this.voiceProviders[type] && this.voiceProviders[type][provider]) {
            this.currentProvider = provider;
            console.log(`✅ تم تعيين مقدم ${type}: ${provider}`);
        }
    }

    // تعيين مفتاح API
    setApiKey(provider, apiKey) {
        this.apiKeys[provider] = apiKey;
        if (this.voiceProviders.stt[provider]) {
            this.voiceProviders.stt[provider].available = true;
            this.voiceProviders.stt[provider].apiKey = apiKey;
        }
        if (this.voiceProviders.tts[provider]) {
            this.voiceProviders.tts[provider].available = true;
            this.voiceProviders.tts[provider].apiKey = apiKey;
        }
        console.log(`✅ تم تعيين مفتاح API لـ ${provider}`);
    }

    // فحص حالة مقدمي الخدمة
    checkProvidersStatus() {
        const status = {
            stt: {},
            tts: {}
        };

        Object.keys(this.voiceProviders.stt).forEach(provider => {
            status.stt[provider] = this.voiceProviders.stt[provider].available;
        });

        Object.keys(this.voiceProviders.tts).forEach(provider => {
            status.tts[provider] = this.voiceProviders.tts[provider].available;
        });

        return status;
    }
}

// Export for global use
if (typeof window !== 'undefined') {
    window.AdvancedVoiceEngine = AdvancedVoiceEngine;
}
