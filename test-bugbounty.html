<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Bug Bounty Mode</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e74c3c;
            border-radius: 10px;
            background: rgba(231, 76, 60, 0.05);
        }
        
        .test-section h3 {
            color: #e74c3c;
            margin-top: 0;
        }
        
        button {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-shield-alt"></i> اختبار Bug Bounty Mode</h1>
        
        <div class="test-section">
            <h3><i class="fas fa-power-off"></i> تفعيل/إلغاء تفعيل الوحدة</h3>
            <button onclick="testActivation()">تفعيل Bug Bounty Mode</button>
            <button onclick="testDeactivation()">إلغاء تفعيل Bug Bounty Mode</button>
            <div id="activationStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-search"></i> اختبار الفحص الأمني</h3>
            <input type="url" id="testUrl" placeholder="https://example.com" style="width: 300px; padding: 10px; margin: 5px;">
            <button onclick="testSecurityScan()">بدء فحص أمني</button>
            <div id="scanStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-microphone"></i> اختبار الأوامر الصوتية</h3>
            <button onclick="testVoiceCommand('فعل bug bounty mode')">أمر: تفعيل Bug Bounty</button>
            <button onclick="testVoiceCommand('افحص موقع https://example.com')">أمر: فحص موقع</button>
            <button onclick="testVoiceCommand('ولد تقرير أمني')">أمر: إنشاء تقرير</button>
            <div id="voiceStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-database"></i> اختبار قاعدة البيانات</h3>
            <button onclick="testVulnerabilityDatabase()">فحص قاعدة بيانات الثغرات</button>
            <button onclick="testPayloadDatabase()">فحص قاعدة بيانات Payloads</button>
            <div id="databaseStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-file-alt"></i> اختبار التقارير</h3>
            <button onclick="testReportGeneration()">إنشاء تقرير تجريبي</button>
            <button onclick="testCSVExport()">تصدير CSV</button>
            <div id="reportStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> سجل الأحداث</h3>
            <button onclick="clearLog()">مسح السجل</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <!-- تضمين ملفات Bug Bounty Mode -->
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>

    <script>
        let bugBountyInstance = null;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[Bug Bounty Test] ${message}`);
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function testActivation() {
            try {
                if (!bugBountyInstance) {
                    bugBountyInstance = new BugBountyCore();
                    log('تم إنشاء مثيل Bug Bounty Core', 'success');
                }
                
                bugBountyInstance.activate();
                setStatus('activationStatus', 'تم تفعيل Bug Bounty Mode بنجاح', 'success');
                log('تم تفعيل Bug Bounty Mode', 'success');
                
            } catch (error) {
                setStatus('activationStatus', `خطأ في التفعيل: ${error.message}`, 'error');
                log(`خطأ في التفعيل: ${error.message}`, 'error');
            }
        }
        
        function testDeactivation() {
            try {
                if (bugBountyInstance) {
                    bugBountyInstance.deactivate();
                    setStatus('activationStatus', 'تم إلغاء تفعيل Bug Bounty Mode', 'info');
                    log('تم إلغاء تفعيل Bug Bounty Mode', 'info');
                } else {
                    setStatus('activationStatus', 'لا يوجد مثيل نشط لإلغاء تفعيله', 'error');
                    log('لا يوجد مثيل نشط لإلغاء تفعيله', 'error');
                }
            } catch (error) {
                setStatus('activationStatus', `خطأ في إلغاء التفعيل: ${error.message}`, 'error');
                log(`خطأ في إلغاء التفعيل: ${error.message}`, 'error');
            }
        }
        
        function testSecurityScan() {
            try {
                if (!bugBountyInstance) {
                    setStatus('scanStatus', 'يجب تفعيل Bug Bounty Mode أولاً', 'error');
                    return;
                }
                
                const url = document.getElementById('testUrl').value || 'https://example.com';
                setStatus('scanStatus', `بدء فحص أمني للموقع: ${url}`, 'info');
                log(`بدء فحص أمني للموقع: ${url}`, 'info');
                
                bugBountyInstance.startComprehensiveScan(url);
                setStatus('scanStatus', 'تم بدء الفحص الأمني بنجاح', 'success');
                log('تم بدء الفحص الأمني', 'success');
                
            } catch (error) {
                setStatus('scanStatus', `خطأ في الفحص: ${error.message}`, 'error');
                log(`خطأ في الفحص: ${error.message}`, 'error');
            }
        }
        
        async function testVoiceCommand(command) {
            try {
                if (!bugBountyInstance) {
                    setStatus('voiceStatus', 'يجب تفعيل Bug Bounty Mode أولاً', 'error');
                    return;
                }
                
                setStatus('voiceStatus', `تنفيذ أمر صوتي: ${command}`, 'info');
                log(`تنفيذ أمر صوتي: ${command}`, 'info');
                
                const response = await bugBountyInstance.handleVoiceCommand(command);
                setStatus('voiceStatus', `رد الأمر: ${response}`, 'success');
                log(`رد الأمر الصوتي: ${response}`, 'success');
                
            } catch (error) {
                setStatus('voiceStatus', `خطأ في الأمر الصوتي: ${error.message}`, 'error');
                log(`خطأ في الأمر الصوتي: ${error.message}`, 'error');
            }
        }
        
        function testVulnerabilityDatabase() {
            try {
                if (!bugBountyInstance) {
                    setStatus('databaseStatus', 'يجب تفعيل Bug Bounty Mode أولاً', 'error');
                    return;
                }
                
                const vulnCount = Object.keys(bugBountyInstance.vulnerabilityDatabase).length;
                let totalVulns = 0;
                
                Object.values(bugBountyInstance.vulnerabilityDatabase).forEach(category => {
                    totalVulns += Object.keys(category).length;
                });
                
                setStatus('databaseStatus', `قاعدة البيانات تحتوي على ${vulnCount} فئات و ${totalVulns} ثغرة`, 'success');
                log(`فحص قاعدة بيانات الثغرات: ${vulnCount} فئات، ${totalVulns} ثغرة`, 'success');
                
            } catch (error) {
                setStatus('databaseStatus', `خطأ في فحص قاعدة البيانات: ${error.message}`, 'error');
                log(`خطأ في فحص قاعدة البيانات: ${error.message}`, 'error');
            }
        }
        
        function testPayloadDatabase() {
            try {
                if (!bugBountyInstance) {
                    setStatus('databaseStatus', 'يجب تفعيل Bug Bounty Mode أولاً', 'error');
                    return;
                }
                
                const payloadTypes = Object.keys(bugBountyInstance.payloadDatabase).length;
                let totalPayloads = 0;
                
                Object.values(bugBountyInstance.payloadDatabase).forEach(payloads => {
                    totalPayloads += payloads.length;
                });
                
                setStatus('databaseStatus', `قاعدة Payloads تحتوي على ${payloadTypes} أنواع و ${totalPayloads} payload`, 'success');
                log(`فحص قاعدة بيانات Payloads: ${payloadTypes} أنواع، ${totalPayloads} payload`, 'success');
                
            } catch (error) {
                setStatus('databaseStatus', `خطأ في فحص قاعدة Payloads: ${error.message}`, 'error');
                log(`خطأ في فحص قاعدة Payloads: ${error.message}`, 'error');
            }
        }
        
        function testReportGeneration() {
            try {
                if (!bugBountyInstance) {
                    setStatus('reportStatus', 'يجب تفعيل Bug Bounty Mode أولاً', 'error');
                    return;
                }
                
                // إنشاء بيانات تجريبية للتقرير
                bugBountyInstance.currentScan = {
                    target: 'https://example.com',
                    startTime: new Date(),
                    endTime: new Date(),
                    vulnerabilities: [
                        {
                            name: 'SQL Injection (Test)',
                            category: 'Injection',
                            severity: 'Critical',
                            cvss: 9.8,
                            description: 'ثغرة SQL injection تجريبية',
                            impact: 'تأثير تجريبي',
                            remediation: 'حل تجريبي',
                            evidence: 'دليل تجريبي',
                            confidence: 85
                        }
                    ]
                };
                
                bugBountyInstance.generateSecurityReport();
                setStatus('reportStatus', 'تم إنشاء تقرير تجريبي بنجاح', 'success');
                log('تم إنشاء تقرير أمني تجريبي', 'success');
                
            } catch (error) {
                setStatus('reportStatus', `خطأ في إنشاء التقرير: ${error.message}`, 'error');
                log(`خطأ في إنشاء التقرير: ${error.message}`, 'error');
            }
        }
        
        function testCSVExport() {
            try {
                if (!bugBountyInstance || !bugBountyInstance.currentScan) {
                    setStatus('reportStatus', 'لا توجد نتائج فحص للتصدير', 'error');
                    return;
                }
                
                bugBountyInstance.exportResults();
                setStatus('reportStatus', 'تم تصدير النتائج بصيغة CSV', 'success');
                log('تم تصدير النتائج بصيغة CSV', 'success');
                
            } catch (error) {
                setStatus('reportStatus', `خطأ في التصدير: ${error.message}`, 'error');
                log(`خطأ في التصدير: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('testLog').textContent = '';
            log('تم مسح السجل', 'info');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة اختبار Bug Bounty Mode', 'success');
            log('جاهز لبدء الاختبارات', 'info');
        });
    </script>
</body>
</html>
