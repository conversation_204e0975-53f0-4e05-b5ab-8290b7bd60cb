function a2_0x4f3e(_0x7625ea,_0x201fb5){const _0x55dd63=a2_0x55dd();return a2_0x4f3e=function(_0x4f3e80,_0x310909){_0x4f3e80=_0x4f3e80-0x18c;let _0xe83c86=_0x55dd63[_0x4f3e80];return _0xe83c86;},a2_0x4f3e(_0x7625ea,_0x201fb5);}(function(_0x3abc09,_0x549042){const _0x34f2c1=a2_0x4f3e,_0x3ce4f8=_0x3abc09();while(!![]){try{const _0x587518=-parseInt(_0x34f2c1(0x1a9))/0x1*(-parseInt(_0x34f2c1(0x1b9))/0x2)+-parseInt(_0x34f2c1(0x191))/0x3*(-parseInt(_0x34f2c1(0x1a3))/0x4)+parseInt(_0x34f2c1(0x198))/0x5*(parseInt(_0x34f2c1(0x1b1))/0x6)+parseInt(_0x34f2c1(0x19c))/0x7+parseInt(_0x34f2c1(0x199))/0x8+parseInt(_0x34f2c1(0x1c6))/0x9*(parseInt(_0x34f2c1(0x1bc))/0xa)+-parseInt(_0x34f2c1(0x1a7))/0xb*(parseInt(_0x34f2c1(0x1ab))/0xc);if(_0x587518===_0x549042)break;else _0x3ce4f8['push'](_0x3ce4f8['shift']());}catch(_0x5b8005){_0x3ce4f8['push'](_0x3ce4f8['shift']());}}}(a2_0x55dd,0x5f0c1),((()=>{'use strict';const _0x3b2520=a2_0x4f3e;var _0x2d261a={0xea9b:(_0x5a1002,_0x46453c,_0x53b5e5)=>{const _0x14b241=a2_0x4f3e;Object['defineProperty'](_0x46453c,_0x14b241(0x19d),{'value':!0x0}),_0x46453c['exposeHostedEnvInPreload']=function(){const _0x2baf6c=_0x14b241,_0x3a8b62={'getApiIpcTunnel':(_0x5ee103,_0x2809cc,_0x41163b,_0x4ca25a)=>{const _0x36b679=a2_0x4f3e;switch(_0x5ee103){case'notepadMinusMinus':case _0x36b679(0x18f):case _0x36b679(0x1aa):case _0x36b679(0x1af):case _0x36b679(0x1a0):case'contextMenu':case _0x36b679(0x194):case'httpServer':case _0x36b679(0x1a5):case _0x36b679(0x1c5):case _0x36b679(0x19e):case _0x36b679(0x1b3):case _0x36b679(0x1be):case _0x36b679(0x1bb):case'system':case _0x36b679(0x1bf):case _0x36b679(0x1c3):case'files':case _0x36b679(0x1bd):case _0x36b679(0x1c1):case _0x36b679(0x1b7):case'downloads':case'userFiles':case'softwareUpdate':case _0x36b679(0x1c4):case _0x36b679(0x1a4):case _0x36b679(0x196):case'virtualModel':case _0x36b679(0x1b8):case _0x36b679(0x1b0):case'presets':case _0x36b679(0x1b2):case _0x36b679(0x1ba):case _0x36b679(0x1b5):case'queuedFileWrites':case _0x36b679(0x1a1):case'scaffolding':case'plugins':case _0x36b679(0x1c2):case'auth':case _0x36b679(0x195):case _0x36b679(0x1ac):case _0x36b679(0x1a6):case'developmentPluginRunning':case _0x36b679(0x1ad):case _0x36b679(0x18e):case _0x36b679(0x1ae):{const {port1:_0x254d9c,port2:_0x14a080}=new MessageChannel();return _0x254d9c[_0x36b679(0x18c)]('message',_0x2e4d67=>{const _0x3653de=_0x36b679;_0x3d68fa&&console[_0x3653de(0x1c0)](_0x3653de(0x19b)+_0x5ee103+_0x3653de(0x193),_0x2e4d67[_0x3653de(0x18d)]),_0x41163b(_0x2e4d67[_0x3653de(0x18d)]);}),_0x254d9c[_0x36b679(0x18c)](_0x36b679(0x1b4),()=>{_0x4ca25a();}),_0x1640cd['ipcRenderer']['postMessage'](_0x36b679(0x190)+_0x5ee103,_0x2809cc,[_0x14a080]),_0x254d9c[_0x36b679(0x1a2)](),_0x2b75fc=>{const _0x43c9c1=_0x36b679;_0x3d68fa&&console[_0x43c9c1(0x1c0)]('[RC]['+_0x5ee103+']\x20SEND:',_0x2b75fc),_0x254d9c[_0x43c9c1(0x1b6)](_0x2b75fc);};}default:throw new Error(_0x36b679(0x192)+_0x5ee103+_0x36b679(0x19a));}}};_0x1640cd[_0x2baf6c(0x1a8)]['exposeInMainWorld'](_0x2baf6c(0x1c7),_0x3a8b62);};const _0x1640cd=_0x53b5e5(0x11a6a),_0x3d68fa=!0x1;},0x11a6a:_0x4beb25=>{const _0x42d93d=a2_0x4f3e;_0x4beb25[_0x42d93d(0x19f)]=require('electron');}},_0x1198c1={};function _0x91d42b(_0x5b21af){const _0xf94bca=a2_0x4f3e;var _0x481b88=_0x1198c1[_0x5b21af];if(void 0x0!==_0x481b88)return _0x481b88['exports'];var _0x5cd5da=_0x1198c1[_0x5b21af]={'exports':{}};return _0x2d261a[_0x5b21af](_0x5cd5da,_0x5cd5da[_0xf94bca(0x19f)],_0x91d42b),_0x5cd5da[_0xf94bca(0x19f)];}void 0x0!==_0x91d42b&&(_0x91d42b['ab']=__dirname+'/native_modules/'),(0x0,_0x91d42b(0xea9b)[_0x3b2520(0x197)])();})()));function a2_0x55dd(){const _0x49c540=['exposeHostedEnvInPreload','245ZBvVig','1382640EwcEFG','.\x20If\x20this\x20is\x20a\x20new\x20API,\x20please\x20add\x20it\x20to\x20exposeHostedEnvInPreload.ts.','[RC][','527324LXPCLv','__esModule','llmInstanceState','exports','presetsEditor','uiState','start','122036ZRgpKz','predictionProcess','search','pluginDownload','11eEAjSd','contextBridge','1dHrBUw','appSettings','24738636ByTAMn','notification','longRunningTask','enterprise','modelIndex','modelData','42270iMEQec','serverSession','transientStorage','close','popOutWindow','postMessage','frameworkIndex','pathOpener','1533854NpUCdx','repository','embedding','3012140EOXvAP','systemResources','llm','diagnostics','debug','runtimeIndex','internalPlugins','retrieval','backendDownload','modelLoading','18yjLhdX','lmsHostedEnv','addEventListener','data','hardwareConfig','deepLinkHandling','api:','48aHhxhI','Unknown\x20API\x20ID:\x20',']\x20RECEIVE:','conversations','cloudBackend','userModelDefaultConfig'];a2_0x55dd=function(){return _0x49c540;};return a2_0x55dd();}