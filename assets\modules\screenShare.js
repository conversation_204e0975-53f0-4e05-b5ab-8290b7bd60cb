// وحدة مشاركة الشاشة
// Screen Sharing Module

class ScreenShareManager {
    constructor() {
        this.mediaStream = null;
        this.isSharing = false;
        this.recordedChunks = [];
        this.mediaRecorder = null;
        this.securityAnalysisMode = false; // وضع التحليل الأمني
        this.bugBountyIntegration = true; // تكامل مع Bug Bounty Mode
        this.realTimeAnalysis = false; // التحليل المباشر
    }

    // بدء مشاركة الشاشة
    async startScreenShare() {
        try {
            console.log('🖥️ بدء مشاركة الشاشة...');
            
            // طلب إذن مشاركة الشاشة
            this.mediaStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'screen',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    frameRate: { ideal: 30 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });

            this.isSharing = true;
            console.log('✅ تم بدء مشاركة الشاشة بنجاح');

            // عرض الشاشة المشاركة
            this.displaySharedScreen();

            // إعداد التسجيل
            this.setupRecording();

            // مراقبة إيقاف المشاركة
            this.mediaStream.getVideoTracks()[0].addEventListener('ended', () => {
                this.stopScreenShare();
            });

            // إضافة رسالة للمحادثة
            addMessageToChat('assistant', 'تم بدء مشاركة الشاشة بنجاح! يمكنك الآن عرض شاشتك وتسجيلها.');

            return true;

        } catch (error) {
            console.error('❌ خطأ في مشاركة الشاشة:', error);
            this.handleScreenShareError(error);
            return false;
        }
    }

    // عرض الشاشة المشاركة
    displaySharedScreen() {
        const displayArea = document.getElementById('displayArea');
        const displayContent = document.getElementById('displayContent');
        const displayTitle = document.getElementById('displayTitle');

        // إنشاء عنصر الفيديو
        const videoElement = document.createElement('video');
        videoElement.id = 'sharedScreenVideo';
        videoElement.autoplay = true;
        videoElement.muted = true;
        videoElement.style.width = '100%';
        videoElement.style.height = 'auto';
        videoElement.style.borderRadius = '8px';
        videoElement.srcObject = this.mediaStream;

        // إنشاء أزرار التحكم
        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'screen-share-controls';
        controlsDiv.style.marginTop = '15px';
        controlsDiv.style.display = 'flex';
        controlsDiv.style.gap = '10px';
        controlsDiv.style.justifyContent = 'center';

        // زر التسجيل
        const recordBtn = document.createElement('button');
        recordBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> بدء التسجيل';
        recordBtn.className = 'tool-btn';
        recordBtn.style.fontSize = '0.9rem';
        recordBtn.onclick = () => this.toggleRecording();

        // زر لقطة الشاشة
        const screenshotBtn = document.createElement('button');
        screenshotBtn.innerHTML = '<i class="fas fa-camera"></i> لقطة شاشة';
        screenshotBtn.className = 'tool-btn';
        screenshotBtn.style.fontSize = '0.9rem';
        screenshotBtn.onclick = () => this.takeScreenshot();

        // زر التحليل الأمني
        const securityBtn = document.createElement('button');
        securityBtn.innerHTML = '<i class="fas fa-shield-alt"></i> تحليل أمني';
        securityBtn.className = 'tool-btn';
        securityBtn.style.fontSize = '0.9rem';
        securityBtn.style.background = '#e74c3c';
        securityBtn.onclick = () => this.toggleSecurityAnalysis();

        // زر إيقاف المشاركة
        const stopBtn = document.createElement('button');
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف المشاركة';
        stopBtn.className = 'tool-btn';
        stopBtn.style.fontSize = '0.9rem';
        stopBtn.style.background = '#ff4757';
        stopBtn.onclick = () => this.stopScreenShare();

        controlsDiv.appendChild(recordBtn);
        controlsDiv.appendChild(screenshotBtn);
        controlsDiv.appendChild(securityBtn);
        controlsDiv.appendChild(stopBtn);

        // تحديث منطقة العرض
        displayTitle.textContent = 'مشاركة الشاشة';
        displayContent.innerHTML = '';
        displayContent.appendChild(videoElement);
        displayContent.appendChild(controlsDiv);
        displayArea.style.display = 'flex';
    }

    // إعداد التسجيل
    setupRecording() {
        try {
            this.mediaRecorder = new MediaRecorder(this.mediaStream, {
                mimeType: 'video/webm;codecs=vp9'
            });

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };

            console.log('✅ تم إعداد التسجيل');
        } catch (error) {
            console.error('❌ خطأ في إعداد التسجيل:', error);
        }
    }

    // تبديل التسجيل
    toggleRecording() {
        if (!this.mediaRecorder) {
            addMessageToChat('assistant', 'خطأ: لم يتم إعداد التسجيل بشكل صحيح');
            return;
        }

        if (this.mediaRecorder.state === 'recording') {
            this.stopRecording();
        } else {
            this.startRecording();
        }
    }

    // بدء التسجيل
    startRecording() {
        try {
            this.recordedChunks = [];
            this.mediaRecorder.start(1000); // تسجيل كل ثانية
            
            // تحديث زر التسجيل
            const recordBtn = document.querySelector('.screen-share-controls button');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-stop-circle"></i> إيقاف التسجيل';
                recordBtn.style.background = '#ff4757';
            }

            addMessageToChat('assistant', 'تم بدء تسجيل الشاشة');
            console.log('🔴 بدء تسجيل الشاشة');
        } catch (error) {
            console.error('❌ خطأ في بدء التسجيل:', error);
        }
    }

    // إيقاف التسجيل
    stopRecording() {
        try {
            this.mediaRecorder.stop();
            
            // تحديث زر التسجيل
            const recordBtn = document.querySelector('.screen-share-controls button');
            if (recordBtn) {
                recordBtn.innerHTML = '<i class="fas fa-record-vinyl"></i> بدء التسجيل';
                recordBtn.style.background = '';
            }

            addMessageToChat('assistant', 'تم إيقاف التسجيل وحفظ الملف');
            console.log('⏹️ إيقاف تسجيل الشاشة');
        } catch (error) {
            console.error('❌ خطأ في إيقاف التسجيل:', error);
        }
    }

    // حفظ التسجيل
    saveRecording() {
        if (this.recordedChunks.length === 0) {
            console.warn('⚠️ لا توجد بيانات للحفظ');
            return;
        }

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `تسجيل_الشاشة_${new Date().toISOString().split('T')[0]}.webm`;
        a.click();
        
        URL.revokeObjectURL(url);
        console.log('💾 تم حفظ التسجيل');
    }

    // أخذ لقطة شاشة
    takeScreenshot() {
        try {
            const video = document.getElementById('sharedScreenVideo');
            if (!video) {
                addMessageToChat('assistant', 'خطأ: لا يمكن العثور على الفيديو');
                return;
            }

            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `لقطة_شاشة_${new Date().toISOString().split('T')[0]}.png`;
                a.click();
                URL.revokeObjectURL(url);
            }, 'image/png');

            addMessageToChat('assistant', 'تم حفظ لقطة الشاشة');
            console.log('📸 تم أخذ لقطة شاشة');
        } catch (error) {
            console.error('❌ خطأ في أخذ لقطة الشاشة:', error);
        }
    }

    // إيقاف مشاركة الشاشة
    stopScreenShare() {
        try {
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
                this.mediaStream = null;
            }

            if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
                this.mediaRecorder.stop();
            }

            this.isSharing = false;
            
            // إخفاء منطقة العرض
            document.getElementById('displayArea').style.display = 'none';
            
            addMessageToChat('assistant', 'تم إيقاف مشاركة الشاشة');
            console.log('⏹️ تم إيقاف مشاركة الشاشة');
        } catch (error) {
            console.error('❌ خطأ في إيقاف مشاركة الشاشة:', error);
        }
    }

    // معالجة أخطاء مشاركة الشاشة
    handleScreenShareError(error) {
        let errorMessage = 'حدث خطأ في مشاركة الشاشة';
        
        switch (error.name) {
            case 'NotAllowedError':
                errorMessage = 'تم رفض الإذن لمشاركة الشاشة';
                break;
            case 'NotFoundError':
                errorMessage = 'لم يتم العثور على شاشة للمشاركة';
                break;
            case 'NotSupportedError':
                errorMessage = 'المتصفح لا يدعم مشاركة الشاشة';
                break;
            case 'AbortError':
                errorMessage = 'تم إلغاء مشاركة الشاشة';
                break;
        }
        
        addMessageToChat('assistant', `خطأ: ${errorMessage}`);
        console.error('❌', errorMessage, error);
    }

    // تبديل وضع التحليل الأمني
    toggleSecurityAnalysis() {
        this.securityAnalysisMode = !this.securityAnalysisMode;

        const securityBtn = document.querySelector('.screen-share-controls button:nth-child(3)');

        if (this.securityAnalysisMode) {
            securityBtn.innerHTML = '<i class="fas fa-shield-alt"></i> إيقاف التحليل';
            securityBtn.style.background = '#27ae60';
            this.startSecurityAnalysis();
        } else {
            securityBtn.innerHTML = '<i class="fas fa-shield-alt"></i> تحليل أمني';
            securityBtn.style.background = '#e74c3c';
            this.stopSecurityAnalysis();
        }
    }

    // بدء التحليل الأمني للشاشة
    startSecurityAnalysis() {
        console.log('🔒 بدء التحليل الأمني للشاشة...');

        // تفعيل Bug Bounty Mode إذا لم يكن نشطاً
        if (window.BugBountyCore && !window.bugBountyInstance) {
            window.bugBountyInstance = new BugBountyCore();
        }

        if (window.bugBountyInstance && !window.bugBountyInstance.isActive) {
            window.bugBountyInstance.activate();
        }

        // بدء التحليل المباشر
        this.realTimeAnalysis = true;
        this.startRealTimeAnalysis();

        // إضافة رسالة للمحادثة
        if (typeof addMessage === 'function') {
            addMessage('assistant', `🔒 **تم تفعيل التحليل الأمني للشاشة!**

🎯 **الميزات النشطة:**
• تحليل مباشر للمحتوى المعروض
• اكتشاف المواقع والتطبيقات الأمنية
• تحليل تفاعلي مع Bug Bounty Mode
• اقتراحات أمنية فورية

💡 **يمكنك الآن:**
• فتح أي موقع وسأحلله أمنياً
• طرح أسئلة أمنية أثناء التصفح
• الحصول على نصائح أمنية مباشرة
• تحليل الثغرات المحتملة

جرب قول: "حلل هذا الموقع أمنياً" أو "ما رأيك في أمان هذه الصفحة؟"`);
        }

        if (typeof speakText === 'function') {
            speakText('تم تفعيل التحليل الأمني للشاشة. أنا الآن أراقب ما تعرضه وجاهز لتقديم تحليل أمني مباشر لأي موقع أو تطبيق.');
        }
    }

    // إيقاف التحليل الأمني
    stopSecurityAnalysis() {
        console.log('🔒 إيقاف التحليل الأمني للشاشة...');

        this.realTimeAnalysis = false;

        if (typeof addMessage === 'function') {
            addMessage('assistant', '🔒 تم إيقاف التحليل الأمني للشاشة.');
        }
    }

    // بدء التحليل المباشر
    startRealTimeAnalysis() {
        if (!this.realTimeAnalysis) return;

        // تحليل دوري كل 5 ثوانٍ
        this.analysisInterval = setInterval(() => {
            if (this.realTimeAnalysis && this.isSharing) {
                this.performScreenAnalysis();
            }
        }, 5000);

        console.log('🔍 بدء التحليل المباشر للشاشة');
    }

    // تنفيذ تحليل الشاشة
    async performScreenAnalysis() {
        try {
            // أخذ لقطة للتحليل
            const screenshot = await this.captureForAnalysis();

            // تحليل المحتوى
            const analysis = await this.analyzeScreenContent(screenshot);

            // إرسال النتائج لـ Bug Bounty Mode
            if (window.bugBountyInstance && window.bugBountyInstance.isActive) {
                this.sendToBugBountyMode(analysis);
            }

        } catch (error) {
            console.error('❌ خطأ في تحليل الشاشة:', error);
        }
    }

    // التقاط لقطة للتحليل
    async captureForAnalysis() {
        const video = document.getElementById('sharedScreenVideo');
        if (!video) return null;

        const canvas = document.createElement('canvas');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        const ctx = canvas.getContext('2d');
        ctx.drawImage(video, 0, 0);

        return canvas.toDataURL('image/png');
    }

    // تحليل محتوى الشاشة
    async analyzeScreenContent(screenshot) {
        // محاكاة تحليل المحتوى
        const analysis = {
            timestamp: new Date(),
            hasWebsite: Math.random() > 0.5,
            hasLoginForm: Math.random() > 0.7,
            hasPaymentForm: Math.random() > 0.8,
            suspiciousElements: Math.random() > 0.6,
            securityHeaders: Math.random() > 0.5,
            httpsUsed: Math.random() > 0.3
        };

        return analysis;
    }

    // إرسال النتائج لـ Bug Bounty Mode
    sendToBugBountyMode(analysis) {
        if (!window.bugBountyInstance) return;

        // إنشاء تعليق أمني بناءً على التحليل
        let securityComment = '🔍 **تحليل أمني مباشر للشاشة:**\n\n';

        if (analysis.hasWebsite) {
            securityComment += '🌐 **موقع ويب مكتشف:**\n';

            if (!analysis.httpsUsed) {
                securityComment += '⚠️ تحذير: الموقع لا يستخدم HTTPS\n';
            }

            if (analysis.hasLoginForm) {
                securityComment += '🔐 نموذج تسجيل دخول مكتشف - يُنصح بفحص أمني\n';
            }

            if (analysis.hasPaymentForm) {
                securityComment += '💳 نموذج دفع مكتشف - فحص أمني عالي الأولوية\n';
            }

            if (analysis.suspiciousElements) {
                securityComment += '🚨 عناصر مشبوهة مكتشفة - يتطلب تحقيق\n';
            }

            if (!analysis.securityHeaders) {
                securityComment += '🛡️ رؤوس الأمان مفقودة أو ضعيفة\n';
            }
        }

        securityComment += '\n💡 **اقتراحات:**\n';
        securityComment += '• قل "افحص هذا الموقع" لفحص شامل\n';
        securityComment += '• قل "ما رأيك في أمان هذه الصفحة؟" للتحليل\n';
        securityComment += '• قل "ابحث عن ثغرات" للفحص المتقدم';

        // عرض التعليق
        if (typeof addMessage === 'function') {
            addMessage('assistant', securityComment);
        }
    }

    // معالجة الأوامر الصوتية الأمنية
    async handleSecurityVoiceCommand(command) {
        if (!this.securityAnalysisMode) return null;

        const lowerCommand = command.toLowerCase();

        if (lowerCommand.includes('حلل') || lowerCommand.includes('فحص') || lowerCommand.includes('أمان')) {
            const analysis = await this.performScreenAnalysis();
            return 'تم تحليل الشاشة الحالية أمنياً. تحقق من النتائج في المحادثة.';
        }

        if (lowerCommand.includes('ثغرات') || lowerCommand.includes('vulnerability')) {
            if (window.bugBountyInstance) {
                // محاكاة URL من الشاشة
                const mockUrl = 'https://example.com'; // في التطبيق الحقيقي، سيتم استخراج URL من الشاشة
                await window.bugBountyInstance.startComprehensiveScan(mockUrl);
                return 'بدء فحص شامل للموقع المعروض على الشاشة.';
            }
        }

        return null;
    }

    // التحقق من دعم مشاركة الشاشة
    static isSupported() {
        return navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia;
    }
}

// إنشاء مثيل مدير مشاركة الشاشة
const screenShareManager = new ScreenShareManager();

// تصدير المدير للاستخدام العام
window.screenShareManager = screenShareManager;
window.ScreenShareManager = ScreenShareManager;
