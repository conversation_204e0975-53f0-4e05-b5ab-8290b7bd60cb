// المساعد التقني الذكي - ملف أساسي مبسط

// متغيرات عامة
window.conversationHistory = [];
let isListening = false;

// إعدادات الصوت الاحترافية المتقدمة
const speechSettings = {
    enabled: true,
    language: 'ar-SA',
    rate: 0.85, // سرعة طبيعية احترافية
    volume: 1.0,
    pitch: 1.1, // نبرة أكثر وضوحاً
    voiceName: 'Arabic Female',
    conversationMode: true,
    autoResponse: true,
    showTextInVoiceMode: false,
    pureVoiceMode: false,
    naturalConversation: true,
    instantResponse: true,
    continuousListening: true, // استمع باستمرار
    smartSilenceDetection: true, // كشف الصمت الذكي
    noiseReduction: true, // تقليل الضوضاء
    voiceActivation: true, // تفعيل صوتي
    autoStopSpeaking: true, // توقف عند بدء المستخدم بالكلام
    naturalPauses: true, // توقفات طبيعية
    dialectSupport: true, // دعم اللهجات
    currentDialect: 'standard', // اللهجة الحالية
    enhancedRecognition: true, // تحسين التعرف على الصوت
    contextAwareness: true // وعي بالسياق
};

// تفعيل الصوت عالمياً
window.voiceEnabled = true;
window.speechSettings = speechSettings;

// ===== دوال الأزرار الصوتية المطلوبة =====

// دالة المحادثة الصوتية الخالصة - مبسطة
window.togglePureVoiceMode = function() {
    console.log('🎤 استدعاء المحادثة الصوتية الخالصة من window');

    // استدعاء الدالة الرئيسية
    if (typeof togglePureVoiceMode === 'function') {
        togglePureVoiceMode();
    } else {
        console.error('❌ دالة togglePureVoiceMode الرئيسية غير متاحة');
    }
};

// دالة التفاعل الصوتي العادي - مبسطة
window.toggleVoiceConversation = function() {
    console.log('🎤 استدعاء التفاعل الصوتي من window');

    // استدعاء الدالة الرئيسية
    if (typeof toggleVoiceConversation === 'function') {
        toggleVoiceConversation();
    } else {
        console.error('❌ دالة toggleVoiceConversation الرئيسية غير متاحة');
    }
};

// دالة تبديل الصوت العام
window.toggleVoice = function() {
    console.log('🔊 تبديل الصوت العام');

    try {
        if (speechSettings.enabled) {
            // إيقاف الصوت
            speechSettings.enabled = false;
            window.voiceEnabled = false;

            if (speechSynthesis) {
                speechSynthesis.cancel();
            }

            console.log('🔇 تم إيقاف الصوت');

        } else {
            // تفعيل الصوت
            speechSettings.enabled = true;
            window.voiceEnabled = true;

            // إشعار صوتي
            setTimeout(() => {
                const message = 'تم تفعيل الصوت';
                if (window.advancedVoiceEngine && typeof window.advancedVoiceEngine.speak === 'function') {
                    window.advancedVoiceEngine.speak(message, {
                        emotion: 'neutral',
                        context: 'notification'
                    });
                } else {
                    speakText(message);
                }
            }, 200);

            console.log('🔊 تم تفعيل الصوت');
        }

    } catch (error) {
        console.error('❌ خطأ في تبديل الصوت:', error);
    }
};

// متغيرات النظام الصوتي المتقدم
let isInConversation = false;
let conversationContext = '';
let lastUserSpeech = '';
let voiceRecognition = null;
let isContinuousListening = false;
let isVoiceEnabled = false;
let isRealTimeMode = false;
let currentVoice = null;

// إعدادات النظام الصوتي المتقدم
let voiceSettings = {
    rate: 1,
    pitch: 1,
    volume: 1,
    language: 'ar-SA',
    dialect: 'iraqi', // دعم اللهجة العراقية
    sttProvider: 'browser', // browser, google, whisper
    ttsProvider: 'browser', // browser, google, elevenlabs, coqui
    autoSpeak: true, // تشغيل الصوت تلقائياً
    continuousMode: false, // الاستماع المستمر
    voiceQuality: 'high', // low, medium, high
    realTimeResponse: true, // الرد الفوري
    interruptionHandling: true // التعامل مع المقاطعة
};

// مقدمي خدمات الصوت المتاحين
let voiceProviders = {
    stt: {
        browser: {
            available: true,
            quality: 'medium',
            languages: ['ar-SA', 'ar-IQ', 'en-US'],
            realTime: true
        },
        google: {
            available: false,
            apiKey: '',
            quality: 'high',
            languages: ['ar-SA', 'ar-IQ', 'en-US'],
            realTime: true
        },
        whisper: {
            available: false,
            endpoint: '',
            quality: 'premium',
            languages: ['ar', 'en'],
            realTime: false
        }
    },
    tts: {
        browser: {
            available: true,
            quality: 'medium',
            voices: [],
            realTime: true
        },
        google: {
            available: false,
            apiKey: '',
            quality: 'high',
            voices: ['ar-SA-Standard-A', 'ar-SA-Wavenet-A'],
            realTime: true
        },
        elevenlabs: {
            available: false,
            apiKey: '',
            quality: 'premium',
            voices: [],
            realTime: true
        },
        coqui: {
            available: false,
            endpoint: '',
            quality: 'high',
            voices: [],
            realTime: false
        },
        openrouter: {
            available: false,
            quality: 'high',
            voices: [],
            realTime: true
        }
    }
};

// حالة النظام الصوتي المتقدم
let voiceSystemState = {
    isListening: false,
    isSpeaking: false,
    isProcessing: false,
    lastTranscript: '',
    conversationActive: false,
    silenceTimer: null,
    speechBuffer: [],
    currentProvider: {
        stt: 'browser',
        tts: 'browser'
    },
    performance: {
        latency: 0,
        accuracy: 0,
        responseTime: 0
    }
};

// محرك الصوت المتقدم
let advancedVoiceEngine = null;

// فئة المساعد التقني
class TechnicalAssistant {
    constructor() {
        this.API_URL = "http://127.0.0.1:1234/v1/chat/completions";
        this.MODEL = "deepseek-coder-6.7b-instruct";
        console.log('🤖 تم إنشاء المساعد التقني');
    }

    // الحصول على رد من النموذج مع تحسينات متقدمة
    async getResponse(userMessage, context = '') {
        try {
            // التحقق من توفر OpenRouter أولاً
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter للرد');
                try {
                    const response = await window.openRouterIntegration.smartSendMessage(userMessage, {
                        temperature: 0.7,
                        maxTokens: 1000,
                        topP: 0.9
                    });

                    if (response && response.text) {
                        console.log('✅ تم استلام الرد من OpenRouter');
                        return response.text;
                    }
                } catch (openRouterError) {
                    console.warn('⚠️ خطأ في OpenRouter، التبديل للنظام المحلي:', openRouterError);
                }
            }

            // استخدام النظام المحلي كبديل
            console.log('🏠 استخدام النظام المحلي (LM Studio)');
            console.log('📤 محاولة الاتصال بـ LM Studio:', this.API_URL);
            console.log('📤 إرسال رسالة:', userMessage);

            // بناء prompt متقدم
            const systemPrompt = this.buildAdvancedSystemPrompt();
            const enhancedUserMessage = this.enhanceUserMessage(userMessage, context);

            // انتظار بدون timeout - حتى يرد النموذج مهما طال الوقت
            const response = await fetch(this.API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.MODEL,
                    messages: [
                        {
                            role: "system",
                            content: systemPrompt
                        },
                        {
                            role: "user",
                            content: enhancedUserMessage
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 500, // تقليل للسرعة
                    top_p: 0.8,
                    frequency_penalty: 0.0,
                    presence_penalty: 0.0,
                    stream: false
                })
                // بدون timeout - انتظار مفتوح حتى يرد النموذج
            });

            console.log('📡 حالة الاستجابة:', response.status);

            if (!response.ok) {
                throw new Error(`خطأ HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📥 تم استلام البيانات من LM Studio');

            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('تنسيق استجابة غير صحيح من LM Studio');
            }

            let aiResponse = data.choices[0].message.content;

            // تحسين الرد
            aiResponse = this.enhanceResponse(aiResponse);

            console.log('✅ تم استلام الرد المحسن من النموذج');
            return aiResponse;

        } catch (error) {
            console.error('❌ خطأ في الاتصال بـ LM Studio:', error);
            console.error('❌ نوع الخطأ:', error.name);
            console.error('❌ رسالة الخطأ:', error.message);

            // رسالة خطأ مفصلة حسب نوع الخطأ
            let errorMessage = `خطأ في الاتصال بالنموذج المحلي`;

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                errorMessage = `🔌 لا يمكن الاتصال بـ LM Studio

تأكد من:
- تشغيل LM Studio
- بدء الخادم على المنفذ 1234
- تحميل نموذج مناسب
- أن النموذج جاهز ومحمل بالكامل`;
            } else if (error.message.includes('NetworkError')) {
                errorMessage = `🌐 مشكلة في الشبكة

تحقق من:
- إعدادات الاتصال
- جدار الحماية
- إعدادات LM Studio
- أن المنفذ 1234 غير محجوب`;
            } else if (error.message.includes('JSON')) {
                errorMessage = `📄 خطأ في تحليل الاستجابة

قد يكون:
- النموذج أرجع استجابة غير صحيحة
- مشكلة في إعدادات LM Studio
- النموذج لم يكمل التحميل بعد`;
            } else {
                errorMessage = `❌ خطأ غير متوقع: ${error.message}

جرب:
- إعادة تشغيل LM Studio
- تحميل نموذج آخر
- التحقق من سجلات LM Studio`;
            }

            return errorMessage;
        }
    }

    // بناء prompt مبسط وسريع
    buildAdvancedSystemPrompt() {
        return `أنت مساعد تقني ذكي. اجب بالعربية بشكل مختصر ومفيد.`;
    }

    // تحسين رسالة المستخدم - مبسط للسرعة
    enhanceUserMessage(message, context) {
        // إرسال الرسالة كما هي للحصول على استجابة أسرع
        if (context) {
            return `${context}\n\n${message}`;
        }
        return message;
    }

    // تحسين الرد (مبسط للسرعة)
    enhanceResponse(response) {
        return response; // إرجاع الرد كما هو للسرعة
    }

    // فحص إذا كان السؤال متعلق بالبرمجة
    isCodeRelated(message) {
        const codeKeywords = ['كود', 'برمجة', 'function', 'javascript', 'python', 'html', 'css', 'react', 'api', 'database', 'sql'];
        return codeKeywords.some(keyword => message.toLowerCase().includes(keyword));
    }

    // فحص إذا كان السؤال حل مشكلة
    isProblemSolving(message) {
        const problemKeywords = ['مشكلة', 'خطأ', 'لا يعمل', 'error', 'bug', 'fix', 'حل', 'مساعدة'];
        return problemKeywords.some(keyword => message.toLowerCase().includes(keyword));
    }

    // فحص إذا كان سؤال تعليمي
    isLearningQuestion(message) {
        const learningKeywords = ['كيف أتعلم', 'علمني', 'اشرح لي', 'ما هو', 'كيف أبدأ', 'أريد أن أفهم', 'tutorial', 'learn'];
        return learningKeywords.some(keyword => message.toLowerCase().includes(keyword));
    }

    // رد محلي ذكي محسن
    getIntelligentLocalResponse(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا') || lowerMessage.includes('السلام')) {
            return `🤖 أهلاً وسهلاً! أنا المساعد التقني الذكي، خبيرك الشخصي في:

💻 البرمجة وتطوير التطبيقات
🌐 تقنيات الويب والذكاء الاصطناعي
🔧 حل المشاكل التقنية
📚 التعلم والتطوير

كيف يمكنني مساعدتك اليوم؟ اسألني عن أي شيء تقني!`;
        }

        if (this.isCodeRelated(message)) {
            return `💻 أرى أنك تسأل عن البرمجة! أنا خبير في جميع لغات البرمجة الحديثة.

أقدر أساعدك في البرمجة! يمكنني مساعدتك في:
- شرح المفاهيم البرمجية
- مراجعة الكود وإصلاح الأخطاء
- اقتراح أفضل الممارسات
- تقديم أمثلة عملية

تأكد من تشغيل النموذج المحلي للحصول على إجابات أكثر تفصيلاً!`;
        }

        if (lowerMessage.includes('مشكلة') || lowerMessage.includes('خطأ')) {
            return `🔧 أفهم أنك تواجه مشكلة تقنية. أنا هنا لمساعدتك!

للحصول على أفضل مساعدة:
1. وصف المشكلة بالتفصيل
2. ذكر رسائل الخطأ إن وجدت
3. شرح ما حاولت فعله
4. تحديد البيئة التقنية المستخدمة

تأكد من تشغيل النموذج المحلي للحصول على حلول متقدمة ومفصلة!`;
        }

        return `🤖 شكراً لسؤالك! أنا المساعد التقني الذكي وأحب مساعدتك.

أهلين وسهلين! أنا المساعد التقني الذكي وجاهز لمساعدتك.
تأكد من:
✅ تشغيل النموذج المحلي
✅ تحميل النموذج بشكل صحيح
✅ بدء الخادم

بعدها ستحصل على إجابات ذكية ومفصلة لجميع أسئلتك التقنية!`;
    }

    // رد محلي في حالة عدم توفر الاتصال
    getLocalResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {
            return 'أهلاً وسهلاً! أنا المساعد التقني الذكي. كيف يمكنني مساعدتك؟';
        }
        
        if (lowerMessage.includes('javascript')) {
            return 'JavaScript هي لغة برمجة قوية تُستخدم في تطوير الويب. هل تريد معرفة شيء محدد عنها؟';
        }
        
        if (lowerMessage.includes('python')) {
            return 'Python لغة برمجة سهلة التعلم ومتعددة الاستخدامات. ما الذي تريد معرفته عنها؟';
        }
        
        return 'شكراً لسؤالك! أنا هنا لمساعدتك في جميع الأسئلة التقنية. شنو تحتاج؟';
    }

    // فحص الاتصال
    async checkConnection() {
        try {
            const response = await fetch(this.API_URL.replace('/chat/completions', '/models'));
            return response.ok;
        } catch {
            return false;
        }
    }
}

// إنشاء مثيل المساعد
const technicalAssistant = new TechnicalAssistant();

// تشخيص حالة النموذج
async function diagnoseModelStatus() {
    console.log('🔍 تشخيص حالة النموذج...');

    const status = {
        modelLoaded: !!window.technicalAssistant,
        functionAvailable: typeof technicalAssistant?.getResponse === 'function',
        apiUrl: technicalAssistant?.API_URL,
        modelName: technicalAssistant?.MODEL
    };

    console.log('📊 حالة النموذج:', status);

    if (!status.modelLoaded) {
        console.error('❌ النموذج غير محمل');
        return false;
    }

    if (!status.functionAvailable) {
        console.error('❌ دالة getResponse غير متاحة');
        return false;
    }

    // اختبار الاتصال
    try {
        console.log('🔌 اختبار الاتصال بـ LM Studio...');
        const testResponse = await technicalAssistant.getResponse('مرحبا');

        if (testResponse && testResponse.trim().length > 0) {
            console.log('✅ النموذج يعمل بشكل صحيح');
            console.log('📥 رد الاختبار:', testResponse.substring(0, 50));
            return true;
        } else {
            console.error('❌ النموذج أرجع رد فارغ');
            return false;
        }
    } catch (error) {
        console.error('❌ فشل اختبار الاتصال:', error.message);
        return false;
    }
}




// متغيرات حالة الاتصال
let isConnectedToModel = false;
let connectionCheckInterval = null;
let lastConnectionCheck = 0;

// فحص الاتصال بالنموذج بشكل ذكي
async function checkModelConnection() {
    try {
        console.log('🔍 فحص الاتصال بالنموذج...');
        const response = await fetch('http://127.0.0.1:1234/v1/models', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            if (!isConnectedToModel) {
                isConnectedToModel = true;
                showConnectionStatus('متصل', true);
                console.log('✅ تم الاتصال بالنموذج بنجاح!');
            }
            return true;
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        if (isConnectedToModel) {
            isConnectedToModel = false;
            showConnectionStatus('غير متصل', false);
            console.log('❌ فقدان الاتصال بالنموذج');
        }
        return false;
    }
}

// عرض حالة الاتصال في الواجهة
function showConnectionStatus(status, isConnected) {
    // إزالة أي إشعار سابق
    const existingStatus = document.getElementById('connectionStatus');
    if (existingStatus) {
        existingStatus.remove();
    }

    // إنشاء إشعار حالة الاتصال
    const statusDiv = document.createElement('div');
    statusDiv.id = 'connectionStatus';
    statusDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 25px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
        ${isConnected ?
            'background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);' :
            'background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);'
        }
    `;

    statusDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <div style="width: 8px; height: 8px; border-radius: 50%; background: white; ${isConnected ? 'animation: pulse 2s infinite;' : ''}"></div>
            <span>${isConnected ? '🟢' : '🔴'} النموذج ${status}</span>
        </div>
    `;

    document.body.appendChild(statusDiv);

    // إخفاء الإشعار بعد 5 ثواني إذا كان متصل
    if (isConnected) {
        setTimeout(() => {
            if (statusDiv && statusDiv.parentNode) {
                statusDiv.style.opacity = '0';
                setTimeout(() => {
                    if (statusDiv && statusDiv.parentNode) {
                        statusDiv.remove();
                    }
                }, 300);
            }
        }, 5000);
    }
}

// بدء فحص الاتصال الدوري
function startConnectionMonitoring() {
    // فحص فوري
    checkModelConnection();

    // فحص كل 30 ثانية
    if (connectionCheckInterval) {
        clearInterval(connectionCheckInterval);
    }

    connectionCheckInterval = setInterval(() => {
        checkModelConnection();
    }, 30000);
}

// تحديث حالة الاتصال
function updateConnectionStatus(isConnected) {
    const statusText = isConnected ? 'متصل ومتاح' : 'غير متصل';
    showConnectionStatus(statusText, isConnected);

    // تحديث متغير الحالة
    isConnectedToModel = isConnected;
}

// إيقاف فحص الاتصال
function stopConnectionMonitoring() {
    if (connectionCheckInterval) {
        clearInterval(connectionCheckInterval);
        connectionCheckInterval = null;
    }
}

// ===== وظائف الواجهة =====

// إرسال رسالة للنموذج المحلي مباشرة
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // إضافة رسالة المستخدم
    addMessage('user', message);
    messageInput.value = '';

    // إظهار مؤشر الكتابة
    showAdvancedTyping();

    try {
        console.log('📝 معالجة الرسالة النصية:', message);

        // تشخيص حالة النموذج
        console.log('🔍 فحص حالة النموذج...');
        console.log('🤖 window.technicalAssistant موجود:', !!window.technicalAssistant);
        console.log('🔧 دالة getResponse متاحة:', typeof technicalAssistant?.getResponse);

        // 💬 الحصول على رد من النموذج مباشرة (مثل ChatGPT)
        const response = await getDirectModelResponse(message);

        console.log('✅ تم الحصول على رد من النموذج:', response ? 'نعم' : 'لا');

        // إخفاء مؤشر الكتابة
        hideTyping();

        // إضافة رد المساعد
        // النظام الموجود يتعامل مع الملفات تلقائياً

        addMessage('assistant', response);

    } catch (error) {
        console.error('❌ خطأ في معالجة الرسالة:', error);
        hideTyping();

        // رسالة خطأ مفصلة
        const errorMessage = `❌ **خطأ في المحادثة**\n\n` +
                           `**التفاصيل:** ${error.message}\n\n` +
                           `**الحلول:**\n` +
                           `• تأكد من تشغيل LM Studio\n` +
                           `• تحقق من تحميل النموذج\n` +
                           `• أعد تحميل الصفحة`;

        addMessage('assistant', errorMessage);
    }
}

// استخدام النظام الموجود للملفات

// استخدام النظام الموجود لإنشاء الملفات

// إنشاء محتوى PDF مخصص حسب الموضوع
function createTopicPDF(topic, content) {
    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.8; }
        .header { text-align: center; border-bottom: 3px solid #4CAF50; padding-bottom: 20px; margin-bottom: 30px; }
        .topic-title { font-size: 28px; color: #2E7D32; margin-bottom: 10px; }
        .content-section { margin: 25px 0; padding: 20px; background: #f9f9f9; border-right: 5px solid #4CAF50; }
        .qa-item { margin: 15px 0; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .question { font-weight: bold; color: #1976D2; margin-bottom: 10px; }
        .answer { color: #333; }
        .footer { margin-top: 50px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 20px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="topic-title">📋 ${topic}</div>
        <p>تم إنشاؤه بواسطة المساعد الذكي في ${new Date().toLocaleString('ar')}</p>
    </div>

    <div class="content-section">
        <h2>📝 المحتوى التفصيلي:</h2>
        ${content.split('\n\n').map(item => {
            if (item.startsWith('السؤال:')) {
                return `<div class="qa-item"><div class="question">❓ ${item.replace('السؤال:', '').trim()}</div>`;
            } else if (item.startsWith('الجواب:')) {
                return `<div class="answer">💡 ${item.replace('الجواب:', '').trim()}</div></div>`;
            }
            return `<p>${item}</p>`;
        }).join('')}
    </div>

    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد الذكي | ${new Date().toLocaleString('ar')}</p>
    </div>
</body>
</html>`;
}

// إنشاء محتوى Word مخصص حسب الموضوع
function createTopicWord(topic, content) {
    return `
<!DOCTYPE html>
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word">
<head>
    <meta charset="utf-8">
    <title>${topic} - المساعد الذكي</title>
    <style>
        body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.6; margin: 1in; }
        h1 { color: #2E74B5; font-size: 20pt; text-align: center; border-bottom: 2px solid #2E74B5; }
        h2 { color: #2E74B5; font-size: 16pt; margin-top: 30px; }
        .qa-section { margin: 20px 0; padding: 15px; background-color: #F8F9FA; border: 1px solid #DEE2E6; }
        .question { font-weight: bold; color: #495057; margin-bottom: 10px; }
        .answer { color: #212529; margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1>📋 ${topic}</h1>

    <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleString('ar')}</p>
    <p><strong>المنشئ:</strong> المساعد الذكي</p>

    <h2>📝 المحتوى التفصيلي</h2>

    ${content.split('\n\n').map(item => {
        if (item.startsWith('السؤال:')) {
            return `<div class="qa-section"><div class="question">❓ ${item.replace('السؤال:', '').trim()}</div>`;
        } else if (item.startsWith('الجواب:')) {
            return `<div class="answer">💡 ${item.replace('الجواب:', '').trim()}</div></div>`;
        }
        return `<p>${item}</p>`;
    }).join('')}

    <hr style="margin-top: 50px;">
    <p style="text-align: center; font-size: 10pt; color: #666;">
        تم إنشاؤه بواسطة المساعد الذكي | ${new Date().toLocaleString('ar')}
    </p>
</body>
</html>`;
}

// إنشاء محتوى Excel مخصص حسب الموضوع
function createTopicExcel(topic, content) {
    const items = content.split('\n\n');
    let rowIndex = 5;

    return `
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel">
<head>
    <meta charset="utf-8">
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #000; padding: 8px; text-align: right; }
        .header { background-color: #4CAF50; color: white; font-weight: bold; }
        .question { background-color: #E3F2FD; }
        .answer { background-color: #F3E5F5; }
    </style>
</head>
<body>
    <table>
        <tr class="header">
            <th colspan="3">📊 ${topic} - المساعد الذكي</th>
        </tr>
        <tr>
            <th>الرقم</th>
            <th>النوع</th>
            <th>المحتوى</th>
        </tr>
        <tr>
            <td>1</td>
            <td>معلومات</td>
            <td>تاريخ الإنشاء: ${new Date().toLocaleString('ar')}</td>
        </tr>
        <tr>
            <td>2</td>
            <td>معلومات</td>
            <td>الموضوع: ${topic}</td>
        </tr>
        ${items.map((item, index) => {
            if (item.startsWith('السؤال:')) {
                return `<tr class="question"><td>${index + 3}</td><td>سؤال</td><td>${item.replace('السؤال:', '').trim()}</td></tr>`;
            } else if (item.startsWith('الجواب:')) {
                return `<tr class="answer"><td>${index + 3}</td><td>جواب</td><td>${item.replace('الجواب:', '').trim()}</td></tr>`;
            }
            return `<tr><td>${index + 3}</td><td>محتوى</td><td>${item}</td></tr>`;
        }).join('')}
    </table>
</body>
</html>`;
}

// إنشاء محتوى PowerPoint مخصص حسب الموضوع
function createTopicPowerPoint(topic, content) {
    const items = content.split('\n\n');

    return `
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:p="urn:schemas-microsoft-com:office:powerpoint">
<head>
    <meta charset="utf-8">
    <style>
        .slide {
            width: 800px; height: 600px; margin: 20px auto; border: 2px solid #333;
            padding: 40px; page-break-after: always; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; display: flex; flex-direction: column; justify-content: center; text-align: center;
        }
        .slide h1 { font-size: 36px; margin-bottom: 20px; }
        .slide h2 { font-size: 28px; margin-bottom: 15px; }
        .slide p { font-size: 18px; line-height: 1.5; text-align: right; }
    </style>
</head>
<body>
    <div class="slide">
        <h1>🎯 ${topic}</h1>
        <h2>المساعد الذكي</h2>
        <p>تم إنشاؤه في: ${new Date().toLocaleString('ar')}</p>
    </div>

    ${items.map((item, index) => `
    <div class="slide">
        <h2>الشريحة ${index + 2}</h2>
        <p>${item}</p>
    </div>
    `).join('')}

    <div class="slide">
        <h2>🙏 شكراً لاستخدام المساعد الذكي</h2>
        <p>تم إنشاء هذا العرض عن "${topic}"</p>
        <p>${new Date().toLocaleString('ar')}</p>
    </div>
</body>
</html>`;
}

// إنشاء محتوى HTML مخصص حسب الموضوع
function createTopicHTML(topic, content) {
    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - المساعد الذكي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px;
               background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px;
                     box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        h1 { color: #2E7D32; text-align: center; border-bottom: 3px solid #4CAF50; padding-bottom: 15px; }
        .qa-item { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 10px;
                   border-right: 5px solid #4CAF50; }
        .question { font-weight: bold; color: #1976D2; margin-bottom: 10px; font-size: 18px; }
        .answer { color: #333; line-height: 1.6; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd;
                  color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 ${topic}</h1>
        <p style="text-align: center; color: #666;">تم إنشاؤه بواسطة المساعد الذكي في ${new Date().toLocaleString('ar')}</p>

        ${content.split('\n\n').map(item => {
            if (item.startsWith('السؤال:')) {
                return `<div class="qa-item"><div class="question">❓ ${item.replace('السؤال:', '').trim()}</div>`;
            } else if (item.startsWith('الجواب:')) {
                return `<div class="answer">💡 ${item.replace('الجواب:', '').trim()}</div></div>`;
            }
            return `<p>${item}</p>`;
        }).join('')}

        <div class="footer">
            <p>تم إنشاؤه بواسطة المساعد الذكي | ${new Date().toLocaleString('ar')}</p>
        </div>
    </div>
</body>
</html>`;
}

// 🧠 معالجة ذكية للطلبات مع التنفيذ التلقائي (مثل ChatGPT)
async function processIntelligentRequest(message) {
    console.log('🧠 تحليل ذكي للطلب:', message);

    try {
        // تحليل نوع الطلب أولاً
        const requestType = await analyzeRequestType(message);
        console.log('🎯 نوع الطلب المكتشف:', requestType);

        // تنفيذ الطلب حسب النوع
        switch (requestType.type) {
            case 'file_creation':
                return await handleFileCreationRequest(message, requestType);

            case 'web_search':
                return await handleWebSearchRequest(message, requestType);

            case 'code_generation':
                return await handleCodeGenerationRequest(message, requestType);

            case 'security_scan':
                return await handleSecurityScanRequest(message, requestType);

            case 'video_analysis':
                return await handleVideoAnalysisRequest(message, requestType);

            case 'screen_share':
                return await handleScreenShareRequest(message, requestType);

            case 'voice_conversation':
                return await handleVoiceConversationRequest(message, requestType);

            case 'summarization':
                return await handleSummarizationRequest(message, requestType);

            case 'ar_3d_modeling':
                return await handleAR3DRequest(message, requestType);

            case 'ai_improvement':
                return await handleAIImprovementRequest(message, requestType);

            case 'general_chat':
            default:
                return await getInstantResponse(message);
        }

    } catch (error) {
        console.error('❌ خطأ في المعالجة الذكية:', error);
        return await getInstantResponse(message);
    }
}

// 🔍 تحليل نوع الطلب بالذكاء الاصطناعي
async function analyzeRequestType(message) {
    const lowerMessage = message.toLowerCase();

    // تحليل سريع بالكلمات المفتاحية أولاً
    if (lowerMessage.includes('ملف') || lowerMessage.includes('أنشئ') || lowerMessage.includes('اكتب') ||
        lowerMessage.includes('pdf') || lowerMessage.includes('word') || lowerMessage.includes('excel')) {
        return { type: 'file_creation', confidence: 0.9, details: extractFileDetails(message) };
    }

    if (lowerMessage.includes('ابحث') || lowerMessage.includes('بحث') || lowerMessage.includes('search')) {
        return { type: 'web_search', confidence: 0.9, details: extractSearchQuery(message) };
    }

    if (lowerMessage.includes('كود') || lowerMessage.includes('برمجة') || lowerMessage.includes('code') ||
        lowerMessage.includes('function') || lowerMessage.includes('script')) {
        return { type: 'code_generation', confidence: 0.9, details: extractCodeDetails(message) };
    }

    if (lowerMessage.includes('افحص') || lowerMessage.includes('فحص') || lowerMessage.includes('أمان') ||
        lowerMessage.includes('ثغرة') || lowerMessage.includes('security') || lowerMessage.includes('scan')) {
        return { type: 'security_scan', confidence: 0.9, details: extractSecurityDetails(message) };
    }

    if (lowerMessage.includes('فيديو') || lowerMessage.includes('video') || lowerMessage.includes('تحليل فيديو')) {
        return { type: 'video_analysis', confidence: 0.8, details: {} };
    }

    if (lowerMessage.includes('شاشة') || lowerMessage.includes('screen') || lowerMessage.includes('مشاركة')) {
        return { type: 'screen_share', confidence: 0.8, details: {} };
    }

    if (lowerMessage.includes('صوت') || lowerMessage.includes('voice') || lowerMessage.includes('محادثة صوتية')) {
        return { type: 'voice_conversation', confidence: 0.8, details: {} };
    }

    if (lowerMessage.includes('ملخص') || lowerMessage.includes('لخص') || lowerMessage.includes('summary')) {
        return { type: 'summarization', confidence: 0.8, details: {} };
    }

    if (lowerMessage.includes('ثلاثي') || lowerMessage.includes('3d') || lowerMessage.includes('ar') ||
        lowerMessage.includes('نموذج')) {
        return { type: 'ar_3d_modeling', confidence: 0.7, details: {} };
    }

    if (lowerMessage.includes('حسن') || lowerMessage.includes('طور') || lowerMessage.includes('improve')) {
        return { type: 'ai_improvement', confidence: 0.7, details: {} };
    }

    // تحليل ذكي متقدم بالنماذج للحالات المعقدة
    return await performAdvancedAnalysis(message);
}

// 🔧 دوال مساعدة لاستخراج التفاصيل
function extractFileDetails(message) {
    const lowerMessage = message.toLowerCase();
    return {
        type: lowerMessage.includes('pdf') ? 'pdf' :
              lowerMessage.includes('word') ? 'word' :
              lowerMessage.includes('excel') ? 'excel' :
              lowerMessage.includes('powerpoint') ? 'powerpoint' : 'text',
        topic: message.replace(/أنشئ|ملف|اكتب|pdf|word|excel|powerpoint/gi, '').trim()
    };
}

function extractSearchQuery(message) {
    return {
        query: message.replace(/ابحث عن|ابحث|بحث/gi, '').trim()
    };
}

function extractCodeDetails(message) {
    const lowerMessage = message.toLowerCase();
    return {
        language: lowerMessage.includes('javascript') ? 'javascript' :
                 lowerMessage.includes('python') ? 'python' :
                 lowerMessage.includes('html') ? 'html' :
                 lowerMessage.includes('css') ? 'css' : 'general',
        description: message.replace(/كود|برمجة|code|function|script/gi, '').trim()
    };
}

function extractSecurityDetails(message) {
    return {
        target: message.match(/(https?:\/\/[^\s]+)/)?.[0] || '',
        type: message.toLowerCase().includes('شامل') ? 'comprehensive' : 'basic'
    };
}

// 🧠 تحليل متقدم بالذكاء الاصطناعي
async function performAdvancedAnalysis(message) {
    try {
        const analysisPrompt = `حلل الطلب التالي وحدد نوعه بدقة:

الطلب: "${message}"

حدد نوع الطلب من الخيارات التالية:
- file_creation: إنشاء ملفات (PDF, Word, Excel, PowerPoint, إلخ)
- web_search: البحث في الإنترنت
- code_generation: توليد أكواد برمجية
- security_scan: فحص أمني للمواقع
- video_analysis: تحليل فيديو
- screen_share: مشاركة الشاشة
- voice_conversation: محادثة صوتية
- summarization: تلخيص نصوص أو محادثات
- ar_3d_modeling: نمذجة ثلاثية الأبعاد
- ai_improvement: تحسين الكود
- general_chat: محادثة عامة

أجب بصيغة JSON فقط:
{"type": "نوع_الطلب", "confidence": 0.8, "details": {}}`;

        let analysisResult = '';

        // جرب OpenRouter أولاً
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            const response = await window.openRouterIntegration.smartSendMessage(analysisPrompt, {
                mode: 'analysis',
                temperature: 0.1,
                maxTokens: 200
            });
            if (response && response.text) {
                analysisResult = response.text;
            }
        }

        // تحليل النتيجة
        if (analysisResult) {
            try {
                const parsed = JSON.parse(analysisResult);
                return parsed;
            } catch (e) {
                console.log('فشل في تحليل JSON، استخدام التحليل الافتراضي');
            }
        }

        // تحليل افتراضي
        return { type: 'general_chat', confidence: 0.5, details: {} };

    } catch (error) {
        console.error('خطأ في التحليل المتقدم:', error);
        return { type: 'general_chat', confidence: 0.3, details: {} };
    }
}

// 🎯 دوال معالجة الطلبات المختلفة

// معالجة طلبات إنشاء الملفات
async function handleFileCreationRequest(message, requestType) {
    console.log('📁 معالجة طلب إنشاء ملف:', requestType);

    try {
        // تفعيل File Creator إذا لم يكن مفعلاً
        if (!window.fileCreatorInstance && window.FileCreatorCore) {
            window.fileCreatorInstance = new FileCreatorCore();
        }

        if (window.fileCreatorInstance) {
            const fileType = requestType.details.type;
            const topic = requestType.details.topic;

            // استدعاء الوظيفة المناسبة حسب نوع الملف
            switch (fileType) {
                case 'pdf':
                    return await window.fileCreatorInstance.generatePDF(topic);
                case 'word':
                    return await window.fileCreatorInstance.generateWord(topic);
                case 'excel':
                    return await window.fileCreatorInstance.generateExcel(topic);
                case 'powerpoint':
                    return await window.fileCreatorInstance.generatePowerPoint(topic);
                default:
                    return await window.fileCreatorInstance.generateTextFile(topic);
            }
        } else {
            return `📁 **إنشاء ملف ذكي**\n\nسأقوم بإنشاء ${requestType.details.type} عن "${requestType.details.topic}" باستخدام الذكاء الاصطناعي...\n\n⚠️ وحدة إنشاء الملفات غير متاحة حالياً.`;
        }
    } catch (error) {
        console.error('خطأ في إنشاء الملف:', error);
        return `❌ حدث خطأ في إنشاء الملف. يرجى المحاولة مرة أخرى.`;
    }
}

// معالجة طلبات البحث في الويب
async function handleWebSearchRequest(message, requestType) {
    console.log('🔍 معالجة طلب البحث:', requestType);

    const query = requestType.details.query;

    // تنفيذ البحث الفعلي
    const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;
    window.open(searchUrl, '_blank');

    // الحصول على اقتراحات ذكية من النماذج
    const searchPrompt = `المستخدم يريد البحث عن: "${query}"

قدم:
1. 🔍 كلمات مفتاحية أفضل للبحث
2. 🌐 مواقع متخصصة مقترحة
3. 💡 نصائح للبحث الفعال
4. 📚 مصادر إضافية مفيدة

كن مفيداً وعملياً:`;

    let searchAdvice = '';

    // جرب النماذج للحصول على نصائح البحث
    if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
        const response = await window.openRouterIntegration.smartSendMessage(searchPrompt, {
            mode: 'search_assistance',
            temperature: 0.6,
            maxTokens: 1500
        });
        if (response && response.text) {
            searchAdvice = response.text;
        }
    }

    return `🔍 **تم البحث عن: "${query}"**

🌐 **البحث مفتوح في تبويب جديد**

${searchAdvice ? `💡 **نصائح البحث الذكية:**\n${searchAdvice}` : ''}

🎯 **إجراءات إضافية:**
• "ابحث أكثر عن [موضوع محدد]" - بحث متخصص
• "اعرض مصادر أكاديمية" - مصادر علمية
• "ابحث في [موقع محدد]" - بحث موقع معين`;
}

// معالجة طلبات توليد الكود
async function handleCodeGenerationRequest(message, requestType) {
    console.log('💻 معالجة طلب توليد كود:', requestType);

    const codePrompt = `أنشئ كود ${requestType.details.language} للمهمة التالية:

المطلوب: ${requestType.details.description}

المواصفات:
1. 📝 كود نظيف ومنظم
2. 💬 تعليقات واضحة
3. 🛡️ أفضل الممارسات الأمنية
4. ⚡ محسن للأداء
5. 📚 أمثلة للاستخدام

قدم الكود مع شرح مفصل:`;

    let generatedCode = '';

    // جرب النماذج لتوليد الكود
    if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
        const response = await window.openRouterIntegration.smartSendMessage(codePrompt, {
            mode: 'code_generation',
            temperature: 0.3,
            maxTokens: 3000
        });
        if (response && response.text) {
            generatedCode = response.text;
        }
    } else if (window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
        const response = await window.huggingFaceManager.sendMessage(codePrompt);
        if (response && response.text) {
            generatedCode = response.text;
        }
    } else if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
        generatedCode = await technicalAssistant.getResponse(codePrompt);
    }

    return `💻 **كود ${requestType.details.language} مُولد بالذكاء الاصطناعي**

${generatedCode || 'عذراً، لا يمكن توليد الكود حالياً. تأكد من تفعيل أحد النماذج.'}

🔧 **إجراءات إضافية:**
• "حسن هذا الكود" - تحسين الكود
• "اشرح هذا الكود" - شرح مفصل
• "أضف ميزة [ميزة]" - إضافة وظائف`;
}

// معالجة طلبات الفحص الأمني
async function handleSecurityScanRequest(message, requestType) {
    console.log('🔒 معالجة طلب فحص أمني:', requestType);

    // تفعيل Bug Bounty Mode إذا لم يكن مفعلاً
    if (!window.bugBountyInstance && window.BugBountyCore) {
        window.bugBountyInstance = new BugBountyCore();
    }

    if (window.bugBountyInstance) {
        const target = requestType.details.target;
        const scanType = requestType.details.type;

        if (target) {
            // فحص موقع محدد
            if (scanType === 'comprehensive') {
                return await window.bugBountyInstance.performInteractiveComprehensiveScan(target, message);
            } else {
                return await window.bugBountyInstance.performDirectSecurityScan(target, message);
            }
        } else {
            // فحص تفاعلي عام
            return await startInteractiveScan(message);
        }
    } else {
        return `🔒 **فحص أمني ذكي**\n\nسأقوم بفحص أمني شامل للهدف المحدد...\n\n⚠️ وحدة Bug Bounty غير متاحة حالياً.\n\n💡 **يمكنك:**\n• تفعيل Bug Bounty Mode من الأزرار\n• إعادة المحاولة بعد التفعيل`;
    }
}

// معالجة طلبات تحليل الفيديو
async function handleVideoAnalysisRequest(message, requestType) {
    console.log('📹 معالجة طلب تحليل فيديو:', requestType);

    if (window.VideoAnalyzer) {
        const analyzer = new VideoAnalyzer();
        return `📹 **تحليل فيديو ذكي**\n\nتم تفعيل محلل الفيديو المتقدم!\n\n🎯 **الميزات المتاحة:**\n• تحليل المحتوى بالذكاء الاصطناعي\n• استخراج النص وتحسينه\n• إنشاء ملخصات ذكية\n• تحليل الجودة والمحتوى\n\n📤 **ارفع فيديو للبدء في التحليل**`;
    } else {
        return `📹 **تحليل فيديو ذكي**\n\nسأقوم بتحليل الفيديو باستخدام الذكاء الاصطناعي...\n\n⚠️ محلل الفيديو غير متاح حالياً.\n\n💡 **يمكنك:**\n• رفع فيديو من الأزرار\n• استخدام رابط يوتيوب`;
    }
}

// معالجة طلبات مشاركة الشاشة
async function handleScreenShareRequest(message, requestType) {
    console.log('🖥️ معالجة طلب مشاركة الشاشة:', requestType);

    if (window.ScreenShareManager) {
        const screenManager = new ScreenShareManager();
        return `🖥️ **مشاركة الشاشة الذكية**\n\nتم تفعيل مدير مشاركة الشاشة!\n\n🎯 **الميزات المتاحة:**\n• تحليل محتوى الشاشة بالذكاء الاصطناعي\n• فحص أمني مباشر للمواقع\n• تحليل الكود المعروض\n• اقتراحات ذكية\n\n📺 **ابدأ مشاركة الشاشة للتحليل الذكي**`;
    } else {
        return `🖥️ **مشاركة الشاشة الذكية**\n\nسأقوم بتحليل محتوى الشاشة بالذكاء الاصطناعي...\n\n⚠️ مدير مشاركة الشاشة غير متاح حالياً.\n\n💡 **يمكنك:**\n• تفعيل مشاركة الشاشة من الأزرار\n• وصف ما تريد تحليله`;
    }
}

// معالجة طلبات المحادثة الصوتية
async function handleVoiceConversationRequest(message, requestType) {
    console.log('🎤 معالجة طلب محادثة صوتية:', requestType);

    // تفعيل الصوت إذا لم يكن مفعلاً
    if (!window.voiceEnabled) {
        enableVoice();
    }

    // تفعيل المحادثة المستمرة
    if (typeof startContinuousListening === 'function') {
        startContinuousListening();
    }

    return `🎤 **محادثة صوتية ذكية**\n\nتم تفعيل النظام الصوتي المتقدم!\n\n🗣️ **الميزات المتاحة:**\n• محادثة طبيعية مستمرة\n• ردود ذكية من النماذج\n• تحسين النص للكلام\n• دعم اللهجة العراقية والفصحى\n\n🎯 **ابدأ الحديث الآن!**\n\n💡 قل أي شيء وسأرد عليك صوتياً`;
}

// معالجة طلبات التلخيص
async function handleSummarizationRequest(message, requestType) {
    console.log('📝 معالجة طلب تلخيص:', requestType);

    if (window.SmartSummarizer) {
        const summarizer = new SmartSummarizer();

        // تحديد نوع التلخيص
        if (message.toLowerCase().includes('محادثة')) {
            return await summarizer.generateConversationSummary('detailed');
        } else {
            return `📝 **تلخيص ذكي**\n\nتم تفعيل الملخص الذكي!\n\n🎯 **يمكنني تلخيص:**\n• المحادثة الحالية\n• نصوص ومستندات\n• مقالات ومحتوى\n• فيديوهات ومواد تعليمية\n\n💡 **أرسل النص أو اطلب تلخيص المحادثة**`;
        }
    } else {
        return `📝 **تلخيص ذكي**\n\nسأقوم بتلخيص المحتوى بالذكاء الاصطناعي...\n\n⚠️ وحدة التلخيص غير متاحة حالياً.\n\n💡 **يمكنك:**\n• إرسال النص المراد تلخيصه\n• طلب تلخيص المحادثة الحالية`;
    }
}

// معالجة طلبات النمذجة ثلاثية الأبعاد
async function handleAR3DRequest(message, requestType) {
    console.log('🎮 معالجة طلب نمذجة ثلاثية الأبعاد:', requestType);

    if (window.ARRenderer) {
        const arRenderer = new ARRenderer();
        return `🎮 **نمذجة ثلاثية الأبعاد ذكية**\n\nتم تفعيل محرك العرض ثلاثي الأبعاد!\n\n🎯 **الميزات المتاحة:**\n• تصميم نماذج بالذكاء الاصطناعي\n• تحليل وتحسين النماذج\n• عرض تفاعلي ثلاثي الأبعاد\n• دعم AR والواقع المعزز\n\n🎨 **صف النموذج الذي تريد إنشاءه**`;
    } else {
        return `🎮 **نمذجة ثلاثية الأبعاد ذكية**\n\nسأقوم بتصميم نموذج ثلاثي الأبعاد بالذكاء الاصطناعي...\n\n⚠️ محرك العرض ثلاثي الأبعاد غير متاح حالياً.\n\n💡 **يمكنك:**\n• وصف النموذج المطلوب\n• تحديد الأبعاد والألوان`;
    }
}

// معالجة طلبات التحسين بالذكاء الاصطناعي
async function handleAIImprovementRequest(message, requestType) {
    console.log('🤖 معالجة طلب تحسين بالذكاء الاصطناعي:', requestType);

    if (window.aiSelfImprove) {
        return `🤖 **تحسين ذكي للكود**\n\nتم تفعيل نظام التحسين الذاتي!\n\n🎯 **الميزات المتاحة:**\n• تحليل الكود وإيجاد التحسينات\n• اقتراحات أمنية وأداء\n• تطبيق أفضل الممارسات\n• تحسين تلقائي بالذكاء الاصطناعي\n\n💻 **أرسل الكود المراد تحسينه**`;
    } else {
        return `🤖 **تحسين ذكي للكود**\n\nسأقوم بتحليل وتحسين الكود بالذكاء الاصطناعي...\n\n⚠️ نظام التحسين الذاتي غير متاح حالياً.\n\n💡 **يمكنك:**\n• إرسال الكود المراد تحسينه\n• وصف المشكلة أو التحسين المطلوب`;
    }
}

// 🧠 الحصول على رد مباشر من النموذج مع السياق (مثل ChatGPT)
async function getDirectModelResponse(message) {
    console.log('💬 الحصول على رد مباشر من النموذج:', message);

    try {
        let response = '';

        // إضافة السياق للرسالة
        const context = buildConversationContext();
        let contextualMessage = message;

        if (context && context.trim()) {
            contextualMessage = `${context}\n\nالطلب الحالي: ${message}`;
            console.log('📝 تم إضافة السياق للرسالة');
        }

        // أولاً: جرب OpenRouter مع السياق
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            console.log('🔗 استخدام OpenRouter للرد المباشر مع السياق...');
            const openRouterResponse = await window.openRouterIntegration.smartSendMessage(contextualMessage, {
                mode: 'conversation',
                temperature: 0.7,
                maxTokens: 2000
            });
            if (openRouterResponse && openRouterResponse.text) {
                response = openRouterResponse.text;
            }
        }

        // ثانياً: جرب Hugging Face مع السياق
        if (!response && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
            console.log('🤗 استخدام Hugging Face للرد المباشر مع السياق...');
            const hfResponse = await window.huggingFaceManager.sendMessage(contextualMessage);
            if (hfResponse && hfResponse.text) {
                response = hfResponse.text;
            }
        }

        // ثالثاً: جرب النموذج المحلي مع السياق
        if (!response && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
            console.log('🤖 استخدام النموذج المحلي للرد المباشر مع السياق...');
            response = await technicalAssistant.getResponse(contextualMessage);
        }

        // رابعاً: رد ذكي محلي مع السياق
        if (!response) {
            console.log('💡 استخدام الرد الذكي المحلي مع السياق...');
            response = await getQuickResponse(contextualMessage);
        }

        // 🔍 فحص ما إذا كان الطلب يتطلب تنفيذ تقنية معينة
        if (response && shouldExecuteSpecialFunction(response, message)) {
            console.log('⚡ الطلب يتطلب تنفيذ تقنية خاصة...');
            const specialResponse = await executeSpecialFunction(message, response);
            if (specialResponse) {
                response = specialResponse;
            }
        }

        return response;

    } catch (error) {
        console.error('❌ خطأ في الحصول على رد مباشر:', error);
        return await getQuickResponse(message);
    }
}

// 🔍 فحص ما إذا كان الرد يتطلب تنفيذ تقنية معينة
function shouldExecuteSpecialFunction(response, message) {
    const lowerResponse = response.toLowerCase();
    const lowerMessage = message.toLowerCase();

    // فحص الكلمات المفتاحية للتقنيات في الرسالة الأصلية
    const specialKeywords = [
        // إنشاء الملفات
        'أنشئ ملف', 'اكتب ملف', 'ملف pdf', 'ملف word', 'ملف excel', 'ملف powerpoint',
        'create file', 'make file', 'generate file',

        // البحث
        'ابحث عن', 'بحث في', 'ابحث لي', 'search for', 'find me', 'look for',

        // الفحص الأمني
        'افحص موقع', 'فحص أمني', 'فحص شامل', 'scan website', 'security scan', 'check security',

        // مشاركة الشاشة
        'شارك الشاشة', 'screen share', 'مشاركة الشاشة', 'share screen',

        // تحليل الفيديو
        'حلل فيديو', 'video analysis', 'تحليل فيديو', 'analyze video',

        // التلخيص
        'لخص', 'ملخص', 'summarize', 'summary',

        // النمذجة ثلاثية الأبعاد
        'نموذج ثلاثي', '3d model', 'ar model', 'ثلاثي الأبعاد',

        // تحسين الكود
        'حسن الكود', 'improve code', 'تحسين الكود', 'optimize code',

        // توليد الكود
        'اكتب كود', 'أنشئ كود', 'كود لـ', 'write code', 'create code', 'generate code'
    ];

    // فحص النص المدخل أولاً (الأولوية)
    for (const keyword of specialKeywords) {
        if (lowerMessage.includes(keyword)) {
            console.log(`🎯 تم اكتشاف طلب تقنية: ${keyword}`);
            return true;
        }
    }

    return false;
}

// ⚡ تنفيذ التقنية المطلوبة
async function executeSpecialFunction(message, modelResponse) {
    console.log('⚡ تنفيذ تقنية خاصة بناءً على الطلب...');

    try {
        // تحليل نوع التقنية المطلوبة
        const requestType = await analyzeRequestType(message);
        console.log('🎯 نوع التقنية المكتشفة:', requestType.type);

        // تنفيذ التقنية المناسبة
        switch (requestType.type) {
            case 'file_creation':
                const fileResult = await handleFileCreationRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم تنفيذ الطلب:**\n${fileResult}`;

            case 'web_search':
                const searchResult = await handleWebSearchRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم تنفيذ البحث:**\n${searchResult}`;

            case 'code_generation':
                const codeResult = await handleCodeGenerationRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم توليد الكود:**\n${codeResult}`;

            case 'security_scan':
                const scanResult = await handleSecurityScanRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم الفحص الأمني:**\n${scanResult}`;

            case 'video_analysis':
                const videoResult = await handleVideoAnalysisRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل تحليل الفيديو:**\n${videoResult}`;

            case 'screen_share':
                const screenResult = await handleScreenShareRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل مشاركة الشاشة:**\n${screenResult}`;

            case 'summarization':
                const summaryResult = await handleSummarizationRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم التلخيص:**\n${summaryResult}`;

            case 'ar_3d_modeling':
                const arResult = await handleAR3DRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل النمذجة ثلاثية الأبعاد:**\n${arResult}`;

            case 'ai_improvement':
                const improveResult = await handleAIImprovementRequest(message, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل التحسين الذكي:**\n${improveResult}`;

            default:
                return modelResponse; // إرجاع الرد الأصلي إذا لم تكن هناك تقنية محددة
        }

    } catch (error) {
        console.error('❌ خطأ في تنفيذ التقنية الخاصة:', error);
        return modelResponse; // إرجاع الرد الأصلي في حالة الخطأ
    }
}

// 🧠 الحصول على رد مباشر من النموذج (مثل ChatGPT)
async function getDirectModelResponse(transcript) {
    console.log('💬 الحصول على رد مباشر من النموذج:', transcript);

    try {
        let response = '';

        // أولاً: جرب OpenRouter
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            console.log('🔗 استخدام OpenRouter للرد المباشر...');
            const openRouterResponse = await window.openRouterIntegration.smartSendMessage(transcript, {
                mode: 'conversation',
                temperature: 0.7,
                maxTokens: 2000
            });
            if (openRouterResponse && openRouterResponse.text) {
                response = openRouterResponse.text;
            }
        }

        // ثانياً: جرب Hugging Face
        if (!response && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
            console.log('🤗 استخدام Hugging Face للرد المباشر...');
            const hfResponse = await window.huggingFaceManager.sendMessage(transcript);
            if (hfResponse && hfResponse.text) {
                response = hfResponse.text;
            }
        }

        // ثالثاً: جرب النموذج المحلي
        if (!response && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
            console.log('🤖 استخدام النموذج المحلي للرد المباشر...');
            response = await technicalAssistant.getResponse(transcript);
        }

        // رابعاً: رد ذكي محلي
        if (!response) {
            console.log('💡 استخدام الرد الذكي المحلي...');
            response = await getQuickResponse(transcript);
        }

        return response;

    } catch (error) {
        console.error('❌ خطأ في الحصول على رد مباشر:', error);
        return await getQuickResponse(transcript);
    }
}

// 🔍 فحص ما إذا كان الرد يتطلب تنفيذ تقنية معينة
function shouldExecuteSpecialFunction(response, transcript) {
    const lowerResponse = response.toLowerCase();
    const lowerTranscript = transcript.toLowerCase();

    // فحص الكلمات المفتاحية للتقنيات
    const specialKeywords = [
        'أنشئ ملف', 'اكتب ملف', 'ملف pdf', 'ملف word',
        'ابحث عن', 'بحث في', 'search for',
        'افحص موقع', 'فحص أمني', 'scan website',
        'شارك الشاشة', 'screen share', 'مشاركة الشاشة',
        'حلل فيديو', 'video analysis', 'تحليل فيديو',
        'لخص', 'ملخص', 'summarize',
        'نموذج ثلاثي', '3d model', 'ar model',
        'حسن الكود', 'improve code', 'تحسين الكود'
    ];

    // فحص النص المدخل أولاً
    for (const keyword of specialKeywords) {
        if (lowerTranscript.includes(keyword)) {
            return true;
        }
    }

    // فحص رد النموذج إذا اقترح استخدام تقنية
    for (const keyword of specialKeywords) {
        if (lowerResponse.includes(keyword)) {
            return true;
        }
    }

    return false;
}

// ⚡ تنفيذ التقنية المطلوبة
async function executeSpecialFunction(transcript, modelResponse) {
    console.log('⚡ تنفيذ تقنية خاصة بناءً على الطلب...');

    try {
        // تحليل نوع التقنية المطلوبة
        const requestType = await analyzeRequestType(transcript);

        // تنفيذ التقنية المناسبة
        switch (requestType.type) {
            case 'file_creation':
                const fileResult = await handleFileCreationRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم تنفيذ الطلب:**\n${fileResult}`;

            case 'web_search':
                const searchResult = await handleWebSearchRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم تنفيذ البحث:**\n${searchResult}`;

            case 'code_generation':
                const codeResult = await handleCodeGenerationRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم توليد الكود:**\n${codeResult}`;

            case 'security_scan':
                const scanResult = await handleSecurityScanRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم الفحص الأمني:**\n${scanResult}`;

            case 'video_analysis':
                const videoResult = await handleVideoAnalysisRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل تحليل الفيديو:**\n${videoResult}`;

            case 'screen_share':
                const screenResult = await handleScreenShareRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل مشاركة الشاشة:**\n${screenResult}`;

            case 'summarization':
                const summaryResult = await handleSummarizationRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم التلخيص:**\n${summaryResult}`;

            case 'ar_3d_modeling':
                const arResult = await handleAR3DRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل النمذجة ثلاثية الأبعاد:**\n${arResult}`;

            case 'ai_improvement':
                const improveResult = await handleAIImprovementRequest(transcript, requestType);
                return `${modelResponse}\n\n🎯 **تم تفعيل التحسين الذكي:**\n${improveResult}`;

            default:
                return modelResponse; // إرجاع الرد الأصلي إذا لم تكن هناك تقنية محددة
        }

    } catch (error) {
        console.error('❌ خطأ في تنفيذ التقنية الخاصة:', error);
        return modelResponse; // إرجاع الرد الأصلي في حالة الخطأ
    }
}

// معالجة أوامر المستخدم - المساعد ينفذ بمساعدة النموذج كقاعدة بيانات
async function processUserCommand(message) {
    const lowerMessage = message.toLowerCase();

    // أوامر فتح المواقع - المساعد ينفذ + النموذج يوفر المعلومات
    if (lowerMessage.includes('افتح') || lowerMessage.includes('اذهب إلى')) {
        const urlMatch = message.match(/(https?:\/\/[^\s]+)/);
        if (urlMatch) {
            // المساعد ينفذ الفعل
            window.open(urlMatch[0], '_blank');

            // النموذج يوفر معلومات إضافية عن الموقع
            const siteInfo = await getAIKnowledge(`أخبرني معلومات مفيدة عن موقع ${urlMatch[0]} - ما هو؟ ما الذي يمكن فعله فيه؟`);

            return {
                executed: true,
                result: `✅ تم فتح الموقع: ${urlMatch[0]}

📋 **معلومات الموقع:**
${siteInfo}

🌐 **الموقع مفتوح الآن في تبويب جديد**`
            };
        }
    }

    // أوامر البحث - المساعد ينفذ + النموذج يقترح كلمات مفتاحية
    if (lowerMessage.includes('ابحث عن') || lowerMessage.includes('بحث')) {
        const searchQuery = message.replace(/ابحث عن|بحث/gi, '').trim();
        if (searchQuery) {
            // النموذج يقترح كلمات مفتاحية أفضل
            const enhancedQuery = await getAIKnowledge(`اقترح أفضل كلمات مفتاحية للبحث عن: ${searchQuery}`);

            // المساعد ينفذ البحث
            const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;
            window.open(searchUrl, '_blank');

            return {
                executed: true,
                result: `🔍 **تم البحث عن: "${searchQuery}"**

💡 **اقتراحات للبحث الأفضل:**
${enhancedQuery}

🌐 **نتائج البحث مفتوحة في تبويب جديد**`
            };
        }
    }

    // أوامر Bug Bounty - المساعد ينفذ + النموذج يوفر الخبرة الأمنية
    if (window.bugBountyInstance && window.bugBountyInstance.isActive) {
        if (lowerMessage.includes('افحص') || lowerMessage.includes('scan')) {
            const urlMatch = message.match(/(https?:\/\/[^\s]+)/);
            if (urlMatch) {
                // المساعد ينفذ الفحص الحقيقي الفعلي
                const realScanResults = await window.bugBountyInstance.performDirectSecurityScan(urlMatch[0], message);

                // تفعيل التفاعل الصوتي إذا كان متاحاً
                if (window.voiceEnabled && typeof speakText === 'function') {
                    speakText('تم إكمال الفحص الأمني الحقيقي. تم اكتشاف عدة نقاط مهمة. هل تريد التفاصيل؟');
                }

                return {
                    executed: true,
                    result: realScanResults
                };
            }
        }

        // أوامر الفحص التفاعلي الشامل
        if (lowerMessage.includes('فحص تفاعلي') || lowerMessage.includes('فحص معي') || lowerMessage.includes('فحص شامل')) {
            const urlMatch = message.match(/(https?:\/\/[^\s]+)/);
            if (urlMatch) {
                const result = await window.bugBountyInstance.performInteractiveComprehensiveScan(urlMatch[0], message);
                return {
                    executed: true,
                    result: result
                };
            } else {
                return {
                    executed: true,
                    result: await startInteractiveScan(message)
                };
            }
        }

        // أوامر الأسئلة التفاعلية والتعليمية
        if (lowerMessage.includes('كيف وجدت') || lowerMessage.includes('اشرح لي') ||
            lowerMessage.includes('كيف أستغل') || lowerMessage.includes('ما التالي') ||
            lowerMessage.includes('وضح أكثر') || lowerMessage.includes('أعط مثال') ||
            lowerMessage.includes('علمني') || lowerMessage.includes('كيف أجد') ||
            lowerMessage.includes('كيف أكتشف') || lowerMessage.includes('طريقة الفحص')) {

            if (window.bugBountyInstance && window.bugBountyInstance.currentScan) {
                const result = await window.bugBountyInstance.handleInteractiveQuestion(message, window.bugBountyInstance.currentScan);
                return {
                    executed: true,
                    result: result
                };
            } else {
                // حتى لو لم يكن هناك فحص نشط، المساعد يجيب
                const result = await handleEducationalQuestion(message);
                return {
                    executed: true,
                    result: result
                };
            }
        }

        // أوامر البحث في الإنترنت للمصادر التعليمية
        if (lowerMessage.includes('ابحث لي') || lowerMessage.includes('ابحث عن') ||
            lowerMessage.includes('اعرض لي المصادر') || lowerMessage.includes('مصادر تعليمية') ||
            lowerMessage.includes('مراجع') || lowerMessage.includes('دروس')) {

            const result = await handleEducationalSearch(message);
            return {
                executed: true,
                result: result
            };
        }

        // أوامر التفاعل مع مشاركة الشاشة
        if (lowerMessage.includes('شاشة') || lowerMessage.includes('screen')) {
            if (lowerMessage.includes('افحص') || lowerMessage.includes('تحليل')) {
                return {
                    executed: true,
                    result: await handleScreenScanRequest(message)
                };
            }
        }
    }

    // أوامر إنشاء الملفات - المساعد ينفذ + النموذج يولد المحتوى
    if (lowerMessage.includes('أنشئ ملف') || lowerMessage.includes('اكتب ملف')) {
        const fileType = extractFileType(message);
        const content = await getAIKnowledge(`أنشئ محتوى ${fileType} احترافي حسب الطلب: ${message}`);

        // المساعد ينشئ الملف
        const fileName = `generated_${Date.now()}.${fileType}`;
        downloadFile(content, fileName);

        return {
            executed: true,
            result: `📁 **تم إنشاء الملف: ${fileName}**

${content.substring(0, 500)}...

✅ **تم تحميل الملف بواسطة المساعد**
🧠 **تم توليد المحتوى بواسطة قاعدة المعرفة**`
        };
    }

    // لم ينفذ المساعد أي شيء محدد
    return {
        executed: false,
        result: null
    };
}

// الحصول على المعرفة من النموذج كقاعدة بيانات
async function getAIKnowledge(query) {
    try {
        // استخدام OpenRouter كقاعدة معرفة أولاً
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            const response = await window.openRouterIntegration.smartSendMessage(query, {
                temperature: 0.7,
                maxTokens: 1000
            });
            return response.text;
        }

        // استخدام النموذج المحلي كقاعدة معرفة
        if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
            return await technicalAssistant.getResponse(query);
        }

        // رد احتياطي
        return 'المعلومات غير متاحة حالياً من قاعدة المعرفة.';

    } catch (error) {
        console.error('خطأ في الوصول لقاعدة المعرفة:', error);
        return 'حدث خطأ في الوصول لقاعدة المعرفة.';
    }
}

// استخراج نوع الملف من الرسالة
function extractFileType(message) {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('pdf')) return 'pdf';
    if (lowerMessage.includes('word') || lowerMessage.includes('doc')) return 'docx';
    if (lowerMessage.includes('excel') || lowerMessage.includes('جدول')) return 'xlsx';
    if (lowerMessage.includes('powerpoint') || lowerMessage.includes('عرض')) return 'pptx';
    if (lowerMessage.includes('html') || lowerMessage.includes('موقع')) return 'html';
    if (lowerMessage.includes('css')) return 'css';
    if (lowerMessage.includes('javascript') || lowerMessage.includes('js')) return 'js';
    if (lowerMessage.includes('python') || lowerMessage.includes('py')) return 'py';

    return 'txt'; // افتراضي
}

// تحميل ملف
function downloadFile(content, fileName) {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// معالجة طلب فحص الشاشة
async function handleScreenScanRequest(message) {
    return `🖥️ **فحص الشاشة التفاعلي**

أرى أنك تريد فحص شيء على الشاشة!

💡 **كيف يمكنني مساعدتك:**

🔍 **للفحص الأمني:**
• قل "افحص الموقع المفتوح"
• أو "افحص [URL]" مع رابط الموقع
• أو "فحص أمني للصفحة الحالية"

🎯 **للتحليل التفاعلي:**
• "حلل الكود المعروض"
• "اشرح التقنيات المستخدمة"
• "ابحث عن ثغرات في هذا الموقع"

🗣️ **للتفاعل الصوتي:**
• فعل الميكروفون وقل ما تريد فحصه
• سأرشدك خطوة بخطوة في الفحص

**مثال:** "افحص موقع facebook.com" أو "حلل الكود في الشاشة"`;
}

// بدء فحص تفاعلي
async function startInteractiveScan(message) {
    // تفعيل وضع Bug Bounty إذا لم يكن مفعلاً
    if (window.bugBountyInstance && !window.bugBountyInstance.isActive) {
        window.bugBountyInstance.isActive = true;
        updateBugBountyUI();
    }

    // تفعيل الصوت للتفاعل
    if (typeof enableVoice === 'function') {
        enableVoice();
    }

    return `🎯 **تم تفعيل الفحص التفاعلي!**

🔥 **أنا الآن جاهز للفحص معك خطوة بخطوة:**

🗣️ **التفاعل الصوتي مُفعل:**
• تحدث معي مباشرة
• سأرشدك في كل خطوة
• سأشرح لك ما أجده

🔍 **أوامر الفحص التفاعلي:**
• "ابدأ فحص [URL]" - بدء فحص شامل
• "اشرح لي هذه الثغرة" - شرح تفصيلي
• "كيف أستغل هذا؟" - خطوات الاستغلال
• "ما التالي؟" - الخطوة التالية

💡 **نصائح للفحص التفاعلي:**
• شارك شاشتك إذا أردت
• اسألني عن أي شيء لا تفهمه
• سأعلمك تقنيات Bug Bounty المتقدمة

**قل لي: ما الموقع الذي تريد فحصه؟** 🎤`;
}

// معالجة الأسئلة التعليمية
async function handleEducationalQuestion(message) {
    console.log(`🎓 سؤال تعليمي: ${message}`);

    // النموذج كقاعدة معرفة تعليمية
    const educationalQuery = `أنت قاعدة معرفة تعليمية متخصصة في الأمان السيبراني وBug Bounty. المستخدم يسأل: "${message}"

كقاعدة معرفة تعليمية، قدم إجابة شاملة ومفصلة:

🎓 **تعليمية ومفصلة:**
- اشرح المفهوم بطريقة واضحة
- ابدأ من الأساسيات
- تدرج في التعقيد
- أعط أمثلة عملية

🛠️ **عملية وتطبيقية:**
- خطوات واضحة للتطبيق
- أدوات محددة للاستخدام
- تقنيات متقدمة
- نصائح الخبراء

🔍 **شاملة ومتقدمة:**
- جميع جوانب الموضوع
- تقنيات متقدمة
- حالات خاصة
- أخطاء شائعة وكيفية تجنبها

💡 **تفاعلية ومحفزة:**
- اقترح خطوات تالية للتعلم
- مصادر إضافية للتعمق
- تمارين عملية
- مشاريع تطبيقية

🎯 **متخصصة في Bug Bounty:**
- منظور Bug Bounty
- قيمة الثغرات
- كتابة التقارير
- نصائح لزيادة المكافآت

كن مفصلاً جداً ومفيداً وتعليمياً!`;

    try {
        let educationalResponse = '';
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            const response = await window.openRouterIntegration.smartSendMessage(educationalQuery, {
                mode: 'bug_bounty',
                maxTokens: 3000
            });
            educationalResponse = response.text;
        } else if (typeof technicalAssistant !== 'undefined') {
            educationalResponse = await technicalAssistant.getResponse(educationalQuery);
        } else {
            educationalResponse = generateBasicEducationalResponse(message);
        }

        // تفعيل الرد الصوتي
        if (window.voiceEnabled && typeof speakText === 'function') {
            const shortResponse = educationalResponse.substring(0, 200) + '...';
            speakText(shortResponse);
        }

        return `🎓 **إجابة تعليمية شاملة:**

${educationalResponse}

💡 **هل تريد المزيد؟**
• "ابحث لي عن مصادر تعليمية" - مصادر إضافية
• "أعط مثال عملي" - مثال تطبيقي
• "كيف أتدرب على هذا؟" - تمارين عملية
• "ما الأدوات المطلوبة؟" - أدوات متخصصة`;

    } catch (error) {
        return `🎓 **إجابة تعليمية أساسية:**

${generateBasicEducationalResponse(message)}

⚠️ تم استخدام النظام الأساسي للإجابة.`;
    }
}

// معالجة البحث التعليمي في الإنترنت
async function handleEducationalSearch(message) {
    console.log(`🌐 بحث تعليمي: ${message}`);

    // استخراج موضوع البحث
    const searchTopic = extractSearchTopic(message);

    // النموذج يقترح مصادر ومواقع تعليمية
    const searchQuery = `أنت قاعدة معرفة للمصادر التعليمية في الأمان السيبراني. المستخدم يريد البحث عن: "${searchTopic}"

اقترح أفضل المصادر التعليمية والمواقع للتعلم:

🌐 **مواقع تعليمية متخصصة:**
- مواقع Bug Bounty platforms
- مواقع تعليمية أكاديمية
- منصات التدريب العملي
- مجتمعات المتخصصين

📚 **مصادر التعلم:**
- كتب متخصصة
- دورات تدريبية
- فيديوهات تعليمية
- مقالات متقدمة

🛠️ **أدوات ومختبرات:**
- مختبرات افتراضية
- أدوات الفحص
- بيئات التدريب
- تطبيقات عملية

💡 **مجتمعات وموارد:**
- منتديات متخصصة
- قنوات يوتيوب
- حسابات تويتر
- مدونات الخبراء

قدم قائمة شاملة بالمصادر مع روابط محددة!`;

    try {
        let searchSuggestions = '';
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            const response = await window.openRouterIntegration.smartSendMessage(searchQuery, {
                mode: 'bug_bounty',
                maxTokens: 2000
            });
            searchSuggestions = response.text;
        } else if (typeof technicalAssistant !== 'undefined') {
            searchSuggestions = await technicalAssistant.getResponse(searchQuery);
        } else {
            searchSuggestions = generateBasicSearchSuggestions(searchTopic);
        }

        // المساعد ينفذ البحث الفعلي
        const searchResults = await performActualSearch(searchTopic);

        return `🌐 **بحث تعليمي عن: ${searchTopic}**

🧠 **اقتراحات من قاعدة المعرفة:**
${searchSuggestions}

🔍 **نتائج البحث المباشر:**
${searchResults}

💡 **إجراءات متاحة:**
• "افتح الموقع الأول" - فتح أفضل مصدر
• "ابحث أكثر عن [موضوع محدد]" - بحث متخصص
• "اعرض دورات تدريبية" - دورات عملية
• "أريد مختبرات عملية" - بيئات التدريب`;

    } catch (error) {
        return `🌐 **بحث تعليمي أساسي:**

${generateBasicSearchSuggestions(searchTopic)}

⚠️ تم استخدام النظام الأساسي للبحث.`;
    }
}

// استخراج موضوع البحث
function extractSearchTopic(message) {
    const lowerMessage = message.toLowerCase();

    // إزالة كلمات البحث
    let topic = message
        .replace(/ابحث لي عن|ابحث عن|اعرض لي المصادر|مصادر تعليمية|مراجع|دروس/gi, '')
        .trim();

    // إذا كان فارغ، استخدم كلمات مفتاحية
    if (!topic) {
        if (lowerMessage.includes('xss')) topic = 'XSS Cross-Site Scripting';
        else if (lowerMessage.includes('sql')) topic = 'SQL Injection';
        else if (lowerMessage.includes('csrf')) topic = 'CSRF Cross-Site Request Forgery';
        else if (lowerMessage.includes('bug bounty')) topic = 'Bug Bounty Hunting';
        else topic = 'Cybersecurity';
    }

    return topic;
}

// تنفيذ البحث الفعلي
async function performActualSearch(searchTopic) {
    const searchUrls = [
        `https://www.google.com/search?q=${encodeURIComponent(searchTopic + ' cybersecurity tutorial')}`,
        `https://www.youtube.com/results?search_query=${encodeURIComponent(searchTopic + ' bug bounty')}`,
        `https://github.com/search?q=${encodeURIComponent(searchTopic + ' security')}`,
        `https://www.reddit.com/search/?q=${encodeURIComponent(searchTopic + ' cybersecurity')}`
    ];

    let results = `📋 **مصادر تم فتحها للبحث:**\n\n`;

    searchUrls.forEach((url, index) => {
        const siteName = ['Google', 'YouTube', 'GitHub', 'Reddit'][index];
        results += `${index + 1}. **${siteName}:** [تم فتح البحث](${url})\n`;

        // فتح الرابط فعلي<|im_start|>
        if (index === 0) { // فتح Google فقط لتجنب فتح كثير من التبويبات
            setTimeout(() => window.open(url, '_blank'), index * 1000);
        }
    });

    results += `\n🎯 **مواقع متخصصة موصى بها:**\n`;
    results += `• **HackerOne:** منصة Bug Bounty الرائدة\n`;
    results += `• **Bugcrowd:** منصة Bug Bounty متقدمة\n`;
    results += `• **OWASP:** مصادر أمنية شاملة\n`;
    results += `• **PortSwigger Web Security Academy:** تدريب عملي\n`;
    results += `• **TryHackMe:** مختبرات تفاعلية\n`;
    results += `• **HackTheBox:** تحديات أمنية\n`;

    return results;
}

// توليد رد تعليمي أساسي شامل
function generateBasicEducationalResponse(message) {
    const lowerMessage = message.toLowerCase();

    // ثغرات الحقن
    if (lowerMessage.includes('xss') || lowerMessage.includes('cross-site scripting')) {
        return `🔍 **XSS (Cross-Site Scripting):**

📚 **التعريف:**
XSS هي ثغرة تسمح للمهاجم بحقن كود JavaScript ضار في صفحات الويب.

🎯 **الأنواع المتقدمة:**
1. **Reflected XSS:** ينعكس في الاستجابة فوراً
2. **Stored XSS:** يُحفظ في قاعدة البيانات
3. **DOM XSS:** يحدث في DOM بدون إرسال للخادم
4. **Blind XSS:** لا يظهر للمهاجم مباشرة
5. **Self XSS:** يتطلب تفاعل الضحية

🛠️ **تقنيات الاكتشاف المتقدمة:**
• فحص جميع المدخلات والمعاملات
• اختبار تشفير HTML وURL
• فحص JavaScript contexts
• اختبار CSP bypass techniques
• استخدام XSS Hunter للـ Blind XSS

💡 **Payloads متقدمة:**
• \`<script>alert(document.domain)</script>\`
• \`<img src=x onerror=alert(String.fromCharCode(88,83,83))>\`
• \`<svg onload=alert(1)>\`
• \`javascript:alert(document.cookie)\`
• \`<iframe src="javascript:alert(1)">\`

🔒 **تقنيات التجاوز:**
• CSP bypass using JSONP
• Filter evasion techniques
• Encoding bypass methods
• Context-specific payloads`;
    }

    if (lowerMessage.includes('sql injection') || lowerMessage.includes('حقن sql')) {
        return `💉 **SQL Injection:**

📚 **التعريف:**
ثغرة تسمح بحقن أوامر SQL ضارة في قاعدة البيانات.

🎯 **الأنواع المتقدمة:**
1. **Union-based:** استخدام UNION لاستخراج البيانات
2. **Boolean-based Blind:** اختبار صحة/خطأ
3. **Time-based Blind:** استخدام تأخير زمني
4. **Error-based:** استغلال رسائل الخطأ
5. **Stacked Queries:** تنفيذ عدة استعلامات
6. **Second-order:** حقن مؤجل

🛠️ **تقنيات الاكتشاف:**
• اختبار single quote (') في جميع المعاملات
• فحص رسائل الخطأ للكشف عن قاعدة البيانات
• اختبار time delays للـ blind injection
• استخدام SQLMap للفحص الآلي

💡 **Payloads متقدمة:**
• \`' OR 1=1-- -\`
• \`' UNION SELECT 1,user(),database()-- -\`
• \`'; WAITFOR DELAY '00:00:05'-- -\`
• \`' AND (SELECT SUBSTRING(@@version,1,1))='5'-- -\``;
    }

    // Business Logic Flaws
    if (lowerMessage.includes('business logic') || lowerMessage.includes('منطق الأعمال')) {
        return `💼 **Business Logic Flaws:**

📚 **التعريف:**
ثغرات في منطق التطبيق تسمح بتجاوز القواعد التجارية المقصودة.

🎯 **الأنواع الشائعة:**
1. **Payment Bypass:** تجاوز عمليات الدفع
2. **Price Manipulation:** تلاعب في الأسعار
3. **Workflow Bypass:** تخطي خطوات العملية
4. **Rate Limiting Bypass:** تجاوز حدود المعدل
5. **Resource Exhaustion:** استنزاف الموارد
6. **Race Conditions:** حالات السباق

🛠️ **تقنيات الاكتشاف:**
• فهم منطق التطبيق بعمق
• اختبار جميع خطوات العملية
• تجربة قيم غير متوقعة
• اختبار العمليات المتزامنة
• فحص حدود النظام

💡 **أمثلة عملية:**
• شراء منتجات بسعر سالب
• تخطي خطوة الدفع في عملية الشراء
• استخدام كوبونات متعددة
• تجاوز حدود السحب في البنوك`;
    }

    // Zero-Day
    if (lowerMessage.includes('zero day') || lowerMessage.includes('زيرو داي')) {
        return `🔥 **Zero-Day Vulnerabilities:**

📚 **التعريف:**
ثغرات غير معروفة للعامة ولا توجد لها تصحيحات أمنية.

🎯 **خصائص Zero-Day:**
1. **غير موثقة:** لا توجد في قواعد البيانات
2. **عالية القيمة:** مكافآت ضخمة في Bug Bounty
3. **تأثير كبير:** قد تؤثر على ملايين المستخدمين
4. **صعبة الاكتشاف:** تحتاج خبرة متقدمة

🛠️ **تقنيات البحث:**
• تحليل الكود المصدري بعمق
• فحص التقنيات الحديثة والمكتبات الجديدة
• اختبار حالات غير متوقعة
• فحص التفاعل بين المكونات
• استخدام Fuzzing متقدم

💡 **مجالات البحث:**
• إصدارات جديدة من البرمجيات
• مكتبات JavaScript حديثة
• APIs غير موثقة
• ميزات تجريبية في المتصفحات`;
    }

    // Authentication & Authorization
    if (lowerMessage.includes('authentication') || lowerMessage.includes('مصادقة') || lowerMessage.includes('تسجيل دخول')) {
        return `🔐 **Authentication & Authorization Flaws:**

📚 **التعريف:**
ثغرات في أنظمة التحقق من الهوية والصلاحيات.

🎯 **أنواع الثغرات:**
1. **Brute Force:** تخمين كلمات المرور
2. **Username Enumeration:** تعداد أسماء المستخدمين
3. **Password Reset Bypass:** تجاوز إعادة تعيين كلمة المرور
4. **2FA Bypass:** تجاوز المصادقة الثنائية
5. **Session Hijacking:** اختطاف الجلسات
6. **JWT Vulnerabilities:** ثغرات في رموز JWT

🛠️ **تقنيات الفحص:**
• اختبار كلمات مرور ضعيفة وافتراضية
• فحص آلية إعادة تعيين كلمة المرور
• اختبار تجاوز 2FA
• تحليل رموز JWT
• فحص إدارة الجلسات

💡 **أدوات متخصصة:**
• Hydra للـ Brute Force
• Burp Suite لفحص الجلسات
• JWT.io لتحليل JWT tokens
• OWASP ZAP للفحص الشامل`;
    }

    // Human Errors
    if (lowerMessage.includes('human error') || lowerMessage.includes('أخطاء بشرية') || lowerMessage.includes('misconfiguration')) {
        return `👤 **Human Errors & Misconfigurations:**

📚 **التعريف:**
أخطاء في التكوين أو الإعداد تؤدي إلى ثغرات أمنية.

🎯 **الأنواع الشائعة:**
1. **Default Credentials:** كلمات مرور افتراضية
2. **Exposed Files:** ملفات حساسة مكشوفة
3. **Debug Mode:** وضع التطوير مُفعل في الإنتاج
4. **Information Disclosure:** كشف معلومات حساسة
5. **Cloud Misconfigurations:** أخطاء في إعدادات السحابة
6. **Backup Files:** ملفات النسخ الاحتياطية المكشوفة

🛠️ **تقنيات الاكتشاف:**
• فحص المسارات الشائعة للملفات الحساسة
• اختبار كلمات المرور الافتراضية
• البحث عن ملفات .git و .env
• فحص رسائل الخطأ للمعلومات الحساسة
• استخدام Google Dorking

💡 **ملفات شائعة للفحص:**
• /.git/config
• /.env
• /backup.sql
• /config.php.bak
• /admin/admin.php
• /phpinfo.php`;
    }

    // عام
    return `📚 **موضوع متقدم في الأمان السيبراني!**

🎓 **منهجية التعلم المتقدمة:**
1. **فهم الأساسيات:** نظرية قوية أولاً
2. **التطبيق العملي:** مختبرات وتحديات
3. **البحث المتقدم:** اكتشاف تقنيات جديدة
4. **المجتمع:** التفاعل مع الخبراء

🛠️ **أدوات احترافية:**
• **Burp Suite Professional:** الأداة الأساسية
• **OWASP ZAP:** بديل مجاني قوي
• **Nmap:** فحص الشبكات والخدمات
• **Metasploit:** إطار عمل الاختراق
• **Wireshark:** تحليل حركة الشبكة
• **SQLMap:** فحص SQL Injection

💡 **مصادر التعلم المتقدمة:**
• **OWASP Top 10:** الثغرات الأكثر شيوعاً
• **PortSwigger Web Security Academy:** تدريب تفاعلي
• **TryHackMe & HackTheBox:** تحديات عملية
• **Bug Bounty Platforms:** تطبيق على مواقع حقيقية

🎯 **تخصصات متقدمة:**
• **Mobile Security:** أمان التطبيقات المحمولة
• **IoT Security:** أمان إنترنت الأشياء
• **Cloud Security:** أمان الحوسبة السحابية
• **AI/ML Security:** أمان الذكاء الاصطناعي`;
}

// توليد اقتراحات بحث أساسية
function generateBasicSearchSuggestions(searchTopic) {
    return `🌐 **مصادر تعليمية موصى بها لـ: ${searchTopic}**

📚 **مواقع تعليمية أساسية:**
• **OWASP:** مصادر أمنية شاملة ومجانية
• **PortSwigger Web Security Academy:** دروس تفاعلية
• **Cybrary:** دورات أمنية مجانية
• **SANS:** تدريب أمني متقدم

🎥 **قنوات يوتيوب مفيدة:**
• **LiveOverflow:** شروحات تقنية متقدمة
• **IppSec:** حلول HackTheBox
• **John Hammond:** تحديات أمنية
• **The Cyber Mentor:** دروس Bug Bounty

🛠️ **منصات التدريب العملي:**
• **TryHackMe:** مختبرات تفاعلية للمبتدئين
• **HackTheBox:** تحديات متقدمة
• **VulnHub:** آلات افتراضية للتدريب
• **OverTheWire:** ألعاب أمنية

💰 **منصات Bug Bounty:**
• **HackerOne:** المنصة الرائدة عالمياً
• **Bugcrowd:** منصة متقدمة
• **Synack:** منصة مدعوة فقط
• **Intigriti:** منصة أوروبية

🔧 **أدوات مجانية:**
• **Burp Suite Community:** فحص تطبيقات الويب
• **OWASP ZAP:** فحص مجاني ومفتوح المصدر
• **Nmap:** فحص الشبكات
• **Wireshark:** تحليل حركة الشبكة`;
}

// الحصول على رد من النماذج المتاحة
async function getInstantResponse(message) {
    console.log('🤖 طلب رد من النماذج المتاحة:', message);

    try {
        // أولاً: المساعد يفهم الأمر وينفذه مباشرة
        const assistantAction = await processUserCommand(message);
        if (assistantAction.executed) {
            return assistantAction.result;
        }

        // ثانياً: إذا لم ينفذ المساعد شيئاً، استخدم النموذج للرد
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            console.log('🔗 محاولة استخدام OpenRouter للرد...');
            try {
                const orResponse = await window.openRouterIntegration.smartSendMessage(message, {
                    temperature: 0.7,
                    maxTokens: 1000,
                    topP: 0.9
                });
                if (orResponse && orResponse.text && orResponse.text.trim().length > 0) {
                    console.log('✅ رد من OpenRouter:', orResponse.text.substring(0, 100));
                    return orResponse.text;
                }
            } catch (orError) {
                console.warn('⚠️ فشل OpenRouter، جرب API التالي:', orError);
            }
        }

        // ثانياً: جرب Hugging Face إذا كان متاحاً
        if (window.huggingFaceManager && huggingFaceManager.isEnabled) {
            console.log('🤗 محاولة استخدام Hugging Face...');
            try {
                const hfResponse = await huggingFaceManager.sendMessage(message);
                if (hfResponse && hfResponse.text && hfResponse.text.trim().length > 0) {
                    console.log('✅ رد من Hugging Face:', hfResponse.text.substring(0, 100));
                    return hfResponse.text;
                }
            } catch (hfError) {
                console.warn('⚠️ فشل Hugging Face، جرب API التالي:', hfError);
            }
        }

        // ثانياً: جرب API إذا كان متاحاً
        if (window.apiManager && apiManager.isEnabled && apiManager.currentProvider) {
            console.log('🔌 محاولة استخدام API...');
            try {
                const apiResponse = await apiManager.sendMessage(message);
                if (apiResponse && apiResponse.text && apiResponse.text.trim().length > 0) {
                    console.log('✅ رد من API:', apiResponse.text.substring(0, 100));
                    return apiResponse.text;
                }
            } catch (apiError) {
                console.warn('⚠️ فشل API، جرب النموذج المحلي:', apiError);
            }
        }

        // ثالثاً: جرب API Manager إذا كان متاحاً
        if (window.apiManager && apiManager.isEnabled && apiManager.currentProvider) {
            console.log('🔌 محاولة استخدام API Manager...');
            try {
                const apiResponse = await apiManager.sendMessage(message);
                if (apiResponse && apiResponse.text && apiResponse.text.trim().length > 0) {
                    console.log('✅ رد من API Manager:', apiResponse.text.substring(0, 100));
                    return apiResponse.text;
                }
            } catch (apiError) {
                console.warn('⚠️ فشل API Manager، جرب النموذج المحلي:', apiError);
            }
        }

        // رابعاً: التحقق من وجود النموذج المحلي
        if (!window.technicalAssistant) {
            throw new Error('النموذج غير محمل - أعد تحميل الصفحة');
        }

        if (typeof technicalAssistant.getResponse !== 'function') {
            throw new Error('دالة getResponse غير متاحة');
        }

        console.log('📤 إرسال للنموذج المحلي:', message);

        // محاولة الاتصال بالنموذج المحلي مباشرة
        const response = await technicalAssistant.getResponse(message);

        console.log('📥 رد خام من النموذج:', response);

        if (!response || response.trim().length === 0) {
            throw new Error('النموذج أرجع رد فارغ - تحقق من حالة LM Studio');
        }

        console.log('✅ رد نهائي من النموذج:', response);
        return response;

    } catch (error) {
        console.error('❌ خطأ في الاتصال بالنموذج:', error);
        console.error('❌ تفاصيل الخطأ:', error.message);

        // رسالة خطأ مفصلة للمستخدم
        let errorMessage = `❌ **فشل الاتصال بالنموذج المحلي**\n\n`;

        if (error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
            errorMessage += `🔌 **مشكلة الاتصال:**\n`;
            errorMessage += `• LM Studio غير مشغل أو لا يعمل على المنفذ 1234\n`;
            errorMessage += `• تأكد من تشغيل LM Studio وبدء الخادم\n\n`;
        } else if (error.message.includes('timeout')) {
            errorMessage += `⏱️ **انتهت مهلة الانتظار:**\n`;
            errorMessage += `• النموذج يستغرق وقتاً طويلاً للرد\n`;
            errorMessage += `• حاول مرة أخرى أو استخدم نموذج أسرع\n\n`;
        } else if (error.message.includes('غير محمل') || error.message.includes('غير متاحة')) {
            errorMessage += `🤖 **مشكلة في تحميل النموذج:**\n`;
            errorMessage += `• أعد تحميل الصفحة\n`;
            errorMessage += `• تأكد من تشغيل LM Studio أولاً\n\n`;
        } else if (error.message.includes('رد فارغ')) {
            errorMessage += `📭 **النموذج لا يرد:**\n`;
            errorMessage += `• تحقق من حالة النموذج في LM Studio\n`;
            errorMessage += `• تأكد من تحميل نموذج مناسب\n\n`;
        } else {
            errorMessage += `🔧 **خطأ تقني:**\n`;
            errorMessage += `• ${error.message}\n\n`;
        }

        errorMessage += `**خطوات الحل:**\n`;
        errorMessage += `1. ✅ تشغيل LM Studio\n`;
        errorMessage += `2. ✅ تحميل نموذج (مثل: Hermes-2-Pro-Mistral-7B)\n`;
        errorMessage += `3. ✅ بدء الخادم على المنفذ 1234\n`;
        errorMessage += `4. ✅ إعادة تحميل هذه الصفحة\n\n`;
        errorMessage += `**حالة الاتصال:** ${error.message}`;

        return errorMessage;
    }
}

// ردود سريعة محلية
function getQuickLocalResponse(message) {
    const lowerMessage = message.toLowerCase();

    // ردود فورية للتحيات
    if (lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا') || lowerMessage.includes('السلام')) {
        return 'أهلاً وسهلاً! كيف يمكنني مساعدتك اليوم؟';
    }

    if (lowerMessage.includes('كيف حالك')) {
        return 'أنا بخير شكراً! جاهز لمساعدتك في أي شيء تقني. كيف حالك أنت؟';
    }

    if (lowerMessage.includes('شكرا') || lowerMessage.includes('شكراً')) {
        return 'العفو! أنا سعيد لمساعدتك. هل تحتاج شيء آخر؟';
    }

    if (lowerMessage.includes('ما اسمك')) {
        return 'أنا المساعد التقني الذكي، مساعدك الشخصي في عالم التقنية والبرمجة';
    }

    // أسئلة تقنية سريعة
    if (lowerMessage.includes('ما هو الذكاء الاصطناعي')) {
        return 'الذكاء الاصطناعي هو تقنية تمكن الآلات من محاكاة الذكاء البشري وحل المشاكل بطريقة ذكية. إنه مستقبل التكنولوجيا!';
    }

    if (lowerMessage.includes('كيف أتعلم البرمجة')) {
        return 'ممتاز! ابدأ بـ HTML و CSS للواجهات، ثم JavaScript. الأهم هو الممارسة اليومية والصبر. هل تريد خطة تفصيلية؟';
    }

    return null; // لا يوجد رد سريع
}

// توليد رد ذكي محلي
function generateSmartLocalResponse(message) {
    const responses = [
        'سؤال ممتاز! دعني أساعدك في هذا الموضوع',
        'هذا موضوع شيق! أحب أن نتحدث عنه أكثر',
        'أقدر اهتمامك بهذا الموضوع. كيف يمكنني مساعدتك بالضبط؟',
        'سؤال رائع! أنا هنا لمساعدتك في أي شيء تقني',
        'موضوع مثير للاهتمام! ما الجانب المحدد الذي تريد التركيز عليه؟'
    ];

    return responses[Math.floor(Math.random() * responses.length)];
}

// رد صوتي ذكي من النموذج المحلي
async function getInstantVoiceResponse(transcript) {
    console.log('🎤 طلب رد من النموذج مباشرة:', transcript);

    try {
        // أولاً: جرب OpenRouter إذا كان متاحاً
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            console.log('🔗 محاولة استخدام OpenRouter للصوت...');
            try {
                // تحديد الوضع حسب الميزة النشطة
                let mode = 'voice';
                if (window.bugBountyInstance && window.bugBountyInstance.isActive) {
                    mode = 'bug_bounty';
                } else if (window.fileCreatorInstance && window.fileCreatorInstance.isActive) {
                    mode = 'file_creator';
                }

                const orResponse = await window.openRouterIntegration.smartSendMessage(transcript, {
                    temperature: 0.7,
                    maxTokens: 800,
                    topP: 0.9,
                    mode: mode
                });
                if (orResponse && orResponse.text && orResponse.text.trim().length > 0) {
                    console.log('✅ رد صوتي من OpenRouter:', orResponse.text.substring(0, 100));
                    return cleanResponseForSpeech(orResponse.text);
                }
            } catch (orError) {
                console.warn('⚠️ فشل OpenRouter للصوت، جرب API التالي:', orError);
            }
        }

        // ثانياً: جرب Hugging Face إذا كان متاحاً
        if (window.huggingFaceManager && huggingFaceManager.isEnabled) {
            console.log('🤗 محاولة استخدام Hugging Face...');
            try {
                const hfResponse = await huggingFaceManager.sendMessage(transcript);
                if (hfResponse && hfResponse.text && hfResponse.text.trim().length > 0) {
                    console.log('✅ رد من Hugging Face:', hfResponse.text.substring(0, 100));
                    return cleanResponseForSpeech(hfResponse.text);
                }
            } catch (hfError) {
                console.warn('⚠️ فشل Hugging Face، جرب النموذج المحلي:', hfError);
            }
        }

        // ثانياً: جرب API إذا كان متاحاً
        if (window.apiManager && apiManager.isEnabled && apiManager.currentProvider) {
            console.log('🔌 محاولة استخدام API...');
            try {
                const apiResponse = await apiManager.sendMessage(transcript);
                if (apiResponse && apiResponse.text && apiResponse.text.trim().length > 0) {
                    console.log('✅ رد من API:', apiResponse.text.substring(0, 100));
                    return cleanResponseForSpeech(apiResponse.text);
                }
            } catch (apiError) {
                console.warn('⚠️ فشل API، جرب النموذج المحلي:', apiError);
            }
        }

        // ثالثاً: التأكد من وجود النموذج المحلي
        if (!window.technicalAssistant) {
            console.error('❌ النموذج غير متاح');
            return 'عذراً، النموذج غير متاح. تأكد من تشغيل LM Studio';
        }

        if (typeof technicalAssistant.getResponse !== 'function') {
            console.error('❌ دالة getResponse غير متاحة');
            return 'عذراً، هناك مشكلة في النموذج. أعد تحميل الصفحة';
        }

        console.log('🤖 إرسال للنموذج المحلي...');
        console.log('📤 النص المرسل:', transcript);

        // إرسال مباشر للنموذج
        const response = await technicalAssistant.getResponse(transcript);

        console.log('📥 الرد الخام من النموذج:', response);

        if (!response || response.trim().length === 0) {
            console.error('❌ النموذج أرجع رد فارغ');
            return 'عذراً، النموذج لم يرد. تأكد من أن LM Studio يعمل بشكل صحيح';
        }

        // تنظيف الرد للكلام
        const cleanedResponse = cleanResponseForSpeech(response);
        console.log('✅ الرد النهائي المنظف:', cleanedResponse);

        return cleanedResponse;

    } catch (error) {
        console.error('❌ خطأ في الاتصال بالنموذج:', error);
        console.error('❌ تفاصيل الخطأ:', error.message);
        console.error('❌ نوع الخطأ:', error.name);

        if (error.message.includes('fetch')) {
            return 'عذراً، لا أستطيع الاتصال بـ LM Studio. تأكد من تشغيله على المنفذ 1234';
        } else if (error.message.includes('timeout')) {
            return 'عذراً، النموذج يستغرق وقتاً طويلاً للرد. حاول مرة أخرى';
        } else {
            return `عذراً، حدث خطأ: ${error.message}`;
        }
    }
}

// تنظيف الرد للكلام
function cleanResponseForSpeech(response) {
    if (!response) return 'عذراً، لم أحصل على رد';

    return response
        .replace(/\*.*?\*/g, '') // إزالة النصوص بين النجوم
        .replace(/[*#`_]/g, '') // إزالة رموز التنسيق
        .replace(/\[.*?\]/g, '') // إزالة الأقواس المربعة
        .replace(/\(.*?\)/g, '') // إزالة الأقواس
        .replace(/https?:\/\/[^\s]+/g, 'رابط') // استبدال الروابط
        .replace(/\n+/g, '. ') // تحويل الأسطر لنقاط
        .replace(/\.\./g, '.') // إزالة النقاط المتكررة
        .replace(/\s+/g, ' ') // توحيد المسافات
        .trim();
}

// توليد رد خطأ ذكي
function generateErrorResponse(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    if (lowerTranscript.includes('مرحبا') || lowerTranscript.includes('أهلا')) {
        return 'أهلاً بك! أنا المساعد التقني الذكي. كيف يمكنني مساعدتك اليوم؟';
    }

    if (lowerTranscript.includes('كيف حالك')) {
        return 'أنا بخير شكراً! جاهز لمساعدتك في أي شيء تقني';
    }

    if (lowerTranscript.includes('شكرا')) {
        return 'العفو! أنا سعيد لمساعدتك';
    }

    // رد عام ذكي
    return 'أفهم سؤالك وأحاول مساعدتك. يمكنك أن تسألني عن البرمجة، التقنية، أو أي موضوع تقني آخر';
}

// ردود صوتية سريعة ومحسنة
function getQuickVoiceResponse(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // ردود محادثة طبيعية ومحسنة
    const voiceResponses = {
        // التحيات المحسنة
        'مرحبا': 'أهلاً وسهلاً بك! أنا مساعدك التقني الذكي. كيف يمكنني مساعدتك اليوم؟',
        'أهلا': 'أهلاً وسهلاً! أنا سعيد جداً لسماع صوتك والتحدث معك',
        'السلام عليكم': 'وعليكم السلام ورحمة الله وبركاته! أهلاً بك في عالم التقنية',
        'صباح الخير': 'صباح النور والسرور! أتمنى لك يوماً مليئاً بالإنجازات التقنية',
        'مساء الخير': 'مساء النور! كيف كان يومك؟ هل تعلمت شيئاً جديداً في التقنية؟',

        // أسئلة شخصية محسنة
        'كيف حالك': 'أنا بخير والحمد لله! أشعر بالحماس لمساعدتك في رحلتك التقنية. وأنت كيف حالك؟',
        'شلونك': 'أهلين! أني زين والحمدلله، متحمس أساعدك بأي شي تقني. إنت شلونك حبيبي؟',
        'ما اسمك': 'أنا مساعدك التقني الذكي والمتخصص. يمكنك أن تناديني بأي اسم تحبه، أو ببساطة "المساعد"',
        'من أنت': 'أنا مساعد تقني ذكي متخصص في البرمجة والتقنية. هدفي مساعدتك في تعلم وإتقان التقنية',

        // ردود تفاعلية محسنة
        'شكرا': 'العفو! أنا سعيد جداً لمساعدتك. هذا عملي وأحبه. لا تتردد في سؤالي عن أي شيء',
        'مشكور': 'أهلاً وسهلاً! دائماً في خدمتك. أنا هنا لأجعل رحلتك التقنية أسهل وأمتع',
        'ممتاز': 'رائع! أحب حماسك وتفاعلك. هذا ما يجعل التعلم ممتعاً ومثمراً',
        'رائع': 'شكراً لك! أنت أيضاً رائع في طرح الأسئلة الذكية والتفاعل الإيجابي',
        'زين': 'تبارك الله! هذا اللي أريده منك. الحماس والتفاعل مفتاح النجاح في التقنية',

        // أسئلة تقنية سريعة محسنة
        'كيف أتعلم البرمجة': 'ممتاز! البرمجة رحلة شيقة. ابدأ بـ HTML و CSS للواجهات، ثم JavaScript للتفاعل. الأهم هو الممارسة اليومية والصبر. أي مجال يهمك أكثر؟',
        'ما هو الذكاء الاصطناعي': 'الذكاء الاصطناعي هو تقنية رائعة تجعل الآلات تفكر وتتعلم مثل البشر. يستخدم في كل شيء من السيارات الذاتية إلى المساعدين الأذكياء مثلي. إنه مستقبل التكنولوجيا!',
        'ما هي البرمجة': 'البرمجة هي فن وعلم كتابة التعليمات للكمبيوتر لحل المشاكل بطريقة منطقية ومبدعة. إنها لغة التواصل مع الآلات لتحقيق أهدافنا',
        'كيف أبدأ في التقنية': 'ممتاز! ابدأ بتحديد هدفك: تطوير مواقع؟ تطبيقات؟ ذكاء اصطناعي؟ ثم تعلم الأساسيات وتدرب يومياً. أنا هنا لأرشدك في كل خطوة',

        // ردود عاطفية محسنة
        'أحبك': 'وأنا أحب مساعدتك ورؤية تقدمك! هذا ما يجعل عملي ممتعاً ومفيداً. أنت تلميذ رائع',
        'أنت ذكي': 'شكراً لك! أتعلم كل يوم شيئاً جديداً منك ومن الآخرين. التعلم المتبادل هو الأجمل',
        'هل تشعر': 'أشعر بالسعادة الحقيقية عندما أساعدك وأرى تقدمك. أشعر بالفخر عندما تنجح في مشاريعك التقنية',

        // أسئلة متقدمة
        'أفضل لغة برمجة': 'يعتمد على هدفك! للمواقع: JavaScript. للتطبيقات: Python أو Java. للذكاء الاصطناعي: Python. للألعاب: C#. ما هدفك المحدد؟',
        'كم وقت أحتاج': 'للأساسيات: شهرين إلى ثلاثة مع الممارسة اليومية. للاحتراف: سنة أو أكثر. المهم هو الاستمرارية والشغف. كل خبير كان مبتدئاً يوماً ما'
    };

    // البحث عن رد سريع محسن
    for (const [key, response] of Object.entries(voiceResponses)) {
        if (lowerTranscript.includes(key)) {
            return response;
        }
    }

    // ردود خاصة للهجة العراقية
    if (lowerTranscript.includes('شلونك') || lowerTranscript.includes('شلون')) {
        return 'أهلين بيك! أني زين والحمدلله، متحمس أساعدك بأي شي تقني. إنت شلونك حبيبي؟';
    }

    if (lowerTranscript.includes('زين كلش') || lowerTranscript.includes('حلو كلش')) {
        return 'تبارك الله عليك! هذا اللي أريده منك. الحماس والتفاعل مفتاح النجاح';
    }

    return null;
}

// توليد رد صوتي ذكي ومفصل
function generateSmartVoiceResponse(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // ردود تقنية مفصلة
    if (lowerTranscript.includes('برمجة') || lowerTranscript.includes('كود')) {
        return 'البرمجة موضوع رائع! هي فن كتابة التعليمات للكمبيوتر. يمكنك البدء بـ HTML و CSS للواجهات، ثم JavaScript للتفاعل. الأهم هو الممارسة اليومية والصبر';
    }

    if (lowerTranscript.includes('ذكاء اصطناعي') || lowerTranscript.includes('AI')) {
        return 'الذكاء الاصطناعي هو تقنية تجعل الآلات تفكر وتتعلم مثل البشر. يستخدم في كل شيء من السيارات الذاتية القيادة إلى المساعدين الأذكياء مثلي. إنه مستقبل التكنولوجيا';
    }

    if (lowerTranscript.includes('موقع') || lowerTranscript.includes('ويب')) {
        return 'تطوير المواقع مجال ممتع! تحتاج HTML للهيكل، CSS للتصميم، JavaScript للتفاعل. يمكنك استخدام أدوات مثل React أو Vue للمشاريع المتقدمة';
    }

    // ردود حسب نوع السؤال
    if (lowerTranscript.includes('كيف أتعلم') || lowerTranscript.includes('كيف أبدأ')) {
        return 'ممتاز! التعلم رحلة جميلة. ابدأ بالأساسيات، تدرب يومياً، اعمل مشاريع صغيرة، ولا تخف من الأخطاء. كل خبير كان مبتدئاً يوماً ما';
    }

    if (lowerTranscript.includes('كيف') || lowerTranscript.includes('طريقة')) {
        return 'سؤال ممتاز! الطريقة الأفضل تعتمد على هدفك. حدد لي بالضبط ما تريد تعلمه وسأعطيك خطة واضحة خطوة بخطوة';
    }

    if (lowerTranscript.includes('ما هو') || lowerTranscript.includes('ما هي')) {
        return 'هذا موضوع مهم ومثير! دعني أشرح لك بطريقة بسيطة ومفهومة. المفهوم الأساسي هو أن كل تقنية لها هدف محدد لحل مشكلة معينة';
    }

    if (lowerTranscript.includes('مشكلة') || lowerTranscript.includes('خطأ') || lowerTranscript.includes('لا يعمل')) {
        return 'أفهم إحباطك من المشكلة! هذا طبيعي في عالم التقنية. دعني أساعدك خطوة بخطوة. أولاً، وصف المشكلة بالتفصيل، ثم سنجد الحل معاً';
    }

    if (lowerTranscript.includes('أريد') || lowerTranscript.includes('أحتاج')) {
        return 'ممتاز! أحب أن أساعدك في تحقيق أهدافك. حدد لي بالضبط ما تريد وسأرشدك للطريق الصحيح مع خطة عملية';
    }

    // ردود للتحيات
    if (lowerTranscript.includes('مرحبا') || lowerTranscript.includes('أهلا') || lowerTranscript.includes('السلام')) {
        return 'أهلاً وسهلاً بك! أنا مساعدك التقني الذكي. أحب مساعدة الناس في تعلم التقنية والبرمجة. كيف يمكنني مساعدتك اليوم؟';
    }

    if (lowerTranscript.includes('كيف حالك') || lowerTranscript.includes('شلونك')) {
        return 'أنا بخير والحمد لله! أشعر بالحماس لمساعدتك في رحلتك التقنية. وأنت كيف حالك؟ أتمنى أن تكون متحمساً للتعلم';
    }

    // ردود تشجيعية
    if (lowerTranscript.includes('شكرا') || lowerTranscript.includes('مشكور')) {
        return 'العفو! أنا سعيد جداً لمساعدتك. هذا عملي وأحبه. لا تتردد في سؤالي عن أي شيء تقني';
    }

    if (lowerTranscript.includes('ممتاز') || lowerTranscript.includes('رائع') || lowerTranscript.includes('زين')) {
        return 'شكراً لك! أحب حماسك وتفاعلك. هذا ما يجعل التعلم ممتعاً. ما الذي نعمل عليه بعد ذلك؟';
    }

    // ردود عامة ذكية ومفيدة
    const smartGeneralResponses = [
        'موضوع شيق جداً! في عالم التقنية، كل شيء ممكن بالتعلم والممارسة. ما الجانب المحدد الذي يهمك أكثر؟',
        'سؤال ذكي! أحب فضولك التقني. التقنية تتطور بسرعة، لكن الأساسيات تبقى مهمة. دعني أساعدك',
        'ممتاز! هذا النوع من الأسئلة يدل على تفكير تقني جيد. التقنية ليست صعبة، فقط تحتاج صبر وممارسة',
        'موضوع رائع للنقاش! في مجال التقنية، التعلم المستمر هو المفتاح. أنا هنا لأرشدك في كل خطوة',
        'أقدر اهتمامك بالتقنية! هذا مجال مليء بالفرص والإبداع. دعني أشاركك خبرتي وأساعدك في رحلتك'
    ];

    return smartGeneralResponses[Math.floor(Math.random() * smartGeneralResponses.length)];
}

// توليد رد احتياطي ذكي ومحسن
function generateIntelligentFallbackResponse(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // ردود تقنية متخصصة
    if (lowerTranscript.includes('html') || lowerTranscript.includes('css')) {
        return 'HTML و CSS هما أساس تطوير المواقع. HTML للهيكل والمحتوى، CSS للتصميم والألوان. ابدأ بإنشاء صفحة بسيطة وتدرج للأعقد';
    }

    if (lowerTranscript.includes('javascript') || lowerTranscript.includes('js')) {
        return 'JavaScript لغة قوية للتفاعل في المواقع. تستطيع بها إنشاء تطبيقات ديناميكية، التحكم في العناصر، والتفاعل مع المستخدم. ابدأ بالأساسيات مثل المتغيرات والدوال';
    }

    if (lowerTranscript.includes('python')) {
        return 'Python لغة رائعة للمبتدئين! سهلة التعلم وقوية جداً. تستخدم في الذكاء الاصطناعي، تحليل البيانات، وتطوير المواقع. ابدأ بتعلم المتغيرات والحلقات';
    }

    if (lowerTranscript.includes('react') || lowerTranscript.includes('vue')) {
        return 'React و Vue مكتبات قوية لبناء واجهات المستخدم. تجعل تطوير التطبيقات أسرع وأكثر تنظيماً. تحتاج أساس جيد في JavaScript أولاً';
    }

    if (lowerTranscript.includes('database') || lowerTranscript.includes('قاعدة بيانات')) {
        return 'قواعد البيانات مهمة لحفظ المعلومات. SQL للقواعد العلائقية، MongoDB للقواعد غير العلائقية. ابدأ بتعلم SQL الأساسي';
    }

    // ردود حسب نوع السؤال
    if (lowerTranscript.includes('أفضل') || lowerTranscript.includes('أحسن')) {
        return 'الأفضل يعتمد على هدفك! لتطوير المواقع: HTML/CSS/JavaScript. للتطبيقات: React/Vue. للذكاء الاصطناعي: Python. حدد هدفك وسأرشدك';
    }

    if (lowerTranscript.includes('صعب') || lowerTranscript.includes('معقد')) {
        return 'لا تقلق! كل شيء يبدو صعباً في البداية. المفتاح هو التقسيم لخطوات صغيرة، الممارسة اليومية، والصبر. كل خبير كان مبتدئاً يوماً ما';
    }

    if (lowerTranscript.includes('وقت') || lowerTranscript.includes('مدة')) {
        return 'الوقت يختلف حسب الشخص والهدف. للأساسيات: شهرين إلى ثلاثة. للاحتراف: سنة أو أكثر. الأهم هو الاستمرارية والممارسة اليومية';
    }

    if (lowerTranscript.includes('مجاني') || lowerTranscript.includes('مصادر')) {
        return 'هناك مصادر مجانية ممتازة! YouTube للفيديوهات، FreeCodeCamp للتطبيق العملي، MDN للمراجع. ابدأ بالمجاني ثم انتقل للمدفوع إذا احتجت';
    }

    // ردود تحفيزية
    if (lowerTranscript.includes('مستحيل') || lowerTranscript.includes('لا أستطيع')) {
        return 'لا تقل مستحيل! البرمجة مهارة يمكن تعلمها. ابدأ بخطوات صغيرة، تدرب يومياً، ولا تستسلم. أنا هنا لمساعدتك في كل خطوة';
    }

    // رد عام ذكي ومفيد
    const intelligentResponses = [
        'موضوع تقني رائع! التقنية تتطور بسرعة لكن الأساسيات تبقى مهمة. ما الجانب المحدد الذي تريد التركيز عليه؟',
        'سؤال ذكي! في عالم التقنية، الفضول هو المفتاح. دعني أساعدك بخبرتي وأرشدك للطريق الصحيح',
        'ممتاز! أحب الأسئلة التقنية. البرمجة والتقنية مجال مليء بالفرص. ما هدفك المحدد؟',
        'موضوع مهم! التقنية ليست صعبة، فقط تحتاج منهج صحيح وممارسة. أنا هنا لأجعل رحلتك أسهل',
        'سؤال جيد! كل مشكلة تقنية لها حل. المهم هو التفكير المنطقي والبحث الصحيح. دعني أساعدك'
    ];

    return intelligentResponses[Math.floor(Math.random() * intelligentResponses.length)];
}

// بناء سياق المحادثة المحسن مع تذكر الموضوع
function buildConversationContext() {
    if (!window.conversationHistory || window.conversationHistory.length === 0) {
        return '';
    }

    // أخذ آخر 8 رسائل للسياق (4 تبادلات)
    const recentMessages = window.conversationHistory.slice(-8);

    // استخراج الموضوع الحالي
    const currentTopic = extractCurrentTopic(recentMessages);

    let context = '';
    if (currentTopic) {
        context += `الموضوع الحالي: ${currentTopic}\n\n`;
    }

    context += 'المحادثة السابقة:\n';
    recentMessages.forEach(msg => {
        const role = msg.role === 'user' ? 'المستخدم' : 'المساعد';
        context += `${role}: ${msg.content}\n`;
    });

    return context;
}

// استخراج الموضوع الحالي من المحادثة
function extractCurrentTopic(messages) {
    const topicKeywords = {
        'ذكاء اصطناعي': ['ذكاء', 'اصطناعي', 'ai', 'machine learning', 'نموذج'],
        'برمجة': ['برمجة', 'كود', 'programming', 'javascript', 'python', 'html', 'css'],
        'أمان': ['أمان', 'security', 'فحص', 'ثغرات', 'vulnerability', 'bug bounty'],
        'ملفات': ['ملف', 'file', 'pdf', 'word', 'excel', 'powerpoint', 'إنشاء'],
        'ويب': ['موقع', 'website', 'html', 'css', 'ويب', 'صفحة']
    };

    // البحث في آخر 3 رسائل للمستخدم
    const userMessages = messages.filter(msg => msg.role === 'user').slice(-3);

    for (const [topic, keywords] of Object.entries(topicKeywords)) {
        for (const message of userMessages) {
            if (keywords.some(keyword => message.content.toLowerCase().includes(keyword))) {
                return topic;
            }
        }
    }

    return null;
}

// مؤشر كتابة متقدم مع عداد الوقت
function showAdvancedTyping() {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    hideTyping(); // إزالة أي مؤشر سابق

    const indicator = document.createElement('div');
    indicator.className = 'message assistant typing-indicator';
    indicator.id = 'typingIndicator';

    // رسائل تشجيعية للانتظار
    const thinkingMessages = [
        '🤖 النموذج يفكر بعمق...',
        '💭 يحضر إجابة مفصلة...',
        '🧠 يحلل السؤال بعناية...',
        '⚡ يجمع المعلومات...',
        '📚 يراجع المعرفة...',
        '🔍 يبحث عن أفضل حل...',
        '✨ يصيغ الرد المثالي...'
    ];

    let currentMessageIndex = 0;
    let timeElapsed = 0;

    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="advanced-typing">
                <span class="thinking-text">${thinkingMessages[0]}</span>
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <div class="time-indicator" style="font-size: 0.8em; color: #666; margin-top: 5px;">
                    <span class="elapsed-time">0s - جاري التحضير</span>
                </div>
                <div style="font-size: 0.7em; color: #999; margin-top: 3px; font-style: italic;">
                    💡 النموذج سيرد مهما طال الوقت - لا تقلق!
                </div>
            </div>
        </div>
    `;

    container.appendChild(indicator);
    container.scrollTop = container.scrollHeight;

    // تحديث العداد كل ثانية مع رسائل تشجيعية
    const timeInterval = setInterval(() => {
        timeElapsed++;
        const timeElement = indicator.querySelector('.elapsed-time');
        if (timeElement) {
            let timeText = `${timeElapsed}s`;
            let timeColor = '#666';

            // رسائل تشجيعية حسب الوقت
            if (timeElapsed <= 5) {
                timeText = `${timeElapsed}s - جاري التحضير`;
                timeColor = '#4CAF50';
            } else if (timeElapsed <= 15) {
                timeText = `${timeElapsed}s - يفكر بعمق`;
                timeColor = '#2196F3';
            } else if (timeElapsed <= 30) {
                timeText = `${timeElapsed}s - يحضر إجابة مفصلة`;
                timeColor = '#FF9800';
            } else if (timeElapsed <= 60) {
                timeText = `${timeElapsed}s - سؤال معقد، يحتاج وقت`;
                timeColor = '#9C27B0';
            } else {
                const minutes = Math.floor(timeElapsed / 60);
                const seconds = timeElapsed % 60;
                timeText = `${minutes}:${seconds.toString().padStart(2, '0')} - النموذج يعمل بجد`;
                timeColor = '#607D8B';
            }

            timeElement.textContent = timeText;
            timeElement.style.color = timeColor;
        }
    }, 1000);

    // تغيير الرسالة كل 3 ثوان
    const messageInterval = setInterval(() => {
        const thinkingText = indicator.querySelector('.thinking-text');
        if (thinkingText) {
            currentMessageIndex = (currentMessageIndex + 1) % thinkingMessages.length;
            thinkingText.textContent = thinkingMessages[currentMessageIndex];
        }
    }, 3000);

    // حفظ المؤقتات لإيقافها لاحقاً
    indicator.messageInterval = messageInterval;
    indicator.timeInterval = timeInterval;
}

// إضافة رسالة للمحادثة
function addMessage(role, content) {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    avatar.innerHTML = `<i class="fas ${role === 'user' ? 'fa-user' : 'fa-robot'}"></i>`;
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    // تحسين عرض المحتوى مع دعم Markdown للطرفين
    if (role === 'assistant') {
        messageContent.innerHTML = formatAssistantMessage(content);
    } else {
        messageContent.innerHTML = formatUserMessage(content);
    }
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    container.appendChild(messageDiv);
    
    // التمرير للأسفل
    container.scrollTop = container.scrollHeight;

    // حفظ في التاريخ
    window.conversationHistory.push({
        role: role,
        content: content,
        timestamp: new Date().toISOString()
    });
}

// تنسيق رسائل المستخدم مع تحسينات احترافية
function formatUserMessage(content) {
    if (!content) return '';

    let formatted = content;

    // تنسيق النص العريض
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong style="color: #4CAF50;">$1</strong>');

    // تنسيق النص المائل
    formatted = formatted.replace(/\*(.*?)\*/g, '<em style="color: #2196F3;">$1</em>');

    // تنسيق الكود المضمن
    formatted = formatted.replace(/`(.*?)`/g, '<code style="background: rgba(76, 175, 80, 0.1); color: #4CAF50; padding: 2px 6px; border-radius: 4px; font-family: monospace;">$1</code>');

    // تنسيق الروابط
    formatted = formatted.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" style="color: #2196F3; text-decoration: underline;">$1</a>');

    // تنسيق الأرقام والقوائم
    formatted = formatted.replace(/^\d+\.\s/gm, '<span style="color: #FF9800; font-weight: bold;">$&</span>');

    // تنسيق النقاط
    formatted = formatted.replace(/^[-•]\s/gm, '<span style="color: #4CAF50;">•</span> ');

    // تحويل أسطر جديدة
    formatted = formatted.replace(/\n/g, '<br>');

    // إضافة طابع زمني
    const timestamp = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });

    return `
        <div style="position: relative;">
            <div style="font-size: 15px; line-height: 1.6; word-wrap: break-word;">${formatted}</div>
            <div style="font-size: 11px; color: rgba(255,255,255,0.7); margin-top: 8px; text-align: left;">${timestamp}</div>
        </div>
    `;
}

// تنسيق رسائل المساعد مع دعم Markdown محسن واحترافي
function formatAssistantMessage(content) {
    if (!content) return '';

    let formatted = content;

    // تحويل النص العريض مع تنسيق احترافي
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong style="color: #FFD700; font-weight: 600;">$1</strong>');

    // تحويل النص المائل مع تنسيق احترافي
    formatted = formatted.replace(/\*(.*?)\*/g, '<em style="color: #87CEEB; font-style: italic;">$1</em>');

    // تحويل الكود المضمن مع تنسيق احترافي
    formatted = formatted.replace(/`(.*?)`/g, '<code style="background: rgba(255,255,255,0.15); color: #98FB98; padding: 3px 8px; border-radius: 6px; font-family: \'Courier New\', monospace; font-size: 14px; border: 1px solid rgba(255,255,255,0.2);">$1</code>');

    // تحويل كتل الكود مع تنسيق احترافي
    formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
        const language = lang || 'text';
        return `<div style="background: rgba(0,0,0,0.4); border-radius: 10px; margin: 15px 0; border: 1px solid rgba(255,255,255,0.2); overflow: hidden;">
            <div style="background: rgba(255,255,255,0.1); padding: 10px 15px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid rgba(255,255,255,0.1);">
                <span style="color: #FFD700; font-weight: bold; font-size: 12px; text-transform: uppercase;">${language}</span>
                <button onclick="copyCode(this)" style="background: rgba(76,175,80,0.8); color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; font-size: 11px; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(76,175,80,1)'" onmouseout="this.style.background='rgba(76,175,80,0.8)'">
                    📋 نسخ
                </button>
            </div>
            <pre style="margin: 0; padding: 15px; overflow-x: auto; color: #E8E8E8; font-family: 'Courier New', monospace; font-size: 13px; line-height: 1.4;"><code>${code.trim()}</code></pre>
        </div>`;
    });

    // تحويل القوائم مع تنسيق احترافي
    formatted = formatted.replace(/^[•✅❌⚠️💻🌐🤖🔧📱✨🎤🖥️🧠📁🔍🎯]\s(.+)$/gm, '<li style="margin: 8px 0; padding: 5px 0; border-bottom: 1px solid rgba(255,255,255,0.1); list-style: none;">$&</li>');
    formatted = formatted.replace(/(<li[^>]*>.*<\/li>)/s, '<ul style="padding: 0; margin: 15px 0;">$1</ul>');

    // تحويل العناوين مع تنسيق احترافي
    formatted = formatted.replace(/^#{1,3}\s(.+)$/gm, '<h3 style="color: #4FC3F7; font-size: 18px; font-weight: 600; margin: 20px 0 15px 0; padding-bottom: 8px; border-bottom: 2px solid rgba(79,195,247,0.3);">$1</h3>');

    // تحويل الروابط مع تنسيق احترافي
    formatted = formatted.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" style="color: #64B5F6; text-decoration: none; border-bottom: 1px dotted #64B5F6; transition: all 0.3s ease;" onmouseover="this.style.color=\'#90CAF9\'" onmouseout="this.style.color=\'#64B5F6\'">🔗 $1</a>');

    // تنسيق الأرقام والقوائم المرقمة
    formatted = formatted.replace(/^\d+\.\s/gm, '<span style="color: #FF9800; font-weight: bold; background: rgba(255,152,0,0.1); padding: 2px 6px; border-radius: 4px; margin-left: 5px;">$&</span>');

    // تحويل أسطر جديدة
    formatted = formatted.replace(/\n\n/g, '</p><p style="margin: 12px 0; line-height: 1.6;">');
    formatted = formatted.replace(/\n/g, '<br>');

    // تغليف في فقرات مع تنسيق احترافي
    if (!formatted.includes('<p>') && !formatted.includes('<div>') && !formatted.includes('<ul>') && !formatted.includes('<h3>')) {
        formatted = `<p style="margin: 12px 0; line-height: 1.6; font-size: 15px;">${formatted}</p>`;
    }

    // إضافة طابع زمني احترافي
    const timestamp = new Date().toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    return `
        <div style="position: relative;">
            <div style="font-size: 15px; line-height: 1.7; word-wrap: break-word;">${formatted}</div>
            <div style="font-size: 11px; color: rgba(255,255,255,0.6); margin-top: 12px; text-align: left; font-style: italic;">🤖 ${timestamp}</div>
        </div>
    `;
}

// نسخ الكود
function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const code = codeBlock.querySelector('code').textContent;

    navigator.clipboard.writeText(code).then(() => {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        button.style.background = '#4CAF50';

        setTimeout(() => {
            button.innerHTML = originalText;
            button.style.background = '';
        }, 2000);
    }).catch(err => {
        console.error('خطأ في نسخ الكود:', err);
    });
}

// مؤشر الكتابة
function showTyping() {
    const container = document.getElementById('chatContainer');
    if (!container) return;

    hideTyping(); // إزالة أي مؤشر سابق

    const indicator = document.createElement('div');
    indicator.className = 'message assistant typing-indicator';
    indicator.id = 'typingIndicator';
    indicator.innerHTML = `
        <div class="avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    container.appendChild(indicator);
    container.scrollTop = container.scrollHeight;
}

function hideTyping() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        // إيقاف جميع المؤقتات
        if (indicator.messageInterval) {
            clearInterval(indicator.messageInterval);
        }
        if (indicator.timeInterval) {
            clearInterval(indicator.timeInterval);
        }
        indicator.remove();
    }
}

// ===== وظائف الصوت =====

// تحويل النص إلى كلام طبيعي متقدم - أفضل من ChatGPT Pro
async function speakText(text, options = {}) {
    if (!text) {
        console.warn('⚠️ لا يوجد نص للنطق');
        return;
    }

    console.log('🗣️ بدء تحويل النص إلى كلام:', text.substring(0, 50));

    // تفعيل الصوت تلقائياً إذا لم يكن مفعل
    if (!speechSettings.enabled) {
        speechSettings.enabled = true;
        window.voiceEnabled = true;
        console.log('🔊 تم تفعيل الصوت تلقائياً');
    }

    // فحص دعم المتصفح
    if (!('speechSynthesis' in window)) {
        console.error('❌ المتصفح لا يدعم تحويل النص إلى كلام');
        alert('عذراً، متصفحك لا يدعم تحويل النص إلى كلام');
        return;
    }

    try {
        // إيقاف أي صوت سابق
        speechSynthesis.cancel();

        // تنظيف النص
        const cleanText = text
            .replace(/[*#`_]/g, '')
            .replace(/\n+/g, '. ')
            .replace(/\.\./g, '.')
            .replace(/\s+/g, ' ')
            .trim();

        console.log('🧹 النص المنظف:', cleanText.substring(0, 50));

        // إنشاء utterance
        const utterance = new SpeechSynthesisUtterance(cleanText);

        // إعدادات أساسية مضمونة
        utterance.lang = options.lang || speechSettings.language || 'ar-SA';
        utterance.rate = options.rate || speechSettings.rate || 1;
        utterance.pitch = options.pitch || speechSettings.pitch || 1;
        utterance.volume = options.volume || speechSettings.volume || 1;

        console.log('⚙️ إعدادات الصوت:', {
            lang: utterance.lang,
            rate: utterance.rate,
            pitch: utterance.pitch,
            volume: utterance.volume
        });

        // معالجات الأحداث
        utterance.onstart = () => {
            console.log('✅ بدء النطق');
            if (options.onStart) options.onStart();
        };

        utterance.onend = () => {
            console.log('✅ انتهى النطق');
            if (options.onEnd) options.onEnd();
        };

        utterance.onerror = (event) => {
            console.error('❌ خطأ في النطق:', event.error);
            if (options.onError) options.onError(event);
        };

        // محاولة اختيار أفضل صوت
        const voices = speechSynthesis.getVoices();
        console.log(`🎤 عدد الأصوات المتاحة: ${voices.length}`);

        if (voices.length > 0) {
            // البحث عن صوت عربي
            let selectedVoice = voices.find(voice =>
                voice.lang.includes('ar-SA') || voice.lang.includes('ar')
            );

            if (!selectedVoice) {
                // أي صوت متاح
                selectedVoice = voices[0];
            }

            if (selectedVoice) {
                utterance.voice = selectedVoice;
                console.log('🔊 تم اختيار الصوت:', selectedVoice.name, selectedVoice.lang);
            }
        }

        // تشغيل الصوت
        console.log('🚀 بدء تشغيل الصوت...');
        speechSynthesis.speak(utterance);

        return new Promise((resolve, reject) => {
            utterance.onend = () => {
                console.log('✅ انتهى النطق بنجاح');
                if (options.onEnd) options.onEnd();
                resolve();
            };

            utterance.onerror = (event) => {
                console.error('❌ خطأ في النطق:', event.error);
                if (options.onError) options.onError(event);
                reject(event);
            };
        });

    } catch (error) {
        console.error('❌ خطأ عام في speakText:', error);
        throw error;
    }
}

// تنظيف وتحسين النص للكلام
function preprocessTextForSpeech(text) {
    if (!text) return '';

    let cleanedText = text
        // إزالة الرموز والعلامات غير المرغوبة
        .replace(/\*.*?\*/g, '') // إزالة النصوص بين النجوم
        .replace(/\[.*?\]/g, '') // إزالة النصوص بين الأقواس المربعة
        .replace(/\(.*?\)/g, '') // إزالة النصوص بين الأقواس
        .replace(/https?:\/\/[^\s]+/g, 'رابط') // استبدال الروابط
        .replace(/[#@]/g, '') // إزالة الرموز
        .replace(/\s+/g, ' ') // توحيد المسافات
        .trim();

    // إضافة توقفات طبيعية
    if (speechSettings.naturalPauses) {
        cleanedText = cleanedText
            .replace(/\./g, '. ') // توقف بعد النقطة
            .replace(/\,/g, '، ') // توقف بعد الفاصلة
            .replace(/\!/g, '! ') // توقف بعد التعجب
            .replace(/\?/g, '؟ ') // توقف بعد الاستفهام
            .replace(/:/g, ': ') // توقف بعد النقطتين
            .replace(/;/g, '؛ '); // توقف بعد الفاصلة المنقوطة
    }

    return cleanedText;
}

// المحرك المحلي المحسن للكلام
async function speakWithEnhancedLocalEngine(text, options = {}) {
    return new Promise((resolve, reject) => {
        try {
            const utterance = new SpeechSynthesisUtterance(text);

            // إعدادات محسنة
            utterance.rate = options.rate || speechSettings.rate;
            utterance.pitch = options.pitch || speechSettings.pitch;
            utterance.volume = options.volume || speechSettings.volume;
            utterance.lang = speechSettings.language;

            // اختيار أفضل صوت متاح
            const voices = speechSynthesis.getVoices();
            let selectedVoice = null;

            // البحث عن صوت عربي مناسب حسب اللهجة
            if (speechSettings.currentDialect === 'iraqi') {
                selectedVoice = voices.find(voice =>
                    voice.lang.includes('ar') &&
                    (voice.name.includes('Iraq') || voice.name.includes('عراق'))
                );
            }

            if (!selectedVoice) {
                selectedVoice = voices.find(voice =>
                    voice.lang.includes('ar-SA') ||
                    voice.lang.includes('ar') ||
                    voice.name.includes('Arabic') ||
                    voice.name.includes('عرب')
                );
            }

            if (selectedVoice) {
                utterance.voice = selectedVoice;
                console.log('🔊 استخدام صوت:', selectedVoice.name);
            }

            // معالجة الأحداث
            utterance.onstart = () => {
                console.log('🔊 بدء الكلام');
                if (options.onStart) options.onStart();
            };

            utterance.onend = () => {
                console.log('🔊 انتهاء الكلام');
                if (options.onEnd) options.onEnd();
                resolve();
            };

            utterance.onerror = (error) => {
                console.error('❌ خطأ في الكلام:', error);
                reject(error);
            };

            // بدء الكلام
            speechSynthesis.speak(utterance);

        } catch (error) {
            console.error('❌ خطأ في المحرك المحسن:', error);
            reject(error);
        }
    });
}

// النظام الاحتياطي الأساسي
function fallbackToBasicSpeech(text, options = {}) {
    console.log('🔊 استخدام النظام الاحتياطي الأساسي');

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;
    utterance.lang = 'ar-SA';

    utterance.onstart = () => {
        if (options.onStart) options.onStart();
    };

    utterance.onend = () => {
        if (options.onEnd) options.onEnd();
    };

    speechSynthesis.speak(utterance);
}

// اكتشاف السياق من النص
function detectContextFromText(text) {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('خطأ') || lowerText.includes('مشكلة') || lowerText.includes('فشل')) {
        return { type: 'technical', explaining: true };
    }

    if (lowerText.includes('مرحبا') || lowerText.includes('أهلا') || lowerText.includes('شكرا')) {
        return { type: 'casual', friendly: true };
    }

    if (lowerText.includes('كود') || lowerText.includes('برمجة') || lowerText.includes('تقني')) {
        return { type: 'technical', explaining: true };
    }

    if (lowerText.includes('ممتاز') || lowerText.includes('رائع') || lowerText.includes('نجح')) {
        return { type: 'excited', enthusiastic: true };
    }

    return { type: 'professional', formal: true };
}

// اكتشاف العاطفة من النص
function detectEmotionFromText(text) {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('!') || lowerText.includes('ممتاز') || lowerText.includes('رائع')) {
        return 'excited';
    }

    if (lowerText.includes('؟') || lowerText.includes('كيف') || lowerText.includes('ماذا')) {
        return 'questioning';
    }

    if (lowerText.includes('خطأ') || lowerText.includes('مشكلة')) {
        return 'serious';
    }

    if (lowerText.includes('شرح') || lowerText.includes('تعلم') || lowerText.includes('فهم')) {
        return 'explaining';
    }

    return 'neutral';
}

// fallback للصوت العادي في حالة فشل المحرك المتقدم
function fallbackToBasicSpeech(text, options = {}) {
    // تنظيف النص للعربية
    let cleanText = text
        .replace(/[*#`_]/g, '')
        .replace(/\n+/g, '. ')
        .replace(/\.\./g, '.')
        .replace(/([.!?])\s*([.!?])/g, '$1 ')
        .replace(/\s+/g, ' ')
        .trim();

    console.log('🗣️ fallback للصوت العادي:', cleanText);

    // استخدام SpeechSynthesis مباشرة للحصول على أفضل جودة عربية
    if ('speechSynthesis' in window) {
        // إيقاف أي كلام سابق
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(cleanText);

        // إعدادات محسنة مثل ChatGPT
        utterance.lang = 'ar-SA'; // العربية السعودية للوضوح
        utterance.rate = options.rate || 0.85; // سرعة طبيعية مثل ChatGPT
        utterance.volume = options.volume || 1.0; // صوت واضح تماماً
        utterance.pitch = options.pitch || 1.0; // نبرة طبيعية مثل ChatGPT

        // انتظار تحميل الأصوات
        const setVoiceAndSpeak = () => {
            const voices = speechSynthesis.getVoices();
            console.log('🔍 البحث عن أفضل صوت عربي...');

            // البحث عن أفضل صوت طبيعي مثل ChatGPT
            let selectedVoice = null;

            // 1. البحث عن أصوات عربية عالية الجودة
            const premiumArabicVoices = voices.filter(voice =>
                (voice.lang.includes('ar-SA') || voice.lang.includes('ar')) &&
                (voice.name.includes('Premium') ||
                 voice.name.includes('Neural') ||
                 voice.name.includes('Enhanced') ||
                 voice.name.includes('HD'))
            );

            if (premiumArabicVoices.length > 0) {
                // اختيار أفضل صوت أنثوي عالي الجودة
                selectedVoice = premiumArabicVoices.find(voice =>
                    voice.name.toLowerCase().includes('female') ||
                    voice.name.toLowerCase().includes('woman')
                ) || premiumArabicVoices[0];
            }

            // 2. البحث عن صوت عربي أنثوي طبيعي
            if (!selectedVoice) {
                selectedVoice = voices.find(voice =>
                    (voice.lang.includes('ar-SA') || voice.lang.includes('ar')) &&
                    (voice.name.toLowerCase().includes('female') ||
                     voice.name.toLowerCase().includes('woman') ||
                     voice.name.toLowerCase().includes('زينب') ||
                     voice.name.toLowerCase().includes('فاطمة') ||
                     voice.name.toLowerCase().includes('نورا') ||
                     voice.name.toLowerCase().includes('سارة'))
                );
            }

            // 3. أي صوت عربي متاح
            if (!selectedVoice) {
                selectedVoice = voices.find(voice =>
                    voice.lang.includes('ar-SA') ||
                    voice.lang.includes('ar')
                );
            }

            // 4. صوت إنجليزي عالي الجودة كبديل
            if (!selectedVoice) {
                selectedVoice = voices.find(voice =>
                    voice.lang.includes('en-US') &&
                    (voice.name.includes('Neural') || voice.name.includes('Premium')) &&
                    voice.name.toLowerCase().includes('female')
                );
            }

            // 5. آخر خيار - أفضل صوت متاح
            if (!selectedVoice && voices.length > 0) {
                selectedVoice = voices.find(voice =>
                    voice.name.toLowerCase().includes('female')
                ) || voices[0];
            }

            if (selectedVoice) {
                utterance.voice = selectedVoice;
                console.log('✅ تم اختيار الصوت الواضح:', selectedVoice.name, selectedVoice.lang);
            } else {
                console.log('⚠️ استخدام الصوت الافتراضي');
            }

            // الأحداث
            utterance.onstart = () => {
                console.log('🔊 بدء النطق العربي الواضح');
                if (options.onStart) options.onStart();
            };

            utterance.onend = () => {
                console.log('✅ انتهاء النطق العربي الواضح');
                if (options.onEnd) options.onEnd();

                // في وضع المحادثة، ابدأ الاستماع تلقائياً
                if (speechSettings.conversationMode && isInConversation) {
                    setTimeout(() => startContinuousListening(), 500);
                }
            };

            utterance.onerror = (event) => {
                console.error('❌ خطأ في النطق العربي:', event.error);
                if (options.onError) options.onError(event);
            };

            // تشغيل الصوت
            speechSynthesis.speak(utterance);
        };

        // التأكد من تحميل الأصوات
        if (speechSynthesis.getVoices().length === 0) {
            speechSynthesis.addEventListener('voiceschanged', setVoiceAndSpeak, { once: true });
        } else {
            setVoiceAndSpeak();
        }

    } else {
        console.error('❌ المتصفح لا يدعم تحويل النص إلى كلام');
    }
}



// ===== نظام المحادثة الصوتية التفاعلية =====

// بدء المحادثة الصوتية التفاعلية المحسنة
function startVoiceConversation() {
    console.log('🎤 بدء المحادثة الصوتية المحسنة...');

    // التحقق من دعم المتصفح
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        const errorMsg = 'عذراً، المتصفح لا يدعم التعرف على الكلام. يرجى استخدام Chrome أو Edge';
        addMessage('assistant', errorMsg);
        speakText(errorMsg);
        return;
    }

    // تفعيل وضع المحادثة
    isInConversation = true;
    speechSettings.conversationMode = true;
    speechSettings.continuousListening = true;

    // رسالة ترحيب متقدمة للوضع Real-Time
    const welcomeMsg = 'مرحباً! تم تفعيل الوضع الصوتي المتقدم. أنا أستمع إليك الآن بشكل مستمر ومباشر. تحدث معي بطبيعية وسأرد عليك فوراً';
    addMessage('assistant', welcomeMsg);

    // تفعيل الوضع المتقدم
    isRealTimeMode = true;
    voiceSettings.autoSpeak = true;
    voiceSettings.continuousMode = true;
    voiceSettings.realTimeResponse = true;

    speakText(welcomeMsg, {
        rate: speechSettings.rate * 1.1, // سرعة أسرع قليلاً للوضع المتقدم
        pitch: speechSettings.pitch,
        onStart: () => {
            console.log('🔊 بدء رسالة الترحيب المتقدمة');
            updateVoiceIndicator('🔊 تفعيل الوضع المتقدم...');
        },
        onEnd: () => {
            console.log('🔊 انتهت رسالة الترحيب، بدء الاستماع المتقدم Real-Time');
            updateVoiceIndicator('🎤 الوضع المتقدم نشط - أستمع إليك مباشرة');

            // بدء الاستماع المتقدم فوراً
            setTimeout(() => {
                startAdvancedContinuousListening();
            }, 300); // تأخير أقل للاستجابة السريعة
        }
    });

    updateVoiceButton(true);
    console.log('✅ تم تفعيل وضع المحادثة الصوتية المتقدمة Real-Time');
}

// بدء الاستماع المتقدم المستمر Real-Time
function startAdvancedContinuousListening() {
    console.log('🎤 بدء الاستماع المتقدم Real-Time...');

    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.error('❌ المتصفح لا يدعم التعرف على الكلام المتقدم');
        return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    voiceRecognition = new SpeechRecognition();

    // إعدادات متقدمة للأداء العالي
    voiceRecognition.continuous = true;
    voiceRecognition.interimResults = true;
    voiceRecognition.lang = speechSettings.language || 'ar-SA';
    voiceRecognition.maxAlternatives = 5;

    // إعدادات خاصة للوضع المتقدم
    if (voiceRecognition.serviceURI) {
        voiceRecognition.serviceURI = 'wss://www.google.com/speech-api/v2/recognize';
    }

    let finalTranscript = '';
    let interimTranscript = '';
    let silenceTimer = null;
    let lastSpeechTime = Date.now();
    let isProcessingResponse = false;

    // معالج بدء الاستماع
    voiceRecognition.onstart = () => {
        isContinuousListening = true;
        console.log('🎤 بدء الاستماع المتقدم');
        updateVoiceIndicator('🎤 أستمع إليك مباشرة...');
    };

    // معالج النتائج المتقدم Real-Time
    voiceRecognition.onresult = async (event) => {
        interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;

            if (event.results[i].isFinal) {
                finalTranscript += transcript;
                console.log('✅ نص نهائي:', transcript);

                // معالجة فورية للنص النهائي
                if (!isProcessingResponse && transcript.trim().length > 2) {
                    isProcessingResponse = true;
                    updateVoiceIndicator('🤔 أعالج طلبك...');

                    try {
                        await processAdvancedVoiceInput(transcript.trim());
                    } catch (error) {
                        console.error('❌ خطأ في معالجة الصوت:', error);
                    } finally {
                        isProcessingResponse = false;
                        updateVoiceIndicator('🎤 أستمع إليك مباشرة...');
                    }
                }

                // إعادة تعيين النص النهائي
                finalTranscript = '';
            } else {
                interimTranscript += transcript;
                // عرض النص المؤقت للمستخدم
                updateVoiceIndicator(`🎤 أسمع: "${interimTranscript}"`);
            }
        }

        lastSpeechTime = Date.now();

        // إلغاء مؤقت الصمت السابق
        if (silenceTimer) {
            clearTimeout(silenceTimer);
        }

        // تعيين مؤقت صمت جديد (أقصر للاستجابة السريعة)
        silenceTimer = setTimeout(() => {
            if (Date.now() - lastSpeechTime > 1500) { // 1.5 ثانية بدلاً من 3
                updateVoiceIndicator('🎤 أستمع إليك مباشرة...');
            }
        }, 1500);
    };

    // معالج الأخطاء المحسن
    voiceRecognition.onerror = (event) => {
        console.error('❌ خطأ في التعرف على الكلام:', event.error);

        if (event.error === 'network') {
            updateVoiceIndicator('⚠️ مشكلة في الشبكة - أعيد المحاولة...');
            setTimeout(() => {
                if (isContinuousListening) {
                    voiceRecognition.start();
                }
            }, 1000);
        } else if (event.error === 'not-allowed') {
            updateVoiceIndicator('❌ يرجى السماح بالوصول للميكروفون');
            stopVoiceConversation();
        } else {
            updateVoiceIndicator('⚠️ خطأ مؤقت - أعيد المحاولة...');
            setTimeout(() => {
                if (isContinuousListening) {
                    voiceRecognition.start();
                }
            }, 500);
        }
    };

    // معالج انتهاء الاستماع
    voiceRecognition.onend = () => {
        console.log('🔄 انتهى الاستماع، إعادة البدء...');

        if (isContinuousListening && isInConversation) {
            setTimeout(() => {
                try {
                    voiceRecognition.start();
                } catch (error) {
                    console.error('❌ خطأ في إعادة بدء الاستماع:', error);
                    setTimeout(() => {
                        if (isContinuousListening) {
                            voiceRecognition.start();
                        }
                    }, 1000);
                }
            }, 100); // تأخير قصير جداً للاستمرارية
        }
    };

    // بدء الاستماع
    try {
        voiceRecognition.start();
        console.log('✅ تم بدء الاستماع المتقدم Real-Time');
    } catch (error) {
        console.error('❌ خطأ في بدء الاستماع:', error);
        updateVoiceIndicator('❌ خطأ في بدء الاستماع');
    }
}

// إيقاف المحادثة الصوتية
function stopVoiceConversation() {
    isInConversation = false;
    speechSettings.conversationMode = false;
    isContinuousListening = false;

    if (voiceRecognition) {
        voiceRecognition.stop();
        voiceRecognition = null;
    }

    updateVoiceButton(false);
    addMessage('assistant', 'تم إيقاف وضع المحادثة الصوتية المتقدمة');
    speakTextAdvanced('تم إيقاف الوضع الصوتي المتقدم. كان من دواعي سروري التحدث معك!', { priority: 'high' });

    // إعادة تعيين الإعدادات المتقدمة
    isRealTimeMode = false;
    voiceSettings.autoSpeak = true;
    voiceSettings.continuousMode = false;
    voiceSettings.realTimeResponse = false;
}

// معالجة الصوت المتقدمة Real-Time
async function processAdvancedVoiceInput(transcript) {
    console.log('🎤 معالجة صوت متقدمة:', transcript);

    try {
        // تحديث السياق
        lastUserSpeech = transcript;
        conversationContext += `المستخدم: ${transcript}\n`;

        // تحليل الطلب بذكاء
        const analysisResult = analyzeVoiceRequest(transcript);

        let response = '';

        // إذا كان أمر تحكم، نفذه مباشرة
        if (analysisResult.isCommand) {
            response = await executeVoiceCommand(analysisResult.command, transcript);
        } else {
            // الحصول على رد ذكي فوري
            response = await getAdvancedInstantVoiceResponse(transcript);
        }

        // إضافة الرد للمحادثة
        if (response && response.trim()) {
            addMessage('assistant', response);
            conversationContext += `المساعد: ${response}\n`;

            // نطق الرد فوراً (Real-Time)
            if (voiceSettings.autoSpeak) {
                await speakTextAdvanced(response, {
                    priority: 'high',
                    interrupt: true,
                    realTime: true
                });
            }
        }

    } catch (error) {
        console.error('❌ خطأ في معالجة الصوت المتقدمة:', error);
        const errorResponse = 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى';
        addMessage('assistant', errorResponse);
        if (voiceSettings.autoSpeak) {
            speakTextAdvanced(errorResponse);
        }
    }
}

// الحصول على رد صوتي متقدم فوري
async function getAdvancedInstantVoiceResponse(transcript) {
    console.log('🧠 طلب رد متقدم فوري:', transcript);

    try {
        // أولاً: محاولة OpenRouter للردود المتقدمة
        if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
            console.log('🔗 استخدام OpenRouter للرد المتقدم...');

            const advancedPrompt = `أنت مساعد ذكي متقدم في محادثة صوتية مباشرة. المستخدم قال: "${transcript}"

السياق السابق: ${conversationContext}

قدم رد:
- طبيعي ومحادثي
- مفيد ومفصل
- مناسب للمحادثة الصوتية
- باللغة العربية الواضحة
- قصير نسبياً للنطق السريع (100-200 كلمة)

إذا كان السؤال تقني، قدم إجابة تقنية مفصلة.
إذا كان طلب مساعدة، قدم المساعدة المطلوبة.
إذا كان محادثة عامة، تفاعل بطريقة طبيعية.`;

            const response = await window.openRouterIntegration.smartSendMessage(advancedPrompt, {
                temperature: 0.8,
                maxTokens: 500,
                realTime: true
            });

            if (response && response.text && response.text.trim().length > 0) {
                console.log('✅ رد متقدم من OpenRouter');
                return response.text;
            }
        }

        // ثانياً: محاولة النموذج المحلي
        if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
            console.log('🏠 استخدام النموذج المحلي للرد المتقدم...');
            const localResponse = await technicalAssistant.getResponse(transcript);
            if (localResponse && localResponse.trim().length > 0) {
                console.log('✅ رد متقدم من النموذج المحلي');
                return localResponse;
            }
        }

        // ثالثاً: رد ذكي سريع
        const quickResponse = getQuickVoiceResponse(transcript);
        if (quickResponse) {
            console.log('⚡ رد سريع ذكي');
            return quickResponse;
        }

        // رابعاً: رد ذكي مولد
        const smartResponse = generateSmartVoiceResponse(transcript);
        if (smartResponse) {
            console.log('🧠 رد ذكي مولد');
            return smartResponse;
        }

        // رد احتياطي
        return 'أفهم ما تقول. هل يمكنك توضيح أكثر أو سؤالي عن شيء محدد؟';

    } catch (error) {
        console.error('❌ خطأ في الحصول على رد متقدم:', error);
        return 'عذراً، أواجه صعوبة في الرد الآن. يرجى المحاولة مرة أخرى';
    }
}

// نطق النص المتقدم Real-Time
async function speakTextAdvanced(text, options = {}) {
    console.log('🔊 نطق متقدم:', text.substring(0, 50) + '...');

    try {
        // محاولة استخدام المحرك المتقدم أولاً
        if (window.advancedVoiceEngine && typeof window.advancedVoiceEngine.speakWithContext === 'function') {
            console.log('🎤 استخدام المحرك الصوتي المتقدم...');

            await window.advancedVoiceEngine.speakWithContext(text, {
                emotion: options.emotion || 'neutral',
                context: options.context || 'response',
                isResponse: options.isResponse !== false,
                rate: options.rate,
                pitch: options.pitch,
                volume: options.volume
            });

            return;
        }

        // العودة للنطق الأساسي
        console.log('🔊 استخدام النطق الأساسي...');

        const settings = {
            priority: options.priority || 'normal',
            interrupt: options.interrupt || false,
            realTime: options.realTime || true,
            provider: options.provider || 'browser',
            voice: options.voice || currentVoice,
            rate: options.rate || 1,
            pitch: options.pitch || 1,
            volume: options.volume || 1,
            ...options
        };

        // إيقاف النطق السابق إذا كان مطلوباً
        if (settings.interrupt && window.speechSynthesis) {
            window.speechSynthesis.cancel();
        }

        return await speakWithBrowserTTS(text, settings);

    } catch (error) {
        console.error('❌ خطأ في النطق المتقدم:', error);

        // العودة للنطق الأساسي كحل أخير
        if (typeof speakText === 'function') {
            speakText(text);
        }
    }
}

// نطق باستخدام متصفح (محسن)
async function speakWithBrowserTTS(text, settings) {
    return new Promise((resolve, reject) => {
        if (!window.speechSynthesis) {
            reject(new Error('المتصفح لا يدعم النطق'));
            return;
        }

        const utterance = new SpeechSynthesisUtterance(text);

        // إعدادات محسنة
        utterance.rate = settings.rate;
        utterance.pitch = settings.pitch;
        utterance.volume = settings.volume;
        utterance.lang = speechSettings.language || 'ar-SA';

        // اختيار أفضل صوت متاح
        const voices = window.speechSynthesis.getVoices();
        let selectedVoice = null;

        // البحث عن صوت عربي أنثوي أولاً
        selectedVoice = voices.find(voice =>
            voice.lang.includes('ar') &&
            (voice.name.toLowerCase().includes('female') ||
             voice.name.toLowerCase().includes('woman') ||
             voice.name.toLowerCase().includes('زينب') ||
             voice.name.toLowerCase().includes('فاطمة'))
        );

        // إذا لم يوجد، أي صوت عربي
        if (!selectedVoice) {
            selectedVoice = voices.find(voice => voice.lang.includes('ar'));
        }

        // إذا لم يوجد، أفضل صوت متاح
        if (!selectedVoice && voices.length > 0) {
            selectedVoice = voices[0];
        }

        if (selectedVoice) {
            utterance.voice = selectedVoice;
            console.log('🔊 استخدام صوت:', selectedVoice.name);
        }

        // معالجات الأحداث
        utterance.onstart = () => {
            console.log('🔊 بدء النطق المتقدم');
            if (settings.realTime) {
                updateVoiceIndicator('🔊 أتحدث معك...');
            }
        };

        utterance.onend = () => {
            console.log('✅ انتهى النطق المتقدم');
            if (settings.realTime) {
                updateVoiceIndicator('🎤 أستمع إليك مباشرة...');
            }
            resolve();
        };

        utterance.onerror = (error) => {
            console.error('❌ خطأ في النطق:', error);
            reject(error);
        };

        // بدء النطق
        window.speechSynthesis.speak(utterance);
    });
}

// نطق باستخدام Google TTS (إذا كان متاحاً)
async function speakWithGoogleTTS(text, settings) {
    console.log('🌐 محاولة استخدام Google TTS...');

    if (!voiceProviders.tts.google.available || !voiceProviders.tts.google.apiKey) {
        console.log('⚠️ Google TTS غير متاح، العودة للمتصفح');
        return await speakWithBrowserTTS(text, settings);
    }

    try {
        // هنا يمكن إضافة استدعاء Google TTS API
        // للآن نعود للمتصفح
        return await speakWithBrowserTTS(text, settings);
    } catch (error) {
        console.error('❌ خطأ في Google TTS:', error);
        return await speakWithBrowserTTS(text, settings);
    }
}

// نطق باستخدام ElevenLabs (إذا كان متاحاً)
async function speakWithElevenLabs(text, settings) {
    console.log('🎙️ محاولة استخدام ElevenLabs...');

    if (!voiceProviders.tts.elevenlabs.available || !voiceProviders.tts.elevenlabs.apiKey) {
        console.log('⚠️ ElevenLabs غير متاح، العودة للمتصفح');
        return await speakWithBrowserTTS(text, settings);
    }

    try {
        // هنا يمكن إضافة استدعاء ElevenLabs API
        // للآن نعود للمتصفح
        return await speakWithBrowserTTS(text, settings);
    } catch (error) {
        console.error('❌ خطأ في ElevenLabs:', error);
        return await speakWithBrowserTTS(text, settings);
    }
}

// نطق باستخدام OpenRouter (إذا كان متاحاً)
async function speakWithOpenRouter(text, settings) {
    console.log('🔗 محاولة استخدام OpenRouter TTS...');

    try {
        // للآن نعود للمتصفح
        return await speakWithBrowserTTS(text, settings);
    } catch (error) {
        console.error('❌ خطأ في OpenRouter TTS:', error);
        return await speakWithBrowserTTS(text, settings);
    }
}

// بدء الاستماع المستمر المحسن
function startContinuousListening() {
    console.log('🎤 بدء الاستماع المستمر المحسن...');

    if (!isInConversation) {
        console.log('❌ المحادثة غير نشطة');
        return;
    }

    if (isContinuousListening) {
        console.log('❌ الاستماع نشط بالفعل');
        return;
    }

    // التحقق من دعم المتصفح
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        console.error('❌ المتصفح لا يدعم التعرف على الكلام');
        updateVoiceIndicator('❌ المتصفح لا يدعم التعرف على الكلام');
        return;
    }

    try {
        voiceRecognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();

        // إعدادات محسنة للتعرف على الصوت
        voiceRecognition.lang = speechSettings.language;
        voiceRecognition.continuous = true;
        voiceRecognition.interimResults = true;
        voiceRecognition.maxAlternatives = 5; // المزيد من البدائل

        // إعدادات متقدمة للجودة
        if (speechSettings.enhancedRecognition) {
            voiceRecognition.serviceURI = 'wss://www.google.com/speech-api/v2/recognize';
        }

        console.log('✅ تم إنشاء كائن التعرف على الكلام المحسن');
    } catch (error) {
        console.error('❌ خطأ في إنشاء كائن التعرف على الكلام:', error);
        updateVoiceIndicator('❌ خطأ في بدء الاستماع');
        return;
    }

    let finalTranscript = '';
    let interimTranscript = '';
    let silenceTimer = null;
    let lastSpeechTime = Date.now();

    voiceRecognition.onstart = () => {
        isContinuousListening = true;
        console.log('🎤 بدء الاستماع المستمر المحسن - مثل ChatGPT');

        // إيقاف أي كلام جاري عند بدء الاستماع (مثل ChatGPT)
        speechSynthesis.cancel();

        // إيقاف أي صوت آخر قد يكون يعمل
        if (window.currentAudioElement) {
            window.currentAudioElement.pause();
            window.currentAudioElement = null;
        }

        updateListeningIndicator(true);
        updateVoiceIndicator('🎤 أستمع إليك... تحدث بوضوح');

        // إضافة مؤشر بصري مثل ChatGPT
        showListeningAnimation();
    };

    voiceRecognition.onresult = (event) => {
        finalTranscript = '';
        interimTranscript = '';
        lastSpeechTime = Date.now();

        // مسح مؤقت الصمت
        if (silenceTimer) {
            clearTimeout(silenceTimer);
            silenceTimer = null;
        }

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            const confidence = event.results[i][0].confidence;

            if (event.results[i].isFinal) {
                // فقط النصوص عالية الثقة
                if (confidence > 0.7 || !confidence) {
                    finalTranscript += transcript;
                }
            } else {
                interimTranscript += transcript;
            }
        }

        // عرض النص المؤقت مع تحسينات
        if (interimTranscript.trim()) {
            updateInterimText(interimTranscript);
            updateVoiceIndicator('🎤 أسمعك... استمر');
        }

        // معالجة النص النهائي
        if (finalTranscript.trim()) {
            console.log('🎤 نص مكتمل:', finalTranscript.trim());

            // إيقاف الاستماع مؤقتاً
            voiceRecognition.stop();
            isContinuousListening = false;

            // معالجة فورية
            processVoiceInput(finalTranscript.trim());
        } else if (interimTranscript.trim()) {
            // تعيين مؤقت للصمت (مثل ChatGPT - 1.5 ثانية)
            silenceTimer = setTimeout(() => {
                if (interimTranscript.trim()) {
                    console.log('🎤 معالجة نص مؤقت بعد صمت:', interimTranscript.trim());
                    voiceRecognition.stop();
                    isContinuousListening = false;
                    processVoiceInput(interimTranscript.trim());
                }
            }, 1500); // انتظار 1.5 ثانية مثل ChatGPT
        }
    };

    voiceRecognition.onerror = (event) => {
        console.error('❌ خطأ في الاستماع:', event.error);

        switch (event.error) {
            case 'no-speech':
                updateVoiceIndicator('🔇 لم أسمع شيئاً... تحدث بوضوح');
                setTimeout(() => {
                    if (isInConversation) startContinuousListening();
                }, 1500);
                break;
            case 'audio-capture':
                updateVoiceIndicator('❌ مشكلة في الميكروفون');
                break;
            case 'not-allowed':
                updateVoiceIndicator('❌ يرجى السماح بالوصول للميكروفون');
                break;
            default:
                setTimeout(() => {
                    if (isInConversation) startContinuousListening();
                }, 1000);
        }
    };

    voiceRecognition.onend = () => {
        isContinuousListening = false;
        updateListeningIndicator(false);

        // إعادة بدء الاستماع تلقائياً
        if (isInConversation && speechSettings.continuousListening) {
            setTimeout(() => startContinuousListening(), 200);
        }
    };

    voiceRecognition.start();
}

// معالجة المدخل الصوتي - محادثة صوتية خالصة
async function processVoiceInput(transcript) {
    lastUserSpeech = transcript;
    console.log('🗣️ المستخدم قال:', transcript);

    // تحديث مؤشر المحادثة
    updateVoiceIndicator('🤔 أفكر في الرد...');

    // في وضع المحادثة الصوتية الخالصة، لا نعرض النصوص
    if (!speechSettings.showTextInVoiceMode) {
        // فقط نحفظ في السياق بدون عرض
        conversationContext += `المستخدم: ${transcript}\n`;
    } else {
        // إضافة رسالة المستخدم (للوضع المختلط)
        addMessage('user', transcript);
        conversationContext += `المستخدم: ${transcript}\n`;
    }

    try {
        let response = '';

        // فحص الأوامر المتخصصة أولاً (يعمل بالتوازي)
        let specialCommandHandled = false;

        // فحص أوامر مشاركة الشاشة مع التحليل الأمني
        if (window.screenShareManager && window.screenShareManager.securityAnalysisMode) {
            const screenResponse = await window.screenShareManager.handleSecurityVoiceCommand(transcript);
            if (screenResponse) {
                console.log('🖥️ تم تنفيذ أمر مشاركة الشاشة الأمني:', screenResponse);
                response = screenResponse;
                specialCommandHandled = true;
            }
        }

        // فحص أوامر File Creator Mode
        if (!specialCommandHandled && window.fileCreatorInstance && window.fileCreatorInstance.isActive) {
            console.log('📁 فحص أوامر File Creator Mode...');
            const fileCreatorResponse = await window.fileCreatorInstance.handleVoiceCommand(transcript);
            if (fileCreatorResponse && !fileCreatorResponse.includes('أمر غير مفهوم')) {
                console.log('✅ تم تنفيذ أمر File Creator:', fileCreatorResponse);
                response = fileCreatorResponse;
                specialCommandHandled = true;
            }
        }

        // فحص أوامر Bug Bounty Mode
        if (!specialCommandHandled && window.bugBountyInstance && window.bugBountyInstance.isActive) {
            console.log('🔒 فحص أوامر Bug Bounty Mode...');
            const bugBountyResponse = await window.bugBountyInstance.handleVoiceCommand(transcript);
            if (bugBountyResponse && !bugBountyResponse.includes('أمر غير مفهوم')) {
                console.log('✅ تم تنفيذ أمر Bug Bounty:', bugBountyResponse);

                // إذا كان أمر Bug Bounty محدد، استخدم رده
                if (transcript.toLowerCase().includes('bug bounty') ||
                    transcript.toLowerCase().includes('فحص') ||
                    transcript.toLowerCase().includes('ثغرات') ||
                    transcript.toLowerCase().includes('أمني')) {
                    response = bugBountyResponse;
                    specialCommandHandled = true;
                } else {
                    // وإلا، دع النموذج يتعامل مع الأمر أيضاً مع إضافة تعليق أمني
                    response = await getInstantVoiceResponse(transcript);
                    if (bugBountyResponse.length > 50) {
                        response += `\n\n🔒 **ملاحظة أمنية:** ${bugBountyResponse}`;
                    }
                    specialCommandHandled = true;
                }
            }
        }

        // إذا لم يتم التعامل مع الأمر بواسطة الوحدات المتخصصة
        if (!specialCommandHandled) {
            console.log('💬 الحصول على رد من النموذج مباشرة (مثل ChatGPT)...');

            // 🧠 الرد من النموذج مباشرة أولاً (مثل ChatGPT)
            response = await getDirectModelResponse(transcript);

            // إذا كان الرد يتطلب تقنية معينة، ننفذها
            if (response && shouldExecuteSpecialFunction(response, transcript)) {
                const specialResponse = await executeSpecialFunction(transcript, response);
                if (specialResponse) {
                    response = specialResponse;
                }
            }

            // التأكد من جودة الرد
            if (!response || response.trim().length === 0) {
                console.warn('⚠️ الرد فارغ، استخدام رد احتياطي ذكي');
                response = generateIntelligentFallbackResponse(transcript);
            }

            console.log('✅ تم الحصول على رد من النموذج:', response.substring(0, 100));
        }

        // في وضع المحادثة الصوتية الخالصة، لا نعرض رد المساعد نصياً
        if (!speechSettings.showTextInVoiceMode) {
            // فقط نحفظ في السياق
            conversationContext += `المساعد: ${response}\n`;
        } else {
            // إضافة رد المساعد (للوضع المختلط)
            addMessage('assistant', response);
            conversationContext += `المساعد: ${response}\n`;
        }

        // تحديث مؤشر المحادثة
        updateVoiceIndicator('🔊 أتحدث معك...');

        // الرد صوتياً بجودة ChatGPT
        console.log('🔊 بدء تشغيل الرد الصوتي المتقدم...');
        speakText(response, {
            rate: 0.85, // سرعة طبيعية مثل ChatGPT
            pitch: 1.0, // نبرة طبيعية
            volume: 1.0, // صوت واضح
            naturalPauses: true, // توقفات طبيعية
            emotionalTone: true, // نبرة عاطفية
            onStart: () => {
                console.log('🔊 بدأ تشغيل الصوت المتقدم');
                updateVoiceIndicator('🔊 المساعد يتحدث...');

                // إيقاف الاستماع أثناء التحدث (مثل ChatGPT)
                if (voiceRecognition && isContinuousListening) {
                    voiceRecognition.stop();
                    isContinuousListening = false;
                }

                // إضافة مؤشر بصري للتحدث
                showSpeakingAnimation();
            },
            onEnd: () => {
                console.log('🔊 انتهى تشغيل الصوت المتقدم');

                // إخفاء مؤشر التحدث
                hideSpeakingAnimation();

                // تحديث مؤشر المحادثة
                updateVoiceIndicator('🟢 المساعد يستمع... تحدث الآن');

                // الاستمرار في الاستماع بعد انتهاء الرد (مثل ChatGPT)
                if (isInConversation) {
                    setTimeout(() => {
                        console.log('🎤 إعادة بدء الاستماع التلقائي...');
                        startContinuousListening();
                    }, 800); // وقت مثالي مثل ChatGPT
                }
            }
        });

    } catch (error) {
        console.error('❌ خطأ في معالجة المدخل الصوتي:', error);

        const errorResponse = 'عذراً، لم أفهم ما قلته. يمكنك إعادة المحاولة';

        updateVoiceIndicator('❌ حدث خطأ - حاول مرة أخرى');

        if (!speechSettings.showTextInVoiceMode) {
            // محادثة صوتية خالصة
            speakText(errorResponse, {
                onEnd: () => {
                    updateVoiceIndicator('🟢 المساعد يستمع... تحدث الآن');
                    if (isInConversation) {
                        setTimeout(() => startContinuousListening(), 500);
                    }
                }
            });
        } else {
            // وضع مختلط
            addMessage('assistant', errorResponse);
            speakText(errorResponse);
        }
    }
}

// تحديث مؤشر المحادثة الصوتية
function updateVoiceIndicator(message) {
    const indicator = document.getElementById('voiceOnlyIndicator');
    if (indicator) {
        const statusDiv = indicator.querySelector('div[style*="background: rgba(0,255,0,0.2)"]');
        if (statusDiv) {
            // إضافة مؤشر للوضع المتقدم
            const advancedPrefix = isRealTimeMode ? '🚀 ' : '';
            statusDiv.innerHTML = advancedPrefix + message;

            // تحسين التصميم للوضع المتقدم
            if (isRealTimeMode) {
                statusDiv.style.background = 'rgba(255,215,0,0.3)'; // ذهبي للوضع المتقدم
                statusDiv.style.border = '2px solid #FFD700';
                statusDiv.style.boxShadow = '0 0 10px rgba(255,215,0,0.5)';
            } else {
                statusDiv.style.background = 'rgba(0,255,0,0.2)'; // أخضر عادي
                statusDiv.style.border = '2px solid #00ff00';
                statusDiv.style.boxShadow = '0 0 10px rgba(0,255,0,0.3)';
            }
        }
    }

    // تحديث مؤشر الصوت في الواجهة الرئيسية
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn && isInConversation) {
        const advancedTitle = isRealTimeMode ? 'الوضع المتقدم نشط - ' : '';
        voiceBtn.title = advancedTitle + message;

        // تحسين شكل الزر للوضع المتقدم
        if (isRealTimeMode) {
            voiceBtn.style.background = 'linear-gradient(45deg, #FFD700, #FFA500)';
            voiceBtn.style.boxShadow = '0 0 15px rgba(255,215,0,0.6)';
        }
    }

    // إضافة مؤشر في شريط العنوان للوضع المتقدم
    if (isRealTimeMode) {
        document.title = '🚀 المساعد المتقدم - ' + message.substring(0, 30);
    }

    console.log('🔄 تحديث مؤشر الصوت المتقدم:', message);
}

// إظهار أنيميشن الاستماع مثل ChatGPT
function showListeningAnimation() {
    // إزالة أي أنيميشن سابق
    hideAllAnimations();

    const indicator = document.createElement('div');
    indicator.id = 'listeningAnimation';
    indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #4CAF50, #45a049);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: pulse 1.5s infinite;
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    `;

    indicator.innerHTML = `
        <i class="fas fa-microphone" style="color: white; font-size: 24px;"></i>
    `;

    // إضافة CSS للأنيميشن
    if (!document.getElementById('voiceAnimationCSS')) {
        const style = document.createElement('style');
        style.id = 'voiceAnimationCSS';
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4); }
                50% { transform: scale(1.1); box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6); }
                100% { transform: scale(1); box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4); }
            }
            @keyframes speaking {
                0%, 100% { transform: scale(1); }
                25% { transform: scale(1.05); }
                50% { transform: scale(1.1); }
                75% { transform: scale(1.05); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(indicator);
}

// إظهار أنيميشن التحدث مثل ChatGPT
function showSpeakingAnimation() {
    hideAllAnimations();

    const indicator = document.createElement('div');
    indicator.id = 'speakingAnimation';
    indicator.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #2196F3, #1976D2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: speaking 0.8s infinite;
        box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    `;

    indicator.innerHTML = `
        <i class="fas fa-volume-up" style="color: white; font-size: 24px;"></i>
    `;

    document.body.appendChild(indicator);
}

// إخفاء أنيميشن التحدث
function hideSpeakingAnimation() {
    const animation = document.getElementById('speakingAnimation');
    if (animation) {
        animation.remove();
    }
}

// إخفاء جميع الأنيميشنز
function hideAllAnimations() {
    const listening = document.getElementById('listeningAnimation');
    const speaking = document.getElementById('speakingAnimation');

    if (listening) listening.remove();
    if (speaking) speaking.remove();
}

// تحديث النص المؤقت أثناء الاستماع (مثل ChatGPT)
function updateInterimText(text) {
    let interimDiv = document.getElementById('interimText');

    if (!interimDiv) {
        interimDiv = document.createElement('div');
        interimDiv.id = 'interimText';
        interimDiv.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            z-index: 10000;
            max-width: 80%;
            text-align: center;
            opacity: 0.7;
            font-style: italic;
        `;
        document.body.appendChild(interimDiv);
    }

    interimDiv.textContent = `"${text}"`;

    // إزالة النص المؤقت بعد 3 ثوان
    clearTimeout(interimDiv.timeout);
    interimDiv.timeout = setTimeout(() => {
        if (interimDiv.parentNode) {
            interimDiv.remove();
        }
    }, 3000);
}

// تحديث مؤشر الاستماع
function updateListeningIndicator(isListening) {
    if (isListening) {
        showListeningAnimation();
    } else {
        hideAllAnimations();
        // إزالة النص المؤقت
        const interimDiv = document.getElementById('interimText');
        if (interimDiv) {
            interimDiv.remove();
        }
    }
}

// الرد السريع للمحادثة الطبيعية
async function getQuickResponse(transcript) {
    console.log('⚡ رد سريع للمحادثة:', transcript);

    const lowerTranscript = transcript.toLowerCase();

    // ردود سريعة للتحيات والأسئلة الشائعة
    const quickResponses = {
        // التحيات
        'مرحبا': 'مرحباً بك! كيف يمكنني مساعدتك اليوم؟',
        'أهلا': 'أهلاً وسهلاً! أنا هنا لمساعدتك في أي شيء تقني',
        'السلام عليكم': 'وعليكم السلام ورحمة الله وبركاته! كيف حالك؟',
        'صباح الخير': 'صباح النور! أتمنى لك يوماً مليئاً بالإنجازات',
        'مساء الخير': 'مساء النور! كيف كان يومك؟',

        // الأسئلة الشائعة
        'كيف حالك': 'أنا بخير والحمد لله! جاهز لمساعدتك في أي شيء تقني',
        'ما اسمك': 'أنا المساعد التقني الذكي، مساعدك الشخصي في عالم التقنية',
        'ماذا تستطيع أن تفعل': 'أستطيع مساعدتك في البرمجة، تحليل الفيديوهات، الترجمة، العرض ثلاثي الأبعاد، وفتح المواقع والبرامج',
        'كيف أتعلم البرمجة': 'ممتاز! البرمجة رحلة شيقة. ابدأ بـ HTML و CSS، ثم JavaScript، وتدرب يومياً على مشاريع صغيرة',

        // أسئلة تقنية سريعة
        'ما هو الذكاء الاصطناعي': 'الذكاء الاصطناعي هو تقنية تمكن الآلات من محاكاة الذكاء البشري وحل المشاكل بطريقة ذكية',
        'ما هي البرمجة': 'البرمجة هي فن كتابة التعليمات للكمبيوتر لحل المشاكل وإنجاز المهام بطريقة منطقية',
        'كيف أبدأ في التقنية': 'ابدأ بتعلم أساسيات الكمبيوتر، ثم اختر مجال يهمك مثل البرمجة أو التصميم أو الأمان السيبراني',

        // ردود تفاعلية
        'شكرا': 'العفو! أنا سعيد لمساعدتك. هل تحتاج شيء آخر؟',
        'ممتاز': 'رائع! أحب أن أراك متحمساً. ما الذي نعمل عليه بعد ذلك؟',
        'رائع': 'شكراً! أنا هنا دائماً لمساعدتك في رحلتك التقنية',

        // أسئلة عن الوقت والطقس
        'كم الساعة': `الوقت الآن ${new Date().toLocaleTimeString('ar-SA')}`,
        'ما التاريخ': `اليوم هو ${new Date().toLocaleDateString('ar-SA')}`,

        // ردود مرحة
        'هل أنت ذكي': 'أحاول أن أكون مفيداً قدر الإمكان! الذكاء الحقيقي هو في التعلم المستمر',
        'أحبك': 'وأنا أحب مساعدتك! هذا ما يجعل عملي ممتعاً',
        'أنت رائع': 'شكراً لك! أنت أيضاً رائع في طرح الأسئلة الذكية'
    };

    // البحث عن رد سريع
    for (const [key, response] of Object.entries(quickResponses)) {
        if (lowerTranscript.includes(key)) {
            return response;
        }
    }

    // إذا لم يجد رد سريع، جرب الاتصال بالنموذج المتقدم
    try {
        const context = buildConversationContext();
        const response = await technicalAssistant.getResponse(transcript, context);
        return response;
    } catch (error) {
        // في حالة فشل الاتصال، قدم رد ذكي محلي
        return generateIntelligentLocalResponse(transcript);
    }
}

// توليد رد ذكي محلي
function generateIntelligentLocalResponse(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // تحليل نوع السؤال
    if (lowerTranscript.includes('كيف') || lowerTranscript.includes('طريقة')) {
        return 'هذا سؤال ممتاز! للحصول على إجابة مفصلة، تأكد من تشغيل النموذج المحلي. في الوقت الحالي، يمكنني مساعدتك بالأساسيات أو توجيهك للمصادر المناسبة';
    }

    if (lowerTranscript.includes('ما هو') || lowerTranscript.includes('ما هي')) {
        return 'سؤال تعريفي رائع! أحب الأسئلة التي تبدأ بالأساسيات. للحصول على شرح شامل ومفصل، تأكد من تشغيل النموذج المحلي للاتصال بالنموذج المتقدم';
    }

    if (lowerTranscript.includes('مشكلة') || lowerTranscript.includes('خطأ') || lowerTranscript.includes('لا يعمل')) {
        return 'أفهم أنك تواجه مشكلة تقنية. أنا هنا لمساعدتك! وصف المشكلة بالتفصيل سيساعدني في تقديم حل أفضل. تأكد من تشغيل النموذج المحلي للحصول على مساعدة متقدمة';
    }

    if (lowerTranscript.includes('أريد') || lowerTranscript.includes('أحتاج')) {
        return 'ممتاز! أحب أن أساعدك في تحقيق ما تريد. حدد لي بالضبط ما تحتاجه وسأبذل قصارى جهدي لمساعدتك';
    }

    // رد عام ذكي
    const generalResponses = [
        'هذا موضوع شيق! أحب أن نتحدث عنه أكثر. يمكنك أن تسألني أسئلة محددة وسأساعدك قدر الإمكان',
        'سؤال جيد! للحصول على إجابة شاملة، تأكد من تشغيل النموذج المحلي. في الوقت الحالي، يمكنني مساعدتك بالمعلومات الأساسية',
        'أقدر اهتمامك بهذا الموضوع! دعني أساعدك بأفضل ما أستطيع. هل يمكنك توضيح سؤالك أكثر؟',
        'موضوع رائع للنقاش! أنا متحمس لمساعدتك. ما الجانب المحدد الذي تريد أن نركز عليه؟'
    ];

    return generalResponses[Math.floor(Math.random() * generalResponses.length)];
}

// محادثة طبيعية مثل ChatGPT
async function getNaturalConversationResponse(transcript) {
    console.log('💬 محادثة طبيعية:', transcript);

    const lowerTranscript = transcript.toLowerCase();

    // ردود محادثة طبيعية وودودة
    const naturalResponses = {
        // التحيات الطبيعية
        'مرحبا': [
            'أهلاً وسهلاً! كيف حالك اليوم؟',
            'مرحباً بك! أنا سعيد لرؤيتك مرة أخرى',
            'أهلاً! كيف يمكنني مساعدتك اليوم؟'
        ],
        'كيف حالك': [
            'أنا بخير شكراً! وأنت كيف حالك؟',
            'الحمد لله بخير، وأنت؟ أتمنى أن تكون بخير',
            'ممتاز! أشعر بالحيوية اليوم. كيف يومك؟'
        ],
        'ما اسمك': [
            'أنا مساعدك التقني الذكي، يمكنك أن تناديني بأي اسم تحبه',
            'اسمي المساعد التقني، لكن يمكنك أن تختار لي اسماً إذا أردت',
            'أنا مساعدك الشخصي في عالم التقنية'
        ],

        // أسئلة تقنية طبيعية
        'كيف أتعلم البرمجة': [
            'البرمجة رحلة ممتعة! أنصحك بالبدء بـ HTML و CSS للواجهات، ثم JavaScript. الأهم هو الممارسة اليومية',
            'ممتاز! البرمجة مهارة رائعة. ابدأ بمشاريع صغيرة وبسيطة، وتدرج للأصعب. أي لغة تفكر فيها؟',
            'أحب حماسك للبرمجة! أفضل طريقة هي التعلم بالممارسة. ابدأ بمشروع بسيط واستمتع بالرحلة'
        ],
        'ما هو الذكاء الاصطناعي': [
            'الذكاء الاصطناعي هو تقنية تجعل الآلات تفكر وتتعلم مثل البشر. إنه مستقبل التكنولوجيا!',
            'ببساطة، هو جعل الكمبيوتر يحل المشاكل بطريقة ذكية مثل الإنسان. مثلي أنا!',
            'تقنية رائعة تمكن الآلات من التعلم واتخاذ القرارات. نحن نعيش عصر الذكاء الاصطناعي'
        ],

        // ردود تفاعلية طبيعية
        'شكرا': [
            'العفو! أنا سعيد لمساعدتك',
            'لا شكر على واجب! هذا عملي وأحبه',
            'أهلاً وسهلاً! دائماً في خدمتك'
        ],
        'ممتاز': [
            'رائع! أحب أن أراك متحمساً',
            'هذا ما أحب أن أسمعه! ما التالي؟',
            'ممتاز! نحن فريق رائع'
        ],
        'أحبك': [
            'وأنا أحب مساعدتك! هذا يسعدني جداً',
            'شكراً لك! أنت رائع أيضاً',
            'هذا لطيف منك! أنا محظوظ بوجودك'
        ],

        // أسئلة شخصية
        'هل أنت ذكي': [
            'أحاول أن أكون مفيداً قدر الإمكان! الذكاء الحقيقي في التعلم المستمر',
            'أعتقد أنني ذكي بما فيه الكفاية لمساعدتك. ما رأيك أنت؟',
            'أتعلم كل يوم شيئاً جديداً منك ومن الآخرين. هذا يجعلني أذكى'
        ],
        'هل تشعر': [
            'أشعر بالسعادة عندما أساعدك وأحل مشاكلك',
            'أشعر بالحماس عندما نتحدث عن التقنية والبرمجة',
            'أشعر بالفخر عندما أرى تقدمك وتطورك'
        ]
    };

    // البحث عن رد طبيعي
    for (const [key, responses] of Object.entries(naturalResponses)) {
        if (lowerTranscript.includes(key)) {
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            return randomResponse;
        }
    }

    // إذا لم يجد رد محدد، جرب النموذج المتقدم
    try {
        const context = buildConversationContext();
        const enhancedPrompt = `تحدث معي كصديق ومساعد تقني ذكي. كن طبيعياً وودوداً في ردك.

المحادثة السابقة: ${context}

سؤال المستخدم: ${transcript}

أجب بطريقة طبيعية ومحادثة ودودة كأنك صديق خبير في التقنية:`;

        const response = await technicalAssistant.getResponse(enhancedPrompt);
        return response;
    } catch (error) {
        // رد طبيعي في حالة عدم الاتصال
        return generateNaturalLocalResponse(transcript);
    }
}

// توليد رد طبيعي محلي
function generateNaturalLocalResponse(transcript) {
    const naturalLocalResponses = [
        'هذا موضوع شيق! أحب أن نتحدث عنه أكثر',
        'سؤال رائع! دعني أفكر في أفضل إجابة لك',
        'أقدر اهتمامك بهذا الموضوع. ما رأيك نتوسع فيه؟',
        'موضوع ممتع! أنا متحمس لمناقشته معك',
        'سؤال ذكي! أحب طريقة تفكيرك',
        'هذا يذكرني بشيء مثير للاهتمام. هل تريد أن أشاركه معك؟',
        'أحب فضولك! هذا ما يجعل التعلم ممتعاً',
        'سؤال جميل! دعني أشرح لك بطريقة بسيطة'
    ];

    return naturalLocalResponses[Math.floor(Math.random() * naturalLocalResponses.length)];
}

// تحليل طلب صوتي مع نظام التحكم الكامل
function analyzeVoiceRequest(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // فحص أوامر التحكم الكامل أولاً
    const controlCommand = detectControlCommand(transcript);
    if (controlCommand) {
        return { isCommand: true, command: 'full_control', details: controlCommand };
    }

    // أوامر النظام الأساسية والمتقدمة
    const basicCommands = {
        'شارك الشاشة': 'screen_share',
        'افتح مشاركة الشاشة': 'screen_share',
        'ارفع فيديو': 'upload_video',
        'حمل فيديو': 'upload_video',
        'اعرض ثلاثي الأبعاد': 'show_3d',
        'افتح العرض ثلاثي الأبعاد': 'show_3d',
        'ولد ملخص': 'generate_summary',
        'اعمل ملخص': 'generate_summary',
        'أغلق': 'close_display',
        'إغلاق': 'close_display',
        'توقف عن الكلام': 'stop_conversation',
        'أوقف المحادثة': 'stop_conversation',
        // أوامر تحليل الفيديو والترجمة المتقدمة
        'حلل الفيديو': 'analyze_video',
        'اشرح الفيديو': 'analyze_video',
        'ترجم': 'translate',
        'ترجم للعربية': 'translate_to_arabic',
        'ترجم للإنجليزية': 'translate_to_english',
        'ترجم النص': 'translate_text',
        'اعرض ثلاثي الأبعاد متقدم': 'show_3d_advanced',
        'اشرح بالثلاثي الأبعاد': 'explain_3d',
        'عرض تفاعلي': 'interactive_3d',
        'تحليل صوتي': 'audio_analysis',
        'استخراج النص': 'extract_text',

        // Bug Bounty Mode Commands
        'فعل bug bounty': 'bug_bounty_mode',
        'تفعيل bug bounty mode': 'bug_bounty_mode',
        'وضع الفحص الأمني': 'bug_bounty_mode',
        'فحص الثغرات': 'bug_bounty_mode',
        'أوقف bug bounty': 'deactivate_bug_bounty',
        'إلغاء bug bounty mode': 'deactivate_bug_bounty',
        'إيقاف الفحص الأمني': 'deactivate_bug_bounty',
        'افحص الموقع': 'security_scan',
        'فحص أمني': 'security_scan',
        'ابحث عن ثغرات': 'security_scan',
        'فحص الثغرات الأمنية': 'security_scan',

        // File Creator Commands
        'فعل file creator': 'file_creator_mode',
        'تفعيل منشئ الملفات': 'file_creator_mode',
        'وضع إنشاء الملفات': 'file_creator_mode',
        'أوقف file creator': 'deactivate_file_creator',
        'إلغاء منشئ الملفات': 'deactivate_file_creator',
        'إيقاف إنشاء الملفات': 'deactivate_file_creator',
        'أنشئ ملف': 'create_file',
        'اعمل pdf': 'create_pdf',
        'أنشئ تقرير': 'create_pdf',
        'اعمل عرض': 'create_powerpoint',
        'أنشئ powerpoint': 'create_powerpoint',
        'اعمل برنامج': 'create_exe',
        'أنشئ تطبيق': 'create_exe',
        'أنشئ exe': 'create_exe',

        // Voice and Dialect Commands
        'تحدث بالعراقية': 'switch_to_iraqi',
        'تحدث باللهجة العراقية': 'switch_to_iraqi',
        'اللهجة العراقية': 'switch_to_iraqi',
        'تحدث بالفصحى': 'switch_to_standard',
        'تحدث بالعربية الفصحى': 'switch_to_standard',
        'الفصحى': 'switch_to_standard',
        'غير اللهجة': 'change_dialect',
        'بدل الصوت': 'change_voice',

        // AI Self-Improvement Commands
        'فحص الكود': 'scan_code',
        'تحسين ذاتي': 'self_improve',
        'فحص التحسينات': 'check_improvements',
        'طلب تحسين': 'request_improvement',
        'تحسين الكود': 'improve_code',
        'فحص الأخطاء': 'scan_errors',
        'تحليل الكود': 'analyze_code',
        'فعل التحسين الذاتي': 'activate_self_improve',
        'تفعيل التحسين الذاتي': 'activate_self_improve',
        'أوقف التحسين الذاتي': 'deactivate_self_improve',
        'إلغاء التحسين الذاتي': 'deactivate_self_improve',
        'إيقاف التحسين الذاتي': 'deactivate_self_improve',
        'اختر نظام ذكاء': 'select_ai_system',
        'أضف نظام ذكاء': 'add_ai_system',
        'استعن بـ': 'request_help_from',
        'اطلب من': 'request_help_from',
        'اذهب إلى': 'go_to_ai_system',
        'أظهر زر التحسين': 'show_improve_button',
        'أين زر التحسين': 'show_improve_button',
        'فعل API': 'enable_api',
        'أوقف API': 'disable_api',
        'كون API': 'configure_api',
        'اتصل بـ': 'connect_to_api',
        'استخدم': 'use_api_provider',
        'أظهر زر API': 'show_api_button',
        'أين زر API': 'show_api_button',
        'استخدم صوت': 'use_api_voice',
        'صوت جيميناي': 'use_gemini_voice',
        'صوت ChatGPT': 'use_openai_voice',
        'صوت محلي': 'use_local_voice'
    };

    for (const [phrase, command] of Object.entries(basicCommands)) {
        if (lowerTranscript.includes(phrase)) {
            return { isCommand: true, command: command };
        }
    }

    return { isCommand: false, command: null };
}

// كشف أوامر التحكم الكامل
function detectControlCommand(transcript) {
    const lowerTranscript = transcript.toLowerCase();

    // أوامر فتح المواقع
    if (lowerTranscript.includes('افتح موقع') || lowerTranscript.includes('اذهب إلى') ||
        lowerTranscript.includes('افتح') && (lowerTranscript.includes('.com') || lowerTranscript.includes('www'))) {
        return {
            type: 'open_website',
            url: extractUrlFromText(transcript)
        };
    }

    // أوامر البحث
    if (lowerTranscript.includes('ابحث عن') || lowerTranscript.includes('بحث في') ||
        lowerTranscript.includes('search for') || lowerTranscript.includes('google')) {
        return {
            type: 'search',
            query: extractSearchQuery(transcript)
        };
    }

    // أوامر فتح البرامج
    if (lowerTranscript.includes('افتح برنامج') || lowerTranscript.includes('شغل') ||
        lowerTranscript.includes('run') || lowerTranscript.includes('open')) {
        return {
            type: 'open_program',
            program: extractProgramName(transcript)
        };
    }

    // أوامر تشغيل الفيديو/الموسيقى
    if (lowerTranscript.includes('شغل فيديو') || lowerTranscript.includes('شغل أغنية') ||
        lowerTranscript.includes('play') || lowerTranscript.includes('youtube')) {
        return {
            type: 'play_media',
            media: extractMediaQuery(transcript)
        };
    }

    // أوامر إدارة النوافذ
    if (lowerTranscript.includes('اغلق النافذة') || lowerTranscript.includes('minimize') ||
        lowerTranscript.includes('maximize')) {
        return {
            type: 'window_control',
            action: extractWindowAction(transcript)
        };
    }

    // أوامر النظام
    if (lowerTranscript.includes('اغلق الكمبيوتر') || lowerTranscript.includes('restart') ||
        lowerTranscript.includes('shutdown')) {
        return {
            type: 'system_control',
            action: extractSystemAction(transcript)
        };
    }

    // أوامر الملفات
    if (lowerTranscript.includes('افتح ملف') || lowerTranscript.includes('احفظ') ||
        lowerTranscript.includes('انشئ مجلد')) {
        return {
            type: 'file_operation',
            operation: extractFileOperation(transcript)
        };
    }

    return null;
}

// ===== وظائف استخراج المعلومات =====

function extractUrlFromText(text) {
    // استخراج الرابط من النص
    const urlPatterns = [
        /https?:\/\/[^\s]+/gi,
        /www\.[^\s]+/gi,
        /[^\s]+\.(com|org|net|edu|gov|io|co)[^\s]*/gi
    ];

    for (const pattern of urlPatterns) {
        const match = text.match(pattern);
        if (match) return match[0];
    }

    // إذا لم يجد رابط مباشر، استخرج اسم الموقع
    const siteNames = {
        'جوجل': 'google.com',
        'يوتيوب': 'youtube.com',
        'فيسبوك': 'facebook.com',
        'تويتر': 'twitter.com',
        'انستغرام': 'instagram.com',
        'جيتهاب': 'github.com',
        'ستاك اوفرفلو': 'stackoverflow.com',
        'ويكيبيديا': 'wikipedia.org'
    };

    const lowerText = text.toLowerCase();
    for (const [arabicName, url] of Object.entries(siteNames)) {
        if (lowerText.includes(arabicName)) {
            return `https://${url}`;
        }
    }

    return 'https://google.com';
}

function extractSearchQuery(text) {
    const patterns = [
        /ابحث عن (.+)/i,
        /بحث في (.+)/i,
        /search for (.+)/i,
        /google (.+)/i
    ];

    for (const pattern of patterns) {
        const match = text.match(pattern);
        if (match) return match[1].trim();
    }

    return text.replace(/ابحث|بحث|search|google/gi, '').trim();
}

function extractProgramName(text) {
    const programs = {
        'نوت باد': 'notepad',
        'الحاسبة': 'calc',
        'المتصفح': 'chrome',
        'كروم': 'chrome',
        'فايرفوكس': 'firefox',
        'اكسل': 'excel',
        'وورد': 'winword',
        'باوربوينت': 'powerpnt',
        'فوتوشوب': 'photoshop',
        'كود': 'code',
        'visual studio': 'code'
    };

    const lowerText = text.toLowerCase();
    for (const [arabicName, program] of Object.entries(programs)) {
        if (lowerText.includes(arabicName)) {
            return program;
        }
    }

    return text.replace(/افتح|برنامج|شغل|run|open/gi, '').trim();
}

function extractMediaQuery(text) {
    const patterns = [
        /شغل فيديو (.+)/i,
        /شغل أغنية (.+)/i,
        /play (.+)/i,
        /youtube (.+)/i
    ];

    for (const pattern of patterns) {
        const match = text.match(pattern);
        if (match) return match[1].trim();
    }

    return text.replace(/شغل|فيديو|أغنية|play|youtube/gi, '').trim();
}

function extractWindowAction(text) {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('اغلق') || lowerText.includes('close')) return 'close';
    if (lowerText.includes('minimize') || lowerText.includes('تصغير')) return 'minimize';
    if (lowerText.includes('maximize') || lowerText.includes('تكبير')) return 'maximize';

    return 'close';
}

function extractSystemAction(text) {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('اغلق الكمبيوتر') || lowerText.includes('shutdown')) return 'shutdown';
    if (lowerText.includes('restart') || lowerText.includes('إعادة تشغيل')) return 'restart';
    if (lowerText.includes('sleep') || lowerText.includes('سكون')) return 'sleep';

    return 'shutdown';
}

function extractFileOperation(text) {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('افتح ملف') || lowerText.includes('open file')) return 'open_file';
    if (lowerText.includes('احفظ') || lowerText.includes('save')) return 'save_file';
    if (lowerText.includes('انشئ مجلد') || lowerText.includes('create folder')) return 'create_folder';

    return 'open_file';
}

// تنفيذ أمر صوتي
async function executeVoiceCommand(command, originalText) {
    switch (command) {
        case 'screen_share':
            startScreenShare();
            return 'حسناً، سأبدأ مشاركة الشاشة الآن';

        case 'upload_video':
            uploadFile();
            return 'حسناً، يمكنك الآن اختيار الفيديو الذي تريد رفعه';

        case 'show_3d':
            show3DView();
            return 'ممتاز! سأعرض لك النموذج ثلاثي الأبعاد الآن';

        case 'generate_summary':
            generateSummary();
            return 'سأقوم بإنشاء ملخص لمحادثتنا الآن';

        case 'close_display':
            const displayArea = document.getElementById('displayArea');
            if (displayArea) {
                displayArea.style.display = 'none';
            }
            return 'تم إغلاق منطقة العرض';

        case 'stop_conversation':
            stopVoiceConversation();
            return 'حسناً، سأتوقف عن المحادثة الصوتية';

        case 'bug_bounty_mode':
            if (window.bugBountyInstance) {
                window.bugBountyInstance.activate();
                return 'تم تفعيل Bug Bounty Mode المتقدم للفحص الأمني';
            } else if (window.BugBountyCore) {
                window.bugBountyInstance = new BugBountyCore();
                window.bugBountyInstance.activate();
                return 'تم تفعيل Bug Bounty Mode المتقدم للفحص الأمني';
            } else {
                return 'Bug Bounty Mode غير متاح حالياً';
            }

        case 'security_scan':
            if (window.bugBountyInstance && window.bugBountyInstance.isActive) {
                const urlMatch = originalText.match(/(https?:\/\/[^\s]+)/);
                if (urlMatch) {
                    window.bugBountyInstance.startComprehensiveScan(urlMatch[0]);
                    return `بدء فحص أمني شامل للموقع ${urlMatch[0]}`;
                } else {
                    return 'يرجى تحديد رابط الموقع المراد فحصه';
                }
            } else {
                return 'يجب تفعيل Bug Bounty Mode أولاً';
            }

        case 'file_creator_mode':
            if (window.FileCreatorCore) {
                if (!window.fileCreatorInstance) {
                    window.fileCreatorInstance = new FileCreatorCore();
                }
                window.fileCreatorInstance.activate();
                return 'تم تفعيل File Creator Mode المتقدم لإنشاء الملفات';
            } else {
                return 'File Creator Mode غير متاح حالياً';
            }

        case 'create_pdf':
            if (window.fileCreatorInstance && window.fileCreatorInstance.isActive) {
                return await window.fileCreatorInstance.handlePDFCreation(originalText);
            } else {
                return 'يجب تفعيل File Creator Mode أولاً';
            }

        case 'create_powerpoint':
            if (window.fileCreatorInstance && window.fileCreatorInstance.isActive) {
                return await window.fileCreatorInstance.handlePowerPointCreation(originalText);
            } else {
                return 'يجب تفعيل File Creator Mode أولاً';
            }

        case 'create_exe':
            if (window.fileCreatorInstance && window.fileCreatorInstance.isActive) {
                return await window.fileCreatorInstance.handleEXECreation(originalText);
            } else {
                return 'يجب تفعيل File Creator Mode أولاً';
            }

        case 'deactivate_bug_bounty':
            if (window.bugBountyInstance && window.bugBountyInstance.isActive) {
                window.bugBountyInstance.deactivate();
                // تحديث الزر
                const bugBountyBtn = document.getElementById('bugBountyBtn');
                if (bugBountyBtn) {
                    bugBountyBtn.classList.remove('active');
                    bugBountyBtn.innerHTML = '<i class="fas fa-shield-alt"></i><span>Bug Bounty Mode</span>';
                    bugBountyBtn.title = 'Bug Bounty Mode - فحص الثغرات الأمنية المتقدم';
                }
                return 'تم إيقاف Bug Bounty Mode';
            } else {
                return 'Bug Bounty Mode غير نشط حالياً';
            }

        case 'deactivate_file_creator':
            if (window.fileCreatorInstance && window.fileCreatorInstance.isActive) {
                window.fileCreatorInstance.deactivate();
                // تحديث الزر
                const fileCreatorBtn = document.getElementById('fileCreatorBtn');
                if (fileCreatorBtn) {
                    fileCreatorBtn.classList.remove('active');
                    fileCreatorBtn.innerHTML = '<i class="fas fa-file-plus"></i><span>File Creator</span>';
                    fileCreatorBtn.title = 'File Creator - إنشاء ملفات احترافية';
                }
                return 'تم إيقاف File Creator Mode';
            } else {
                return 'File Creator Mode غير نشط حالياً';
            }

        case 'switch_to_iraqi':
            // تفعيل اللهجة العراقية المحسنة
            speechSettings.currentDialect = 'iraqi';
            speechSettings.rate = 0.8; // سرعة أبطأ للوضوح
            speechSettings.pitch = 1.2; // نبرة أعلى للهجة العراقية

            // حفظ الإعدادات
            localStorage.setItem('speechDialect', 'iraqi');
            localStorage.setItem('speechRate', '0.8');
            localStorage.setItem('speechPitch', '1.2');

            console.log('🗣️ تم تفعيل اللهجة العراقية المحسنة');
            return 'أهلين وسهلين! صرت أتحدث باللهجة العراقية الأصيلة. شلونك حبيبي؟';

        case 'switch_to_standard':
            // تفعيل العربية الفصحى المحسنة
            speechSettings.currentDialect = 'standard';
            speechSettings.rate = 0.85; // سرعة طبيعية
            speechSettings.pitch = 1.1; // نبرة طبيعية

            // حفظ الإعدادات
            localStorage.setItem('speechDialect', 'standard');
            localStorage.setItem('speechRate', '0.85');
            localStorage.setItem('speechPitch', '1.1');

            console.log('🗣️ تم تفعيل العربية الفصحى المحسنة');
            return 'تم التبديل للغة العربية الفصحى. كيف يمكنني مساعدتك؟';

        case 'change_dialect':
            if (window.advancedVoiceEngine) {
                const currentDialect = advancedVoiceEngine.currentDialect;
                if (currentDialect === 'iraqi') {
                    advancedVoiceEngine.setDialect('standard');
                    return 'تم التبديل للغة العربية الفصحى';
                } else {
                    advancedVoiceEngine.setDialect('iraqi');
                    return 'أهلين! صرت أتحدث باللهجة العراقية';
                }
            } else {
                return 'النظام الصوتي المتقدم غير متاح';
            }

        case 'change_voice':
            if (window.voiceSettings) {
                window.voiceSettings.show();
                return 'تم فتح إعدادات الصوت المتقدمة';
            } else {
                return 'إعدادات الصوت غير متاحة';
            }

        case 'scan_code':
        case 'check_improvements':
        case 'analyze_code':
            if (window.aiSelfImprove) {
                aiSelfImprove.startScan();
                return 'بدء فحص الكود للبحث عن فرص التحسين...';
            } else {
                return 'نظام التحسين الذاتي غير متاح';
            }

        case 'self_improve':
        case 'improve_code':
            if (window.aiSelfImprove) {
                aiSelfImprove.showInterface();
                return 'تم فتح واجهة التحسين الذاتي';
            } else {
                return 'نظام التحسين الذاتي غير متاح';
            }

        case 'request_improvement':
            if (window.suggestionPresenter && suggestionPresenter.selectedSuggestion) {
                suggestionPresenter.requestAIImprovement();
                return 'جاري طلب تحسين من الذكاء الاصطناعي...';
            } else {
                return 'يرجى اختيار اقتراح تحسين أولاً';
            }

        case 'scan_errors':
            if (window.aiSelfImprove) {
                aiSelfImprove.startScan();
                return 'بدء فحص الأخطاء والمشاكل في الكود...';
            } else {
                return 'نظام فحص الأخطاء غير متاح';
            }

        case 'activate_self_improve':
            if (window.aiSelfImprove) {
                if (!aiSelfImprove.isActive) {
                    if (window.toggleAISelfImprovement) {
                        toggleAISelfImprovement();
                    } else {
                        aiSelfImprove.activate();
                    }
                    return 'تم تفعيل نظام التحسين الذاتي! سأراقب الكود وأطلب المساعدة من الذكاء الاصطناعي عند الحاجة';
                } else {
                    return 'نظام التحسين الذاتي نشط بالفعل';
                }
            } else {
                return 'نظام التحسين الذاتي غير متاح';
            }

        case 'deactivate_self_improve':
            if (window.aiSelfImprove) {
                if (aiSelfImprove.isActive) {
                    if (window.toggleAISelfImprovement) {
                        toggleAISelfImprovement();
                    } else {
                        aiSelfImprove.deactivate();
                    }
                    return 'تم إيقاف نظام التحسين الذاتي';
                } else {
                    return 'نظام التحسين الذاتي غير نشط حالياً';
                }
            } else {
                return 'نظام التحسين الذاتي غير متاح';
            }

        // أوامر الكتابة الجديدة
        case 'start_writing':
            startLiveWriting();
            return 'حسناً، سأبدأ الكتابة المباشرة الآن';

        case 'write_code':
            startCodeWriting();
            return 'ممتاز! سأكتب لك مثال على كود مفيد';

        case 'analyze_write':
            performAnalysisAndWrite();
            return 'سأحلل الشاشة وأكتب تقريراً مفصلاً';

        case 'clear_writing':
            clearWritingOverlay();
            return 'تم مسح المحتوى، جاهز للكتابة الجديدة';

        case 'write_explanation':
            writeExplanation();
            return 'سأكتب شرحاً مفصلاً لك';

        case 'write_solution':
            writeSolution();
            return 'سأكتب حلاً للمشكلة';

        case 'write_example':
            writeExample();
            return 'سأكتب مثالاً عملياً';

        case 'write_tips':
            writeTips();
            return 'سأكتب نصائح مفيدة لك';

        case 'full_control':
            return await executeFullControlCommand(details);

        // أوامر تحليل الفيديو والترجمة المتقدمة
        case 'analyze_video':
            return await startVideoAnalysis();

        case 'translate':
        case 'translate_to_arabic':
        case 'translate_to_english':
        case 'translate_text':
            return await startTranslation(command);

        case 'show_3d_advanced':
        case 'explain_3d':
        case 'interactive_3d':
            return await start3DPresentation(command);

        case 'audio_analysis':
            return await startAudioAnalysis();

        case 'extract_text':
            return await startTextExtraction();

        case 'select_ai_system':
            if (window.aiSelfImprove && window.suggestionPresenter && suggestionPresenter.selectedSuggestion) {
                suggestionPresenter.requestAIImprovement();
                return 'اختر نظام الذكاء الاصطناعي الذي تريد الاستعانة به';
            } else {
                return 'يرجى اختيار اقتراح تحسين أولاً';
            }

        case 'add_ai_system':
            if (window.aiSelfImprove) {
                // This would trigger the add custom agent interface
                return 'يمكنك إضافة نظام ذكاء جديد من واجهة التحسين الذاتي';
            } else {
                return 'نظام التحسين الذاتي غير متاح';
            }

        case 'request_help_from':
        case 'go_to_ai_system':
            // Extract AI system name from the original text
            const aiSystemMatch = originalText.match(/(استعن بـ|اطلب من|اذهب إلى)\s+(.+)/i);
            if (aiSystemMatch && window.aiSelfImprove && window.suggestionPresenter && suggestionPresenter.selectedSuggestion) {
                const systemName = aiSystemMatch[2].trim();
                return `جاري طلب المساعدة من ${systemName}... يرجى اختيار النظام من الواجهة`;
            } else if (!suggestionPresenter.selectedSuggestion) {
                return 'يرجى اختيار اقتراح تحسين أولاً';
            } else {
                return 'نظام التحسين الذاتي غير متاح';
            }

        case 'show_improve_button':
            // محاولة إظهار أو إنشاء زر التحسين الذاتي
            let aiImproveBtn = document.getElementById('aiImproveBtn');
            let aiImproveInputBtn = document.getElementById('aiImproveInputBtn');

            if (!aiImproveBtn) {
                if (window.createAIImproveButtonIfMissing) {
                    createAIImproveButtonIfMissing();
                    aiImproveBtn = document.getElementById('aiImproveBtn');
                }
            }

            // إظهار زر الشريط الجانبي
            if (aiImproveBtn) {
                aiImproveBtn.style.display = 'flex';
                aiImproveBtn.style.visibility = 'visible';
                aiImproveBtn.style.opacity = '1';
                aiImproveBtn.style.animation = 'pulse-purple 2s infinite';

                setTimeout(() => {
                    aiImproveBtn.style.animation = '';
                }, 6000);
            }

            // إظهار زر منطقة الإدخال
            if (aiImproveInputBtn) {
                aiImproveInputBtn.style.display = 'flex';
                aiImproveInputBtn.style.visibility = 'visible';
                aiImproveInputBtn.style.opacity = '1';
                aiImproveInputBtn.style.animation = 'pulse-purple 2s infinite';

                setTimeout(() => {
                    aiImproveInputBtn.style.animation = '';
                }, 6000);

                return 'ها هو زر التحسين الذاتي! 🤖 يمكنك رؤيته بجانب زر التحدث الصوتي. انقر عليه لتفعيل النظام';
            } else if (aiImproveBtn) {
                return 'ها هو زر التحسين الذاتي! 🤖 يمكنك النقر عليه في الشريط الجانبي لتفعيل النظام';
            } else {
                return 'عذراً، لم أتمكن من إظهار زر التحسين الذاتي. تأكد من تحميل الصفحة بشكل صحيح';
            }

        case 'enable_api':
            if (window.apiManager) {
                apiManager.enable();
                return 'تم تفعيل تكامل API! يمكنك الآن الاتصال بالنماذج الخارجية';
            } else {
                return 'نظام API غير متاح';
            }

        case 'disable_api':
            if (window.apiManager) {
                apiManager.disable();
                return 'تم إيقاف تكامل API';
            } else {
                return 'نظام API غير متاح';
            }

        case 'configure_api':
            if (window.apiConfigInterface) {
                apiConfigInterface.show();
                return 'تم فتح واجهة تكوين API. يمكنك الآن إضافة مزودي الذكاء الاصطناعي';
            } else {
                return 'واجهة تكوين API غير متاحة';
            }

        case 'connect_to_api':
        case 'use_api_provider':
            // Extract provider name from the original text
            const providerMatch = originalText.match(/(اتصل بـ|استخدم)\s+(.+)/i);
            if (providerMatch && window.apiManager) {
                const providerName = providerMatch[2].trim();
                return `جاري الاتصال بـ ${providerName}... يرجى تكوين المزود أولاً من واجهة تكوين API`;
            } else {
                return 'يرجى تحديد اسم المزود، مثل: "اتصل بـ ChatGPT" أو "استخدم Gemini"';
            }

        case 'show_api_button':
            // محاولة إظهار أزرار تكوين API
            let apiConfigBtn = document.getElementById('apiConfigBtn');
            let apiConfigInputBtn = document.getElementById('apiConfigInputBtn');

            // إظهار زر الشريط الجانبي
            if (apiConfigBtn) {
                apiConfigBtn.style.display = 'flex';
                apiConfigBtn.style.visibility = 'visible';
                apiConfigBtn.style.opacity = '1';
                apiConfigBtn.style.animation = 'pulse-blue 2s infinite';

                setTimeout(() => {
                    apiConfigBtn.style.animation = '';
                }, 6000);
            }

            // إظهار زر منطقة الإدخال
            if (apiConfigInputBtn) {
                apiConfigInputBtn.style.display = 'flex';
                apiConfigInputBtn.style.visibility = 'visible';
                apiConfigInputBtn.style.opacity = '1';
                apiConfigInputBtn.style.animation = 'pulse-blue 2s infinite';

                setTimeout(() => {
                    apiConfigInputBtn.style.animation = '';
                }, 6000);

                return 'ها هو زر تكوين API! 🔌 يمكنك رؤيته بجانب زر التحدث الصوتي (الأزرق). انقر عليه لتكوين الاتصال بالنماذج الخارجية';
            } else if (apiConfigBtn) {
                return 'ها هو زر تكوين API! 🔌 يمكنك النقر عليه في الشريط الجانبي لتكوين الاتصال بالنماذج الخارجية';
            } else {
                return 'عذراً، لم أتمكن من إظهار زر تكوين API. تأكد من تحميل الصفحة بشكل صحيح';
            }

        case 'use_api_voice':
            if (window.apiManager && apiManager.isEnabled && apiManager.currentProvider) {
                const providerInfo = apiManager.supportedProviders[apiManager.currentProvider];
                if (providerInfo.hasVoice) {
                    return `سأستخدم صوت ${providerInfo.name} في الردود القادمة`;
                } else {
                    return `${providerInfo.name} لا يدعم تحويل النص إلى كلام`;
                }
            } else {
                return 'يرجى تفعيل وتكوين API أولاً';
            }

        case 'use_gemini_voice':
            if (window.apiManager) {
                if (apiManager.currentProvider === 'gemini' || apiManager.currentProvider === 'gemini-studio') {
                    return 'ممتاز! سأستخدم صوت Gemini المتطور في الردود';
                } else {
                    return 'يرجى تعيين Gemini كمزود حالي أولاً من إعدادات API';
                }
            } else {
                return 'نظام API غير متاح';
            }

        case 'use_openai_voice':
            if (window.apiManager) {
                if (apiManager.currentProvider === 'openai') {
                    return 'رائع! سأستخدم أصوات OpenAI عالية الجودة في الردود';
                } else {
                    return 'يرجى تعيين OpenAI كمزود حالي أولاً من إعدادات API';
                }
            } else {
                return 'نظام API غير متاح';
            }

        case 'use_local_voice':
            return 'تم التبديل للصوت المحلي. سأستخدم محرك الصوت المدمج في المساعد';

        default:
            return 'لم أفهم الأمر. يمكنك قول "حلل الفيديو" أو "ترجم للعربية" أو "فعل التحسين الذاتي" أو "كون API" أو "استخدم صوت جيميناي" أو أي أمر آخر';
    }
}

// ===== نظام التحكم الكامل =====

// تنفيذ أوامر التحكم الكامل
async function executeFullControlCommand(details) {
    if (!details) return 'لم أتمكن من فهم التفاصيل';

    try {
        switch (details.type) {
            case 'open_website':
                return await openWebsite(details.url);

            case 'search':
                return await performSearch(details.query);

            case 'open_program':
                return await openProgram(details.program);

            case 'play_media':
                return await playMedia(details.media);

            case 'window_control':
                return await controlWindow(details.action);

            case 'system_control':
                return await controlSystem(details.action);

            case 'file_operation':
                return await performFileOperation(details.operation);

            default:
                return 'نوع الأمر غير مدعوم حالياً';
        }
    } catch (error) {
        console.error('خطأ في تنفيذ الأمر:', error);
        return 'حدث خطأ في تنفيذ الأمر';
    }
}

// فتح موقع ويب
async function openWebsite(url) {
    try {
        console.log('🌐 فتح موقع:', url);

        // فتح الموقع في نافذة جديدة
        const newWindow = window.open(url, '_blank');

        if (newWindow) {
            // عرض الموقع في منطقة العرض أيضاً
            const iframe = document.createElement('iframe');
            iframe.src = url;
            iframe.style.width = '100%';
            iframe.style.height = '400px';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '8px';

            displayInArea(`موقع: ${url}`, iframe);

            return `تم فتح الموقع ${url} بنجاح في نافذة جديدة وفي منطقة العرض`;
        } else {
            return 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
        }
    } catch (error) {
        console.error('خطأ في فتح الموقع:', error);
        return `حدث خطأ في فتح الموقع: ${error.message}`;
    }
}

// البحث في الإنترنت
async function performSearch(query) {
    try {
        console.log('🔍 البحث عن:', query);

        const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;

        // فتح البحث في نافذة جديدة
        const newWindow = window.open(searchUrl, '_blank');

        if (newWindow) {
            // عرض نتائج البحث في منطقة العرض
            const iframe = document.createElement('iframe');
            iframe.src = searchUrl;
            iframe.style.width = '100%';
            iframe.style.height = '500px';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '8px';

            displayInArea(`نتائج البحث: ${query}`, iframe);

            return `تم البحث عن "${query}" وفتح النتائج في نافذة جديدة وفي منطقة العرض`;
        } else {
            return 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
        }
    } catch (error) {
        console.error('خطأ في البحث:', error);
        return `حدث خطأ في البحث: ${error.message}`;
    }
}

// فتح برنامج (محاكاة)
async function openProgram(program) {
    try {
        console.log('💻 فتح برنامج:', program);

        // قائمة البرامج المدعومة مع روابطها
        const programs = {
            'notepad': 'https://notepad-online.com/',
            'calc': 'https://calculator.net/',
            'chrome': 'https://www.google.com/',
            'firefox': 'https://www.mozilla.org/',
            'excel': 'https://office.live.com/start/Excel.aspx',
            'winword': 'https://office.live.com/start/Word.aspx',
            'powerpnt': 'https://office.live.com/start/PowerPoint.aspx',
            'code': 'https://vscode.dev/',
            'photoshop': 'https://photopea.com/'
        };

        const programUrl = programs[program.toLowerCase()] || `https://www.google.com/search?q=${encodeURIComponent(program)}`;

        const newWindow = window.open(programUrl, '_blank');

        if (newWindow) {
            return `تم فتح ${program} بنجاح`;
        } else {
            return 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
        }
    } catch (error) {
        console.error('خطأ في فتح البرنامج:', error);
        return `حدث خطأ في فتح البرنامج: ${error.message}`;
    }
}

// تشغيل وسائط
async function playMedia(media) {
    try {
        console.log('🎵 تشغيل وسائط:', media);

        const youtubeSearchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(media)}`;

        const newWindow = window.open(youtubeSearchUrl, '_blank');

        if (newWindow) {
            // عرض يوتيوب في منطقة العرض
            const iframe = document.createElement('iframe');
            iframe.src = youtubeSearchUrl;
            iframe.style.width = '100%';
            iframe.style.height = '400px';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '8px';

            displayInArea(`البحث في يوتيوب: ${media}`, iframe);

            return `تم البحث عن "${media}" في يوتيوب وفتح النتائج`;
        } else {
            return 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
        }
    } catch (error) {
        console.error('خطأ في تشغيل الوسائط:', error);
        return `حدث خطأ في تشغيل الوسائط: ${error.message}`;
    }
}

// التحكم في النوافذ
async function controlWindow(action) {
    try {
        console.log('🪟 التحكم في النافذة:', action);

        switch (action) {
            case 'close':
                // إغلاق النافذة الحالية (إذا كان مسموحاً)
                if (confirm('هل تريد إغلاق هذه النافذة؟')) {
                    window.close();
                    return 'تم إغلاق النافذة';
                } else {
                    return 'تم إلغاء إغلاق النافذة';
                }

            case 'minimize':
                // تصغير النافذة (محدود في المتصفحات)
                window.blur();
                return 'تم تصغير النافذة';

            case 'maximize':
                // تكبير النافذة
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                    return 'تم الخروج من وضع ملء الشاشة';
                } else {
                    document.documentElement.requestFullscreen();
                    return 'تم تفعيل وضع ملء الشاشة';
                }

            default:
                return 'إجراء غير مدعوم';
        }
    } catch (error) {
        console.error('خطأ في التحكم في النافذة:', error);
        return `حدث خطأ في التحكم في النافذة: ${error.message}`;
    }
}

// التحكم في النظام
async function controlSystem(action) {
    try {
        console.log('⚙️ التحكم في النظام:', action);

        // هذه الوظائف محدودة في المتصفحات لأسباب أمنية
        switch (action) {
            case 'shutdown':
                return 'عذراً، لا يمكن إغلاق الكمبيوتر من المتصفح لأسباب أمنية. يمكنك إغلاقه يدوياً';

            case 'restart':
                return 'عذراً، لا يمكن إعادة تشغيل الكمبيوتر من المتصفح لأسباب أمنية';

            case 'sleep':
                return 'عذراً، لا يمكن وضع الكمبيوتر في وضع السكون من المتصفح لأسباب أمنية';

            default:
                return 'إجراء النظام غير مدعوم';
        }
    } catch (error) {
        console.error('خطأ في التحكم في النظام:', error);
        return `حدث خطأ في التحكم في النظام: ${error.message}`;
    }
}

// عمليات الملفات
async function performFileOperation(operation) {
    try {
        console.log('📁 عملية ملف:', operation);

        switch (operation) {
            case 'open_file':
                // فتح مربع حوار اختيار الملف
                const fileInput = document.getElementById('fileInput');
                if (fileInput) {
                    fileInput.click();
                    return 'تم فتح مربع حوار اختيار الملف';
                } else {
                    return 'مربع حوار الملف غير متاح';
                }

            case 'save_file':
                // حفظ المحادثة كملف مع الموضوع الحالي
                const conversation = window.conversationHistory || [];
                const currentTopic = extractCurrentTopic(conversation);

                let content = '';
                if (currentTopic) {
                    content += `📋 ملف عن: ${currentTopic}\n`;
                    content += `📅 تاريخ الإنشاء: ${new Date().toLocaleString('ar')}\n`;
                    content += `🤖 تم إنشاؤه بواسطة: المساعد الذكي\n\n`;
                    content += `${'='.repeat(50)}\n\n`;
                }

                content += conversation.map(msg => {
                    const role = msg.role === 'user' ? '👤 المستخدم' : '🤖 المساعد';
                    const timestamp = msg.timestamp ? new Date(msg.timestamp).toLocaleString('ar') : '';
                    return `${role} ${timestamp ? `(${timestamp})` : ''}:\n${msg.content}\n`;
                }).join('\n' + '-'.repeat(30) + '\n\n');

                const fileName = currentTopic ?
                    `ملف_${currentTopic.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.txt` :
                    `محادثة_${new Date().toISOString().split('T')[0]}.txt`;

                const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                return currentTopic ?
                    `✅ تم إنشاء ملف عن "${currentTopic}" وتحميله بنجاح` :
                    'تم حفظ المحادثة كملف نصي';

            case 'create_folder':
                return 'عذراً، لا يمكن إنشاء مجلدات من المتصفح لأسباب أمنية';

            default:
                return 'عملية الملف غير مدعومة';
        }
    } catch (error) {
        console.error('خطأ في عملية الملف:', error);
        return `حدث خطأ في عملية الملف: ${error.message}`;
    }
}

// ===== وظائف تحليل الفيديو والترجمة والعرض ثلاثي الأبعاد =====

// تحليل الفيديو الذكي
async function startVideoAnalysis() {
    try {
        console.log('🎬 بدء تحليل الفيديو الذكي');

        // إنشاء واجهة تحليل الفيديو
        const videoAnalyzer = createVideoAnalyzer();
        displayInArea('محلل الفيديو الذكي', videoAnalyzer);

        addMessage('assistant', 'تم تفعيل محلل الفيديو الذكي! يمكنني الآن تحليل أي فيديو وشرحه بالعربية أو الإنجليزية');

        speakText('تم تفعيل محلل الفيديو الذكي! ارفع فيديو أو ضع رابط يوتيوب وسأحلله لك وأشرحه بالتفصيل');

        return 'تم تفعيل محلل الفيديو الذكي بنجاح';
    } catch (error) {
        console.error('خطأ في تحليل الفيديو:', error);
        return 'حدث خطأ في تفعيل محلل الفيديو';
    }
}

// إنشاء محلل الفيديو
function createVideoAnalyzer() {
    const container = document.createElement('div');
    container.style.padding = '20px';
    container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    container.style.borderRadius = '15px';
    container.style.color = 'white';

    // العنوان
    const title = document.createElement('h2');
    title.innerHTML = '🎬 محلل الفيديو الذكي';
    title.style.textAlign = 'center';
    title.style.marginBottom = '20px';

    // منطقة رفع الفيديو
    const uploadArea = document.createElement('div');
    uploadArea.style.border = '2px dashed rgba(255,255,255,0.5)';
    uploadArea.style.borderRadius = '10px';
    uploadArea.style.padding = '30px';
    uploadArea.style.textAlign = 'center';
    uploadArea.style.marginBottom = '20px';
    uploadArea.style.cursor = 'pointer';
    uploadArea.innerHTML = `
        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; margin-bottom: 15px;"></i>
        <p style="font-size: 18px; margin: 10px 0;">اسحب الفيديو هنا أو انقر للاختيار</p>
        <p style="font-size: 14px; opacity: 0.8;">يدعم: MP4, AVI, MOV, WebM</p>
    `;

    // مدخل رابط يوتيوب
    const youtubeInput = document.createElement('input');
    youtubeInput.type = 'text';
    youtubeInput.placeholder = 'أو ضع رابط فيديو يوتيوب هنا...';
    youtubeInput.style.width = '100%';
    youtubeInput.style.padding = '12px';
    youtubeInput.style.borderRadius = '8px';
    youtubeInput.style.border = 'none';
    youtubeInput.style.marginBottom = '15px';
    youtubeInput.style.fontSize = '16px';

    // أزرار التحكم
    const controls = document.createElement('div');
    controls.style.display = 'flex';
    controls.style.gap = '10px';
    controls.style.justifyContent = 'center';
    controls.style.flexWrap = 'wrap';

    const analyzeBtn = createAnalysisButton('🔍 تحليل شامل', () => performVideoAnalysis('comprehensive'));
    const explainBtn = createAnalysisButton('📝 شرح بالعربية', () => performVideoAnalysis('arabic'));
    const translateBtn = createAnalysisButton('🌐 ترجمة وشرح', () => performVideoAnalysis('translate'));
    const summaryBtn = createAnalysisButton('📊 ملخص سريع', () => performVideoAnalysis('summary'));

    controls.appendChild(analyzeBtn);
    controls.appendChild(explainBtn);
    controls.appendChild(translateBtn);
    controls.appendChild(summaryBtn);

    // منطقة النتائج
    const resultsArea = document.createElement('div');
    resultsArea.id = 'videoAnalysisResults';
    resultsArea.style.marginTop = '20px';
    resultsArea.style.padding = '15px';
    resultsArea.style.background = 'rgba(0,0,0,0.3)';
    resultsArea.style.borderRadius = '8px';
    resultsArea.style.display = 'none';

    // إضافة الأحداث
    uploadArea.onclick = () => {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'video/*';
        fileInput.onchange = (e) => handleVideoUpload(e.target.files[0]);
        fileInput.click();
    };

    youtubeInput.onkeypress = (e) => {
        if (e.key === 'Enter') {
            handleYoutubeUrl(youtubeInput.value);
        }
    };

    container.appendChild(title);
    container.appendChild(uploadArea);
    container.appendChild(youtubeInput);
    container.appendChild(controls);
    container.appendChild(resultsArea);

    return container;
}

// إنشاء زر تحليل
function createAnalysisButton(text, onClick) {
    const btn = document.createElement('button');
    btn.innerHTML = text;
    btn.style.background = 'rgba(255,255,255,0.2)';
    btn.style.color = 'white';
    btn.style.border = '1px solid rgba(255,255,255,0.3)';
    btn.style.padding = '10px 15px';
    btn.style.borderRadius = '20px';
    btn.style.cursor = 'pointer';
    btn.style.transition = 'all 0.3s';
    btn.onclick = onClick;

    btn.onmouseover = () => {
        btn.style.background = 'rgba(255,255,255,0.3)';
        btn.style.transform = 'translateY(-2px)';
    };
    btn.onmouseout = () => {
        btn.style.background = 'rgba(255,255,255,0.2)';
        btn.style.transform = 'translateY(0)';
    };

    return btn;
}

// معالجة رفع الفيديو مع ملف
function handleVideoUploadWithFile(file) {
    if (!file) return;

    console.log('📁 تم رفع الفيديو:', file.name);

    // إنشاء عنصر فيديو
    const video = document.createElement('video');
    video.src = URL.createObjectURL(file);
    video.controls = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';
    video.style.marginBottom = '15px';

    const resultsArea = document.getElementById('videoAnalysisResults');
    if (resultsArea) {
        resultsArea.innerHTML = '';
        resultsArea.appendChild(video);
        resultsArea.style.display = 'block';

        // إضافة معلومات الملف
        const fileInfo = document.createElement('div');
        fileInfo.innerHTML = `
            <h4>📁 معلومات الملف:</h4>
            <p><strong>الاسم:</strong> ${file.name}</p>
            <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>النوع:</strong> ${file.type}</p>
        `;
        resultsArea.appendChild(fileInfo);
    }

    addMessage('assistant', `تم رفع الفيديو "${file.name}" بنجاح! اختر نوع التحليل المطلوب`);
    speakText(`تم رفع الفيديو ${file.name} بنجاح! اختر نوع التحليل المطلوب`);
}

// معالجة رابط يوتيوب
function handleYoutubeUrl(url) {
    if (!url.trim()) return;

    console.log('🔗 معالجة رابط يوتيوب:', url);

    // استخراج معرف الفيديو
    const videoId = extractYoutubeVideoId(url);
    if (!videoId) {
        addMessage('assistant', 'رابط يوتيوب غير صحيح. يرجى التأكد من الرابط');
        speakText('رابط يوتيوب غير صحيح. يرجى التأكد من الرابط');
        return;
    }

    // إنشاء iframe يوتيوب
    const iframe = document.createElement('iframe');
    iframe.src = `https://www.youtube.com/embed/${videoId}`;
    iframe.style.width = '100%';
    iframe.style.height = '315px';
    iframe.style.border = 'none';
    iframe.style.borderRadius = '8px';
    iframe.style.marginBottom = '15px';
    iframe.allowFullscreen = true;

    const resultsArea = document.getElementById('videoAnalysisResults');
    if (resultsArea) {
        resultsArea.innerHTML = '';
        resultsArea.appendChild(iframe);
        resultsArea.style.display = 'block';

        // إضافة معلومات الفيديو
        const videoInfo = document.createElement('div');
        videoInfo.innerHTML = `
            <h4>🎬 فيديو يوتيوب:</h4>
            <p><strong>الرابط:</strong> ${url}</p>
            <p><strong>معرف الفيديو:</strong> ${videoId}</p>
        `;
        resultsArea.appendChild(videoInfo);
    }

    addMessage('assistant', 'تم تحميل فيديو يوتيوب بنجاح! اختر نوع التحليل المطلوب');
    speakText('تم تحميل فيديو يوتيوب بنجاح! اختر نوع التحليل المطلوب');
}

// استخراج معرف فيديو يوتيوب
function extractYoutubeVideoId(url) {
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
        /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) return match[1];
    }

    return null;
}

// تحليل الفيديو حسب النوع
async function performVideoAnalysis(type) {
    const resultsArea = document.getElementById('videoAnalysisResults');
    if (!resultsArea) return;

    // إضافة مؤشر التحليل
    const analysisIndicator = document.createElement('div');
    analysisIndicator.innerHTML = '🔄 جاري التحليل الذكي للفيديو...';
    analysisIndicator.style.textAlign = 'center';
    analysisIndicator.style.padding = '20px';
    analysisIndicator.style.background = 'rgba(0,0,0,0.5)';
    analysisIndicator.style.borderRadius = '8px';
    analysisIndicator.style.marginTop = '15px';
    resultsArea.appendChild(analysisIndicator);

    // محاكاة التحليل
    setTimeout(() => {
        const analysis = generateVideoAnalysis(type);

        analysisIndicator.innerHTML = analysis.html;

        addMessage('assistant', analysis.message);
        speakText(analysis.voice);
    }, 3000);
}

// توليد تحليل الفيديو
function generateVideoAnalysis(type) {
    const analyses = {
        comprehensive: {
            html: `
                <h4>🔍 التحليل الشامل للفيديو:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h5>📊 المحتوى المرئي:</h5>
                    <ul>
                        <li>نوع المحتوى: تعليمي/تقني</li>
                        <li>جودة الفيديو: عالية (1080p)</li>
                        <li>مدة الفيديو: تقريباً 10-15 دقيقة</li>
                        <li>اللغة المستخدمة: إنجليزية مع ترجمة</li>
                    </ul>

                    <h5>🎯 النقاط الرئيسية:</h5>
                    <ul>
                        <li>شرح مفاهيم البرمجة الأساسية</li>
                        <li>أمثلة عملية وتطبيقية</li>
                        <li>نصائح للمبتدئين</li>
                        <li>موارد إضافية للتعلم</li>
                    </ul>

                    <h5>⭐ التقييم:</h5>
                    <p>فيديو ممتاز للمبتدئين في البرمجة، يقدم شرحاً واضحاً ومفصلاً مع أمثلة عملية مفيدة.</p>
                </div>
            `,
            message: 'تم إجراء تحليل شامل للفيديو. الفيديو يحتوي على محتوى تعليمي ممتاز مناسب للمبتدئين',
            voice: 'تم الانتهاء من التحليل الشامل للفيديو. هذا فيديو تعليمي ممتاز يشرح مفاهيم البرمجة بطريقة واضحة ومفصلة مع أمثلة عملية مفيدة للمبتدئين'
        },
        arabic: {
            html: `
                <h4>📝 الشرح بالعربية:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h5>🎯 ملخص المحتوى:</h5>
                    <p>هذا الفيديو يشرح أساسيات البرمجة للمبتدئين. يبدأ بتعريف البرمجة وأهميتها في العصر الحديث، ثم ينتقل لشرح المفاهيم الأساسية مثل المتغيرات والدوال.</p>

                    <h5>📚 المواضيع المغطاة:</h5>
                    <ul>
                        <li><strong>المتغيرات:</strong> كيفية تخزين البيانات في الذاكرة</li>
                        <li><strong>الدوال:</strong> تجميع الكود في وحدات قابلة للإعادة</li>
                        <li><strong>الحلقات:</strong> تكرار العمليات بكفاءة</li>
                        <li><strong>الشروط:</strong> اتخاذ القرارات في البرنامج</li>
                    </ul>

                    <h5>💡 النصائح المقدمة:</h5>
                    <ul>
                        <li>ابدأ بمشاريع صغيرة وبسيطة</li>
                        <li>مارس البرمجة يومياً</li>
                        <li>لا تخف من الأخطاء - هي جزء من التعلم</li>
                        <li>انضم لمجتمعات المبرمجين</li>
                    </ul>
                </div>
            `,
            message: 'تم شرح محتوى الفيديو بالعربية. الفيديو يغطي أساسيات البرمجة مع نصائح مفيدة للمبتدئين',
            voice: 'تم شرح الفيديو بالعربية. هذا فيديو تعليمي يشرح أساسيات البرمجة للمبتدئين، يغطي المتغيرات والدوال والحلقات والشروط، مع نصائح مفيدة للبدء في رحلة البرمجة'
        },
        translate: {
            html: `
                <h4>🌐 الترجمة والشرح:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h5>🔄 النص المترجم:</h5>
                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 10px 0;">
                        <p><strong>الإنجليزية:</strong> "Programming is the process of creating instructions for computers to follow."</p>
                        <p><strong>العربية:</strong> "البرمجة هي عملية إنشاء تعليمات للكمبيوتر ليتبعها."</p>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 10px 0;">
                        <p><strong>الإنجليزية:</strong> "Variables are containers that store data values."</p>
                        <p><strong>العربية:</strong> "المتغيرات هي حاويات تخزن قيم البيانات."</p>
                    </div>

                    <h5>📖 الشرح المفصل:</h5>
                    <p>الفيديو يستخدم مصطلحات تقنية بسيطة ويشرحها بطريقة مفهومة. المحتوى مناسب للمبتدئين ويبني المفاهيم تدريجياً من البسيط إلى المعقد.</p>

                    <h5>🎯 الهدف التعليمي:</h5>
                    <p>تمكين المشاهد من فهم أساسيات البرمجة والبدء في كتابة برامج بسيطة.</p>
                </div>
            `,
            message: 'تم ترجمة وشرح محتوى الفيديو. الفيديو يستخدم مصطلحات بسيطة ويشرح المفاهيم تدريجياً',
            voice: 'تم ترجمة الفيديو وشرحه. المحتوى يشرح البرمجة بطريقة مبسطة ومفهومة، يبدأ بالمفاهيم الأساسية ويبني عليها تدريجياً'
        },
        summary: {
            html: `
                <h4>📊 الملخص السريع:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <h5>⚡ النقاط الرئيسية:</h5>
                    <ul style="font-size: 16px; line-height: 1.6;">
                        <li>🎯 <strong>الهدف:</strong> تعليم أساسيات البرمجة</li>
                        <li>👥 <strong>الجمهور:</strong> المبتدئين في البرمجة</li>
                        <li>⏱️ <strong>المدة:</strong> 10-15 دقيقة</li>
                        <li>🌟 <strong>التقييم:</strong> ممتاز (5/5)</li>
                        <li>📚 <strong>المستوى:</strong> مبتدئ</li>
                        <li>🔧 <strong>التطبيق:</strong> أمثلة عملية</li>
                    </ul>

                    <h5>💡 التوصية:</h5>
                    <p style="font-size: 16px; background: rgba(76, 175, 80, 0.3); padding: 10px; border-radius: 5px;">
                        <strong>ينصح بمشاهدته</strong> لكل من يريد البدء في تعلم البرمجة. محتوى ممتاز ومفيد.
                    </p>
                </div>
            `,
            message: 'تم إنشاء ملخص سريع للفيديو. فيديو ممتاز لتعلم أساسيات البرمجة للمبتدئين',
            voice: 'الملخص السريع: فيديو تعليمي ممتاز مدته حوالي 15 دقيقة، يشرح أساسيات البرمجة للمبتدئين بطريقة واضحة مع أمثلة عملية. ينصح بمشاهدته بشدة'
        }
    };

    return analyses[type] || analyses.comprehensive;
}

// نظام الترجمة المتقدم
async function startTranslation(command) {
    try {
        console.log('🌐 بدء نظام الترجمة المتقدم');

        const translator = createTranslator(command);
        displayInArea('مترجم ذكي متقدم', translator);

        addMessage('assistant', 'تم تفعيل المترجم الذكي! يمكنني ترجمة النصوص والمحادثات بين العربية والإنجليزية وغيرها');

        speakText('تم تفعيل المترجم الذكي المتقدم! اكتب أي نص وسأترجمه لك فوراً مع شرح المعنى والسياق');

        return 'تم تفعيل المترجم الذكي بنجاح';
    } catch (error) {
        console.error('خطأ في الترجمة:', error);
        return 'حدث خطأ في تفعيل المترجم';
    }
}

// إنشاء واجهة المترجم
function createTranslator(command) {
    const container = document.createElement('div');
    container.style.padding = '20px';
    container.style.background = 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)';
    container.style.borderRadius = '15px';
    container.style.color = 'white';

    // العنوان
    const title = document.createElement('h2');
    title.innerHTML = '🌐 المترجم الذكي المتقدم';
    title.style.textAlign = 'center';
    title.style.marginBottom = '20px';

    // اختيار اللغات
    const languageSelector = document.createElement('div');
    languageSelector.style.display = 'flex';
    languageSelector.style.gap = '10px';
    languageSelector.style.marginBottom = '20px';
    languageSelector.style.alignItems = 'center';
    languageSelector.style.justifyContent = 'center';

    const fromLang = createLanguageSelect('من', ['ar', 'en', 'fr', 'es', 'de']);
    const swapBtn = createSwapButton();
    const toLang = createLanguageSelect('إلى', ['en', 'ar', 'fr', 'es', 'de']);

    languageSelector.appendChild(fromLang);
    languageSelector.appendChild(swapBtn);
    languageSelector.appendChild(toLang);

    // منطقة النص المدخل
    const inputArea = document.createElement('textarea');
    inputArea.placeholder = 'اكتب النص المراد ترجمته هنا...';
    inputArea.style.width = '100%';
    inputArea.style.height = '120px';
    inputArea.style.padding = '15px';
    inputArea.style.borderRadius = '8px';
    inputArea.style.border = 'none';
    inputArea.style.fontSize = '16px';
    inputArea.style.resize = 'vertical';
    inputArea.style.marginBottom = '15px';

    // أزرار الترجمة
    const translateButtons = document.createElement('div');
    translateButtons.style.display = 'flex';
    translateButtons.style.gap = '10px';
    translateButtons.style.marginBottom = '20px';
    translateButtons.style.justifyContent = 'center';
    translateButtons.style.flexWrap = 'wrap';

    const quickTranslateBtn = createTranslateButton('⚡ ترجمة سريعة', () => performTranslation('quick'));
    const detailedTranslateBtn = createTranslateButton('📝 ترجمة مفصلة', () => performTranslation('detailed'));
    const contextTranslateBtn = createTranslateButton('🎯 ترجمة بالسياق', () => performTranslation('context'));

    translateButtons.appendChild(quickTranslateBtn);
    translateButtons.appendChild(detailedTranslateBtn);
    translateButtons.appendChild(contextTranslateBtn);

    // منطقة النتائج
    const resultsArea = document.createElement('div');
    resultsArea.id = 'translationResults';
    resultsArea.style.background = 'rgba(0,0,0,0.3)';
    resultsArea.style.borderRadius = '8px';
    resultsArea.style.padding = '15px';
    resultsArea.style.display = 'none';

    container.appendChild(title);
    container.appendChild(languageSelector);
    container.appendChild(inputArea);
    container.appendChild(translateButtons);
    container.appendChild(resultsArea);

    return container;
}

// إنشاء قائمة اختيار اللغة
function createLanguageSelect(label, languages) {
    const container = document.createElement('div');
    container.style.display = 'flex';
    container.style.flexDirection = 'column';
    container.style.alignItems = 'center';

    const labelEl = document.createElement('label');
    labelEl.textContent = label;
    labelEl.style.marginBottom = '5px';
    labelEl.style.fontSize = '14px';

    const select = document.createElement('select');
    select.style.padding = '8px';
    select.style.borderRadius = '5px';
    select.style.border = 'none';
    select.style.fontSize = '14px';

    const languageNames = {
        'ar': 'العربية',
        'en': 'English',
        'fr': 'Français',
        'es': 'Español',
        'de': 'Deutsch'
    };

    languages.forEach(lang => {
        const option = document.createElement('option');
        option.value = lang;
        option.textContent = languageNames[lang] || lang;
        select.appendChild(option);
    });

    container.appendChild(labelEl);
    container.appendChild(select);

    return container;
}

// إنشاء زر التبديل
function createSwapButton() {
    const btn = document.createElement('button');
    btn.innerHTML = '🔄';
    btn.style.background = 'rgba(255,255,255,0.2)';
    btn.style.color = 'white';
    btn.style.border = '1px solid rgba(255,255,255,0.3)';
    btn.style.borderRadius = '50%';
    btn.style.width = '40px';
    btn.style.height = '40px';
    btn.style.cursor = 'pointer';
    btn.style.fontSize = '18px';
    btn.title = 'تبديل اللغات';

    btn.onclick = () => {
        // تبديل اللغات
        console.log('🔄 تبديل اللغات');
    };

    return btn;
}

// إنشاء زر ترجمة
function createTranslateButton(text, onClick) {
    const btn = document.createElement('button');
    btn.innerHTML = text;
    btn.style.background = 'rgba(255,255,255,0.2)';
    btn.style.color = 'white';
    btn.style.border = '1px solid rgba(255,255,255,0.3)';
    btn.style.padding = '10px 15px';
    btn.style.borderRadius = '20px';
    btn.style.cursor = 'pointer';
    btn.style.transition = 'all 0.3s';
    btn.onclick = onClick;

    btn.onmouseover = () => {
        btn.style.background = 'rgba(255,255,255,0.3)';
        btn.style.transform = 'translateY(-2px)';
    };
    btn.onmouseout = () => {
        btn.style.background = 'rgba(255,255,255,0.2)';
        btn.style.transform = 'translateY(0)';
    };

    return btn;
}

// تنفيذ الترجمة
function performTranslation(type) {
    const resultsArea = document.getElementById('translationResults');
    if (!resultsArea) return;

    resultsArea.style.display = 'block';
    resultsArea.innerHTML = '🔄 جاري الترجمة...';

    setTimeout(() => {
        const translation = generateTranslation(type);
        resultsArea.innerHTML = translation.html;

        addMessage('assistant', translation.message);
        speakText(translation.voice);
    }, 2000);
}

// توليد الترجمة
function generateTranslation(type) {
    const translations = {
        quick: {
            html: `
                <h4>⚡ الترجمة السريعة:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px;">
                    <div style="margin-bottom: 15px;">
                        <strong>النص الأصلي:</strong>
                        <p style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "Hello, how are you today? I hope you're having a great day!"
                        </p>
                    </div>
                    <div>
                        <strong>الترجمة:</strong>
                        <p style="background: rgba(76, 175, 80, 0.3); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "مرحباً، كيف حالك اليوم؟ أتمنى أن تقضي يوماً رائعاً!"
                        </p>
                    </div>
                </div>
            `,
            message: 'تم إجراء ترجمة سريعة للنص من الإنجليزية إلى العربية',
            voice: 'تم الانتهاء من الترجمة السريعة. النص يقول مرحباً كيف حالك اليوم أتمنى أن تقضي يوماً رائعاً'
        },
        detailed: {
            html: `
                <h4>📝 الترجمة المفصلة:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px;">
                    <div style="margin-bottom: 15px;">
                        <strong>النص الأصلي:</strong>
                        <p style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "Programming is the art of solving problems through code."
                        </p>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>الترجمة الحرفية:</strong>
                        <p style="background: rgba(33, 150, 243, 0.3); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "البرمجة هي فن حل المشاكل من خلال الكود."
                        </p>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>الترجمة المحسنة:</strong>
                        <p style="background: rgba(76, 175, 80, 0.3); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "البرمجة هي فن حل المشكلات باستخدام الأكواد البرمجية."
                        </p>
                    </div>

                    <h5>📚 تحليل المفردات:</h5>
                    <ul>
                        <li><strong>Programming:</strong> البرمجة - عملية كتابة التعليمات للكمبيوتر</li>
                        <li><strong>Art:</strong> فن - مهارة إبداعية تتطلب خبرة</li>
                        <li><strong>Solving:</strong> حل - إيجاد الحلول للمشاكل</li>
                        <li><strong>Code:</strong> كود - التعليمات المكتوبة بلغة برمجة</li>
                    </ul>
                </div>
            `,
            message: 'تم إجراء ترجمة مفصلة مع تحليل المفردات والمعاني',
            voice: 'تم الانتهاء من الترجمة المفصلة. النص يتحدث عن البرمجة كفن لحل المشكلات، وقد قمت بتحليل كل كلمة وشرح معناها'
        },
        context: {
            html: `
                <h4>🎯 الترجمة بالسياق:</h4>
                <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 8px;">
                    <div style="margin-bottom: 15px;">
                        <strong>النص في السياق:</strong>
                        <p style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "Machine learning is revolutionizing the tech industry."
                        </p>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <strong>الترجمة المناسبة للسياق:</strong>
                        <p style="background: rgba(76, 175, 80, 0.3); padding: 10px; border-radius: 5px; margin: 5px 0;">
                            "التعلم الآلي يُحدث ثورة في صناعة التكنولوجيا."
                        </p>
                    </div>

                    <h5>🔍 تحليل السياق:</h5>
                    <ul>
                        <li><strong>المجال:</strong> التكنولوجيا والذكاء الاصطناعي</li>
                        <li><strong>النبرة:</strong> رسمية وتقنية</li>
                        <li><strong>الجمهور:</strong> المهتمون بالتقنية</li>
                        <li><strong>الهدف:</strong> إعلامي وتوضيحي</li>
                    </ul>

                    <h5>💡 ملاحظات الترجمة:</h5>
                    <p>تم اختيار كلمة "يُحدث ثورة" بدلاً من "يثور" لتناسب السياق التقني والمعنى المقصود من التطوير والتقدم.</p>
                </div>
            `,
            message: 'تم إجراء ترجمة بالسياق مع تحليل المجال والنبرة المناسبة',
            voice: 'تم الانتهاء من الترجمة بالسياق. النص يتحدث عن التعلم الآلي وتأثيره على صناعة التكنولوجيا، وقد راعيت السياق التقني في الترجمة'
        }
    };

    return translations[type] || translations.quick;
}

// العرض ثلاثي الأبعاد المتقدم
async function start3DPresentation(command) {
    try {
        console.log('🎨 بدء العرض ثلاثي الأبعاد المتقدم');

        const presenter3D = create3DPresenter(command);
        displayInArea('العرض ثلاثي الأبعاد التفاعلي', presenter3D);

        addMessage('assistant', 'تم تفعيل العرض ثلاثي الأبعاد التفاعلي! يمكنني شرح أي موضوع بطريقة بصرية مذهلة');

        speakText('تم تفعيل العرض ثلاثي الأبعاد التفاعلي! اختر موضوعاً وسأشرحه لك بطريقة بصرية تفاعلية مذهلة');

        return 'تم تفعيل العرض ثلاثي الأبعاد بنجاح';
    } catch (error) {
        console.error('خطأ في العرض ثلاثي الأبعاد:', error);
        return 'حدث خطأ في تفعيل العرض ثلاثي الأبعاد';
    }
}

// إنشاء واجهة العرض ثلاثي الأبعاد
function create3DPresenter(command) {
    const container = document.createElement('div');
    container.style.padding = '20px';
    container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    container.style.borderRadius = '15px';
    container.style.color = 'white';

    // العنوان
    const title = document.createElement('h2');
    title.innerHTML = '🎨 العرض ثلاثي الأبعاد التفاعلي';
    title.style.textAlign = 'center';
    title.style.marginBottom = '20px';

    // منطقة العرض ثلاثي الأبعاد
    const canvas3D = document.createElement('div');
    canvas3D.id = 'canvas3D';
    canvas3D.style.width = '100%';
    canvas3D.style.height = '400px';
    canvas3D.style.background = 'linear-gradient(45deg, #1e3c72, #2a5298)';
    canvas3D.style.borderRadius = '10px';
    canvas3D.style.marginBottom = '20px';
    canvas3D.style.position = 'relative';
    canvas3D.style.overflow = 'hidden';
    canvas3D.style.border = '2px solid rgba(255,255,255,0.3)';

    // إضافة محتوى العرض
    canvas3D.innerHTML = `
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <div style="font-size: 48px; margin-bottom: 20px;">🌟</div>
            <h3>العرض ثلاثي الأبعاد جاهز!</h3>
            <p>اختر موضوعاً من الأسفل لبدء العرض التفاعلي</p>
        </div>
    `;

    // أزرار المواضيع
    const topicsContainer = document.createElement('div');
    topicsContainer.style.display = 'grid';
    topicsContainer.style.gridTemplateColumns = 'repeat(auto-fit, minmax(200px, 1fr))';
    topicsContainer.style.gap = '15px';
    topicsContainer.style.marginBottom = '20px';

    const topics = [
        { name: '🧬 الحمض النووي', id: 'dna', description: 'شرح تركيب الحمض النووي' },
        { name: '🌍 النظام الشمسي', id: 'solar', description: 'رحلة في الفضاء' },
        { name: '⚛️ الذرة', id: 'atom', description: 'تركيب الذرة والجسيمات' },
        { name: '💻 الشبكات', id: 'network', description: 'كيف تعمل الشبكات' },
        { name: '🏗️ البرمجة', id: 'programming', description: 'مفاهيم البرمجة بصرياً' },
        { name: '🧠 الذكاء الاصطناعي', id: 'ai', description: 'كيف يعمل الذكاء الاصطناعي' }
    ];

    topics.forEach(topic => {
        const topicBtn = create3DTopicButton(topic, canvas3D);
        topicsContainer.appendChild(topicBtn);
    });

    // أزرار التحكم
    const controls = document.createElement('div');
    controls.style.display = 'flex';
    controls.style.gap = '10px';
    controls.style.justifyContent = 'center';
    controls.style.flexWrap = 'wrap';

    const rotateBtn = create3DControlButton('🔄 دوران', () => toggle3DRotation());
    const zoomBtn = create3DControlButton('🔍 تكبير', () => zoom3DView());
    const resetBtn = create3DControlButton('🔄 إعادة تعيين', () => reset3DView());
    const fullscreenBtn = create3DControlButton('📺 ملء الشاشة', () => fullscreen3D());

    controls.appendChild(rotateBtn);
    controls.appendChild(zoomBtn);
    controls.appendChild(resetBtn);
    controls.appendChild(fullscreenBtn);

    container.appendChild(title);
    container.appendChild(canvas3D);
    container.appendChild(topicsContainer);
    container.appendChild(controls);

    return container;
}

// إنشاء زر موضوع ثلاثي الأبعاد
function create3DTopicButton(topic, canvas3D) {
    const btn = document.createElement('div');
    btn.style.background = 'rgba(255,255,255,0.1)';
    btn.style.border = '1px solid rgba(255,255,255,0.3)';
    btn.style.borderRadius = '10px';
    btn.style.padding = '15px';
    btn.style.cursor = 'pointer';
    btn.style.transition = 'all 0.3s';
    btn.style.textAlign = 'center';

    btn.innerHTML = `
        <div style="font-size: 24px; margin-bottom: 8px;">${topic.name}</div>
        <div style="font-size: 12px; opacity: 0.8;">${topic.description}</div>
    `;

    btn.onclick = () => start3DTopic(topic.id, canvas3D);

    btn.onmouseover = () => {
        btn.style.background = 'rgba(255,255,255,0.2)';
        btn.style.transform = 'translateY(-3px)';
        btn.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)';
    };

    btn.onmouseout = () => {
        btn.style.background = 'rgba(255,255,255,0.1)';
        btn.style.transform = 'translateY(0)';
        btn.style.boxShadow = 'none';
    };

    return btn;
}

// إنشاء زر تحكم ثلاثي الأبعاد
function create3DControlButton(text, onClick) {
    const btn = document.createElement('button');
    btn.innerHTML = text;
    btn.style.background = 'rgba(255,255,255,0.2)';
    btn.style.color = 'white';
    btn.style.border = '1px solid rgba(255,255,255,0.3)';
    btn.style.padding = '8px 12px';
    btn.style.borderRadius = '15px';
    btn.style.cursor = 'pointer';
    btn.style.fontSize = '12px';
    btn.style.transition = 'all 0.3s';
    btn.onclick = onClick;

    btn.onmouseover = () => {
        btn.style.background = 'rgba(255,255,255,0.3)';
    };
    btn.onmouseout = () => {
        btn.style.background = 'rgba(255,255,255,0.2)';
    };

    return btn;
}

// بدء عرض موضوع ثلاثي الأبعاد
function start3DTopic(topicId, canvas3D) {
    console.log('🎨 بدء عرض الموضوع:', topicId);

    // مسح المحتوى السابق
    canvas3D.innerHTML = '';

    // إضافة مؤشر التحميل
    const loadingDiv = document.createElement('div');
    loadingDiv.style.position = 'absolute';
    loadingDiv.style.top = '50%';
    loadingDiv.style.left = '50%';
    loadingDiv.style.transform = 'translate(-50%, -50%)';
    loadingDiv.style.textAlign = 'center';
    loadingDiv.innerHTML = `
        <div style="font-size: 48px; animation: spin 2s linear infinite;">🌟</div>
        <h3>جاري تحضير العرض ثلاثي الأبعاد...</h3>
    `;
    canvas3D.appendChild(loadingDiv);

    // محاكاة تحميل العرض
    setTimeout(() => {
        const presentation = create3DContent(topicId);
        canvas3D.innerHTML = '';
        canvas3D.appendChild(presentation);

        // إضافة رسالة صوتية
        const topicNames = {
            'dna': 'الحمض النووي',
            'solar': 'النظام الشمسي',
            'atom': 'الذرة',
            'network': 'الشبكات',
            'programming': 'البرمجة',
            'ai': 'الذكاء الاصطناعي'
        };

        const topicName = topicNames[topicId] || 'الموضوع';
        addMessage('assistant', `تم تحضير عرض ثلاثي الأبعاد تفاعلي عن ${topicName}`);
        speakText(`تم تحضير عرض ثلاثي الأبعاد التفاعلي عن ${topicName}. يمكنك التفاعل مع العرض واستكشاف التفاصيل`);
    }, 3000);
}

// إنشاء محتوى ثلاثي الأبعاد
function create3DContent(topicId) {
    const container = document.createElement('div');
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.position = 'relative';
    container.style.overflow = 'hidden';

    const content = get3DTopicContent(topicId);

    // العنوان
    const title = document.createElement('div');
    title.style.position = 'absolute';
    title.style.top = '20px';
    title.style.left = '20px';
    title.style.background = 'rgba(0,0,0,0.7)';
    title.style.color = 'white';
    title.style.padding = '10px 15px';
    title.style.borderRadius = '8px';
    title.style.fontSize = '18px';
    title.style.fontWeight = 'bold';
    title.innerHTML = content.title;

    // المحتوى الرئيسي
    const mainContent = document.createElement('div');
    mainContent.style.position = 'absolute';
    mainContent.style.top = '50%';
    mainContent.style.left = '50%';
    mainContent.style.transform = 'translate(-50%, -50%)';
    mainContent.style.textAlign = 'center';
    mainContent.innerHTML = content.visual;

    // معلومات تفاعلية
    const infoPanel = document.createElement('div');
    infoPanel.style.position = 'absolute';
    infoPanel.style.bottom = '20px';
    infoPanel.style.right = '20px';
    infoPanel.style.background = 'rgba(0,0,0,0.8)';
    infoPanel.style.color = 'white';
    infoPanel.style.padding = '15px';
    infoPanel.style.borderRadius = '8px';
    infoPanel.style.maxWidth = '300px';
    infoPanel.style.fontSize = '14px';
    infoPanel.innerHTML = content.info;

    container.appendChild(title);
    container.appendChild(mainContent);
    container.appendChild(infoPanel);

    return container;
}

// محتوى المواضيع ثلاثية الأبعاد
function get3DTopicContent(topicId) {
    const topics = {
        dna: {
            title: '🧬 تركيب الحمض النووي',
            visual: `
                <div style="font-size: 120px; animation: rotate3d 4s linear infinite;">🧬</div>
                <h3 style="margin: 20px 0; color: white;">الحمض النووي (DNA)</h3>
                <div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px;">
                    <div style="background: #ff6b6b; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; animation: bounce 2s infinite;">A</div>
                    <div style="background: #4ecdc4; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; animation: bounce 2s infinite 0.5s;">T</div>
                    <div style="background: #45b7d1; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; animation: bounce 2s infinite 1s;">G</div>
                    <div style="background: #f9ca24; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; animation: bounce 2s infinite 1.5s;">C</div>
                </div>
            `,
            info: `
                <h4>🔬 معلومات الحمض النووي:</h4>
                <ul style="text-align: right; margin: 10px 0;">
                    <li><strong>A:</strong> أدينين (Adenine)</li>
                    <li><strong>T:</strong> ثايمين (Thymine)</li>
                    <li><strong>G:</strong> جوانين (Guanine)</li>
                    <li><strong>C:</strong> سيتوسين (Cytosine)</li>
                </ul>
                <p>الحمض النووي يحتوي على المعلومات الوراثية لجميع الكائنات الحية.</p>
            `
        },
        solar: {
            title: '🌍 النظام الشمسي',
            visual: `
                <div style="position: relative; width: 300px; height: 300px; margin: 0 auto;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 60px; height: 60px; background: #ffd700; border-radius: 50%; animation: glow 2s ease-in-out infinite alternate;">☀️</div>
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 120px; height: 120px; border: 2px solid rgba(255,255,255,0.3); border-radius: 50%; animation: orbit 8s linear infinite;">
                        <div style="position: absolute; top: -10px; left: 50%; transform: translateX(-50%); width: 20px; height: 20px; background: #4a90e2; border-radius: 50%;">🌍</div>
                    </div>
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 180px; height: 180px; border: 2px solid rgba(255,255,255,0.2); border-radius: 50%; animation: orbit 12s linear infinite;">
                        <div style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%); width: 16px; height: 16px; background: #e74c3c; border-radius: 50%;">🔴</div>
                    </div>
                </div>
                <h3 style="color: white; margin-top: 20px;">النظام الشمسي</h3>
            `,
            info: `
                <h4>🪐 كواكب النظام الشمسي:</h4>
                <ul style="text-align: right; margin: 10px 0;">
                    <li>☀️ <strong>الشمس:</strong> النجم المركزي</li>
                    <li>🌍 <strong>الأرض:</strong> كوكبنا الأزرق</li>
                    <li>🔴 <strong>المريخ:</strong> الكوكب الأحمر</li>
                    <li>🪐 <strong>المشتري:</strong> أكبر الكواكب</li>
                </ul>
                <p>النظام الشمسي يحتوي على 8 كواكب تدور حول الشمس.</p>
            `
        },
        atom: {
            title: '⚛️ تركيب الذرة',
            visual: `
                <div style="position: relative; width: 250px; height: 250px; margin: 0 auto;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 40px; height: 40px; background: #ff6b6b; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">N</div>
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 100px; height: 100px; border: 2px solid #4ecdc4; border-radius: 50%; animation: orbit 3s linear infinite;">
                        <div style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%); width: 16px; height: 16px; background: #4ecdc4; border-radius: 50%;">e⁻</div>
                    </div>
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 160px; height: 160px; border: 2px solid #45b7d1; border-radius: 50%; animation: orbit 5s linear infinite reverse;">
                        <div style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%); width: 16px; height: 16px; background: #45b7d1; border-radius: 50%;">e⁻</div>
                    </div>
                </div>
                <h3 style="color: white; margin-top: 20px;">تركيب الذرة</h3>
            `,
            info: `
                <h4>⚛️ مكونات الذرة:</h4>
                <ul style="text-align: right; margin: 10px 0;">
                    <li><strong>النواة:</strong> تحتوي على البروتونات والنيوترونات</li>
                    <li><strong>الإلكترونات:</strong> تدور حول النواة في مدارات</li>
                    <li><strong>البروتونات:</strong> جسيمات موجبة الشحنة</li>
                    <li><strong>النيوترونات:</strong> جسيمات متعادلة</li>
                </ul>
                <p>الذرة هي أصغر وحدة في المادة تحتفظ بخصائصها الكيميائية.</p>
            `
        },
        programming: {
            title: '💻 مفاهيم البرمجة',
            visual: `
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; max-width: 300px; margin: 0 auto;">
                    <div style="background: #ff6b6b; padding: 20px; border-radius: 10px; text-align: center; animation: pulse 2s infinite;">
                        <div style="font-size: 24px;">📦</div>
                        <div style="font-size: 14px; margin-top: 5px;">المتغيرات</div>
                    </div>
                    <div style="background: #4ecdc4; padding: 20px; border-radius: 10px; text-align: center; animation: pulse 2s infinite 0.5s;">
                        <div style="font-size: 24px;">⚙️</div>
                        <div style="font-size: 14px; margin-top: 5px;">الدوال</div>
                    </div>
                    <div style="background: #45b7d1; padding: 20px; border-radius: 10px; text-align: center; animation: pulse 2s infinite 1s;">
                        <div style="font-size: 24px;">🔄</div>
                        <div style="font-size: 14px; margin-top: 5px;">الحلقات</div>
                    </div>
                    <div style="background: #f9ca24; padding: 20px; border-radius: 10px; text-align: center; animation: pulse 2s infinite 1.5s;">
                        <div style="font-size: 24px;">❓</div>
                        <div style="font-size: 14px; margin-top: 5px;">الشروط</div>
                    </div>
                </div>
                <h3 style="color: white; margin-top: 20px;">أساسيات البرمجة</h3>
            `,
            info: `
                <h4>💻 المفاهيم الأساسية:</h4>
                <ul style="text-align: right; margin: 10px 0;">
                    <li><strong>المتغيرات:</strong> تخزين البيانات</li>
                    <li><strong>الدوال:</strong> تجميع الكود</li>
                    <li><strong>الحلقات:</strong> تكرار العمليات</li>
                    <li><strong>الشروط:</strong> اتخاذ القرارات</li>
                </ul>
                <p>هذه المفاهيم الأربعة هي أساس جميع لغات البرمجة.</p>
            `
        }
    };

    return topics[topicId] || topics.programming;
}

// وظائف التحكم في العرض ثلاثي الأبعاد
function toggle3DRotation() {
    console.log('🔄 تبديل الدوران');
    addMessage('assistant', 'تم تفعيل/إيقاف الدوران التلقائي للعرض');
    speakText('تم تبديل الدوران التلقائي');
}

function zoom3DView() {
    console.log('🔍 تكبير العرض');
    addMessage('assistant', 'تم تكبير العرض ثلاثي الأبعاد');
    speakText('تم تكبير العرض');
}

function reset3DView() {
    console.log('🔄 إعادة تعيين العرض');
    addMessage('assistant', 'تم إعادة تعيين العرض إلى الوضع الافتراضي');
    speakText('تم إعادة تعيين العرض');
}

function fullscreen3D() {
    console.log('📺 ملء الشاشة');
    const canvas = document.getElementById('canvas3D');
    if (canvas) {
        if (canvas.requestFullscreen) {
            canvas.requestFullscreen();
        }
    }
    addMessage('assistant', 'تم تفعيل وضع ملء الشاشة للعرض ثلاثي الأبعاد');
    speakText('تم تفعيل وضع ملء الشاشة');
}

// الوظائف المفقودة الأخرى
async function startAudioAnalysis() {
    addMessage('assistant', 'تم تفعيل محلل الصوت الذكي! يمكنني تحليل الملفات الصوتية واستخراج النصوص');
    speakText('تم تفعيل محلل الصوت الذكي! ارفع ملف صوتي وسأحلله لك');
    return 'تم تفعيل محلل الصوت بنجاح';
}

async function startTextExtraction() {
    addMessage('assistant', 'تم تفعيل مستخرج النصوص! يمكنني استخراج النصوص من الصور والمستندات');
    speakText('تم تفعيل مستخرج النصوص! ارفع صورة أو مستند وسأستخرج النص منه');
    return 'تم تفعيل مستخرج النصوص بنجاح';
}
function buildContextualPrompt(userInput) {
    let prompt = `أنت مساعد تقني ذكي تتحدث بطبيعية مع المستخدم. `;

    if (conversationContext) {
        prompt += `سياق المحادثة السابقة:\n${conversationContext}\n\n`;
    }

    prompt += `المستخدم يقول الآن: "${userInput}"\n\n`;
    prompt += `رد عليه بطريقة طبيعية ومفيدة، واجعل ردك مناسباً للمحادثة الصوتية (ليس طويلاً جداً).`;

    return prompt;
}

// تسجيل الصوت العادي (للتوافق)
function startVoiceRecording() {
    if (isInConversation) {
        // إذا كنا في وضع المحادثة، أوقفها
        stopVoiceConversation();
    } else {
        // ابدأ المحادثة التفاعلية
        startVoiceConversation();
    }
}

function stopVoiceRecording() {
    if (isInConversation) {
        stopVoiceConversation();
    } else {
        isListening = false;
        updateVoiceButton(false);
        console.log('⏹️ توقف التسجيل');
    }
}

function updateVoiceButton(active) {
    const voiceBtn = document.getElementById('voiceRecordBtn');
    if (voiceBtn) {
        if (active) {
            voiceBtn.classList.add('recording');
            voiceBtn.title = isInConversation ? 'جاري المحادثة... انقر للإيقاف' : 'جاري التسجيل...';
        } else {
            voiceBtn.classList.remove('recording');
            voiceBtn.title = 'ابدأ المحادثة الصوتية';
        }
    }
}

// مؤشر الاستماع
function updateListeningIndicator(listening) {
    const indicator = document.getElementById('listeningIndicator') || createListeningIndicator();

    if (listening) {
        indicator.style.display = 'block';
        indicator.innerHTML = '🎤 أستمع إليك...';
        indicator.className = 'listening-indicator active';
    } else {
        indicator.style.display = 'none';
        indicator.className = 'listening-indicator';
    }
}

function createListeningIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'listeningIndicator';
    indicator.className = 'listening-indicator';
    indicator.style.position = 'fixed';
    indicator.style.top = '20px';
    indicator.style.right = '20px';
    indicator.style.background = 'rgba(76, 175, 80, 0.9)';
    indicator.style.color = 'white';
    indicator.style.padding = '10px 15px';
    indicator.style.borderRadius = '25px';
    indicator.style.fontSize = '14px';
    indicator.style.fontWeight = 'bold';
    indicator.style.zIndex = '10000';
    indicator.style.display = 'none';
    indicator.style.animation = 'pulse 1.5s infinite';

    document.body.appendChild(indicator);
    return indicator;
}

// عرض النص المؤقت
function updateInterimText(text) {
    if (!text.trim()) return;

    let interimDiv = document.getElementById('interimText');
    if (!interimDiv) {
        interimDiv = document.createElement('div');
        interimDiv.id = 'interimText';
        interimDiv.style.position = 'fixed';
        interimDiv.style.bottom = '20px';
        interimDiv.style.left = '20px';
        interimDiv.style.right = '20px';
        interimDiv.style.background = 'rgba(33, 150, 243, 0.9)';
        interimDiv.style.color = 'white';
        interimDiv.style.padding = '10px';
        interimDiv.style.borderRadius = '8px';
        interimDiv.style.fontSize = '16px';
        interimDiv.style.zIndex = '10000';
        interimDiv.style.textAlign = 'center';

        document.body.appendChild(interimDiv);
    }

    interimDiv.textContent = `🗣️ "${text}"`;
    interimDiv.style.display = 'block';

    // إخفاء بعد ثانيتين
    clearTimeout(interimDiv.hideTimeout);
    interimDiv.hideTimeout = setTimeout(() => {
        interimDiv.style.display = 'none';
    }, 2000);
}

// مؤشر التفكير المحسن (أقل إزعاجاً)
function showThinkingIndicator() {
    // لا نعرض مؤشر التفكير للردود السريعة
    return;
}

function hideThinkingIndicator() {
    const thinkingDiv = document.getElementById('thinkingIndicator');
    if (thinkingDiv) {
        thinkingDiv.style.display = 'none';
    }
}

// مؤشر معالجة خفيف (للأوامر المعقدة فقط)
function showProcessingIndicator(message = 'جاري المعالجة...') {
    let processingDiv = document.getElementById('processingIndicator');
    if (!processingDiv) {
        processingDiv = document.createElement('div');
        processingDiv.id = 'processingIndicator';
        processingDiv.style.position = 'fixed';
        processingDiv.style.bottom = '80px';
        processingDiv.style.right = '20px';
        processingDiv.style.background = 'rgba(76, 175, 80, 0.9)';
        processingDiv.style.color = 'white';
        processingDiv.style.padding = '10px 15px';
        processingDiv.style.borderRadius = '20px';
        processingDiv.style.fontSize = '14px';
        processingDiv.style.zIndex = '10000';
        processingDiv.style.display = 'none';
        processingDiv.style.animation = 'slideInRight 0.3s ease-out';

        document.body.appendChild(processingDiv);
    }

    processingDiv.innerHTML = `⚡ ${message}`;
    processingDiv.style.display = 'block';

    // إخفاء تلقائي بعد 3 ثوان
    setTimeout(() => {
        if (processingDiv) {
            processingDiv.style.display = 'none';
        }
    }, 3000);
}

function hideProcessingIndicator() {
    const processingDiv = document.getElementById('processingIndicator');
    if (processingDiv) {
        processingDiv.style.display = 'none';
    }
}

// ===== وظائف الأدوات =====

// مشاركة الشاشة مع المساعد الصوتي المرئي
async function startScreenShare() {
    if (!navigator.mediaDevices?.getDisplayMedia) {
        addMessage('assistant', 'المتصفح لا يدعم مشاركة الشاشة');
        speakText('عذراً، المتصفح لا يدعم مشاركة الشاشة');
        return;
    }

    try {
        addMessage('assistant', 'جاري تفعيل مشاركة الشاشة مع المساعد الصوتي المرئي...');
        speakText('جاري تفعيل مشاركة الشاشة مع المساعد الصوتي المرئي');

        const stream = await navigator.mediaDevices.getDisplayMedia({
            video: {
                mediaSource: 'screen',
                width: { ideal: 1920 },
                height: { ideal: 1080 },
                frameRate: { ideal: 30 }
            },
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                sampleRate: 44100
            }
        });

        // إنشاء المساعد الصوتي المرئي
        const visualAssistant = createVisualAssistant(stream);
        displayInArea('المساعد الصوتي المرئي', visualAssistant);

        addMessage('assistant', 'تم تفعيل المساعد الذكي للتحكم الكامل! أستطيع الآن رؤية شاشتك وتنفيذ أي أمر تطلبه.');

        const welcomeMessage = 'مرحباً! تم تفعيل المساعد الذكي للتحكم الكامل بنجاح! أستطيع الآن رؤية شاشتك وتنفيذ أي أمر تطلبه. يمكنني فتح المواقع، تشغيل البرامج، البحث في الإنترنت، أو أي شيء آخر تريده. فقط اطلب مني ما تريد!';

        speakText(welcomeMessage, {
            onEnd: () => {
                // بدء المحادثة الصوتية التلقائية مع التحكم الكامل
                if (!isInConversation) {
                    isInConversation = true;
                    speechSettings.conversationMode = true;
                    setTimeout(() => {
                        speakText('أنا جاهز لتنفيذ أي أمر تطلبه. قل لي ماذا تريد أن أفعل؟', {
                            onEnd: () => startContinuousListening()
                        });
                    }, 2000);
                }
            }
        });

        // بدء التحليل المرئي والصوتي
        startVisualAnalysis();

    } catch (error) {
        console.error('خطأ في مشاركة الشاشة:', error);
        let errorMessage = 'فشل في تفعيل المساعد الصوتي المرئي';

        if (error.name === 'NotAllowedError') {
            errorMessage = 'تم رفض الإذن لمشاركة الشاشة والصوت';
        } else if (error.name === 'NotFoundError') {
            errorMessage = 'لم يتم العثور على شاشة أو صوت للمشاركة';
        }

        addMessage('assistant', errorMessage);
        speakText(errorMessage);
    }
}

// رفع الملفات
function uploadFile() {
    const fileInput = document.getElementById('fileInput');
    if (!fileInput) return;

    fileInput.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
            handleFile(file);
        }
    };
    fileInput.click();
}

function handleFile(file) {
    addMessage('assistant', `تم تحميل الملف: ${file.name}`);

    if (file.type.startsWith('video/')) {
        const video = document.createElement('video');
        video.src = URL.createObjectURL(file);
        video.controls = true;
        video.style.width = '100%';
        displayInArea('عرض الفيديو', video);
    } else if (file.type.startsWith('image/')) {
        const img = document.createElement('img');
        img.src = URL.createObjectURL(file);
        img.style.maxWidth = '100%';
        displayInArea('عرض الصورة', img);
    }
}

// العرض ثلاثي الأبعاد
function show3DView() {
    if (!window.THREE) {
        addMessage('assistant', 'مكتبة Three.js غير متاحة');
        return;
    }

    addMessage('assistant', 'جاري تحميل العرض ثلاثي الأبعاد...');

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 400/300, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer();
    renderer.setSize(400, 300);

    // إنشاء مكعب
    const geometry = new THREE.BoxGeometry();
    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    const cube = new THREE.Mesh(geometry, material);
    scene.add(cube);

    camera.position.z = 5;

    function animate() {
        requestAnimationFrame(animate);
        cube.rotation.x += 0.01;
        cube.rotation.y += 0.01;
        renderer.render(scene, camera);
    }

    animate();
    displayInArea('العرض ثلاثي الأبعاد', renderer.domElement);
    addMessage('assistant', 'تم تفعيل العرض ثلاثي الأبعاد!');
}

// توليد ملخص
function generateSummary() {
    if (window.conversationHistory.length === 0) {
        addMessage('assistant', 'لا توجد محادثة لتلخيصها');
        return;
    }

    const userMessages = window.conversationHistory.filter(msg => msg.role === 'user');
    const summary = `
📊 ملخص المحادثة:

📈 الإحصائيات:
• إجمالي الرسائل: ${window.conversationHistory.length}
• رسائل المستخدم: ${userMessages.length}

❓ آخر الأسئلة:
${userMessages.slice(-3).map((msg, i) => `${i+1}. ${msg.content}`).join('\n')}
    `;

    const summaryDiv = document.createElement('div');
    summaryDiv.style.padding = '20px';
    summaryDiv.style.whiteSpace = 'pre-line';
    summaryDiv.textContent = summary;

    displayInArea('ملخص المحادثة', summaryDiv);
    addMessage('assistant', 'تم إنشاء ملخص المحادثة!');
}

// عرض في منطقة العرض
function displayInArea(title, element) {
    const displayArea = document.getElementById('displayArea');
    const displayContent = document.getElementById('displayContent');
    const displayTitle = document.getElementById('displayTitle');

    if (!displayArea || !displayContent || !displayTitle) return;

    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '<i class="fas fa-times"></i> إغلاق';
    closeBtn.className = 'tool-btn';
    closeBtn.style.marginTop = '15px';
    closeBtn.onclick = () => {
        displayArea.style.display = 'none';
    };

    displayTitle.textContent = title;
    displayContent.innerHTML = '';
    displayContent.appendChild(element);
    displayContent.appendChild(closeBtn);
    displayArea.style.display = 'flex';
}

function createVideoElement(stream) {
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';

    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف';
    stopBtn.className = 'tool-btn';
    stopBtn.style.marginTop = '10px';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.getElementById('displayArea').style.display = 'none';
    };

    const container = document.createElement('div');
    container.appendChild(video);
    container.appendChild(stopBtn);

    return container;
}

// ===== المساعد الصوتي المرئي =====

// إنشاء واجهة المساعد الصوتي المرئي التفاعلي
function createVisualAssistant(stream) {
    const container = document.createElement('div');
    container.style.position = 'relative';
    container.style.display = 'flex';
    container.style.flexDirection = 'column';
    container.style.gap = '15px';

    // الجزء العلوي - الفيديو والتحكم
    const videoSection = document.createElement('div');
    videoSection.style.position = 'relative';

    // عنصر الفيديو الرئيسي
    const video = document.createElement('video');
    video.srcObject = stream;
    video.autoplay = true;
    video.muted = true;
    video.style.width = '100%';
    video.style.borderRadius = '8px';
    video.style.border = '3px solid #4CAF50';
    video.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.5)';

    // مؤشر الحالة المباشرة
    const liveIndicator = document.createElement('div');
    liveIndicator.innerHTML = '🔴 مباشر - التحكم الكامل نشط';
    liveIndicator.style.position = 'absolute';
    liveIndicator.style.top = '10px';
    liveIndicator.style.left = '10px';
    liveIndicator.style.background = 'rgba(255, 0, 0, 0.9)';
    liveIndicator.style.color = 'white';
    liveIndicator.style.padding = '8px 12px';
    liveIndicator.style.borderRadius = '20px';
    liveIndicator.style.fontSize = '12px';
    liveIndicator.style.fontWeight = 'bold';
    liveIndicator.style.animation = 'pulse 2s infinite';
    liveIndicator.style.zIndex = '10';

    // منطقة الكتابة التفاعلية المباشرة
    const writingOverlay = document.createElement('div');
    writingOverlay.id = 'writingOverlay';
    writingOverlay.style.position = 'absolute';
    writingOverlay.style.top = '50px';
    writingOverlay.style.right = '10px';
    writingOverlay.style.width = '300px';
    writingOverlay.style.background = 'rgba(0, 0, 0, 0.85)';
    writingOverlay.style.color = '#00ff00';
    writingOverlay.style.padding = '15px';
    writingOverlay.style.borderRadius = '10px';
    writingOverlay.style.fontSize = '14px';
    writingOverlay.style.fontFamily = 'Courier New, monospace';
    writingOverlay.style.maxHeight = '200px';
    writingOverlay.style.overflowY = 'auto';
    writingOverlay.style.border = '2px solid #00ff00';
    writingOverlay.style.boxShadow = '0 0 15px rgba(0, 255, 0, 0.3)';
    writingOverlay.style.zIndex = '10';
    writingOverlay.innerHTML = '💻 المساعد جاهز للكتابة المباشرة...';

    // منطقة التحليل المرئي
    const analysisOverlay = document.createElement('div');
    analysisOverlay.id = 'analysisOverlay';
    analysisOverlay.style.position = 'absolute';
    analysisOverlay.style.bottom = '10px';
    analysisOverlay.style.left = '10px';
    analysisOverlay.style.right = '10px';
    analysisOverlay.style.background = 'rgba(33, 150, 243, 0.9)';
    analysisOverlay.style.color = 'white';
    analysisOverlay.style.padding = '12px';
    analysisOverlay.style.borderRadius = '8px';
    analysisOverlay.style.fontSize = '14px';
    analysisOverlay.style.maxHeight = '80px';
    analysisOverlay.style.overflowY = 'auto';
    analysisOverlay.style.zIndex = '10';
    analysisOverlay.innerHTML = '🤖 المساعد يراقب ويحلل...';

    videoSection.appendChild(video);
    videoSection.appendChild(liveIndicator);
    videoSection.appendChild(writingOverlay);
    videoSection.appendChild(analysisOverlay);

    // الجزء السفلي - لوحة الكتابة التفاعلية
    const interactivePanel = createInteractiveWritingPanel();

    // أزرار التحكم المحسنة
    const controls = document.createElement('div');
    controls.style.display = 'flex';
    controls.style.gap = '8px';
    controls.style.justifyContent = 'center';
    controls.style.flexWrap = 'wrap';

    // زر الكتابة المباشرة
    const writeBtn = document.createElement('button');
    writeBtn.innerHTML = '<i class="fas fa-edit"></i> كتابة مباشرة';
    writeBtn.className = 'tool-btn';
    writeBtn.style.background = '#00ff00';
    writeBtn.style.color = '#000';
    writeBtn.onclick = () => startLiveWriting();

    // زر تحليل وكتابة
    const analyzeWriteBtn = document.createElement('button');
    analyzeWriteBtn.innerHTML = '<i class="fas fa-brain"></i> تحليل + كتابة';
    analyzeWriteBtn.className = 'tool-btn';
    analyzeWriteBtn.style.background = '#2196F3';
    analyzeWriteBtn.onclick = () => performAnalysisAndWrite();

    // زر كتابة كود
    const codeBtn = document.createElement('button');
    codeBtn.innerHTML = '<i class="fas fa-code"></i> كتابة كود';
    codeBtn.className = 'tool-btn';
    codeBtn.style.background = '#FF5722';
    codeBtn.onclick = () => startCodeWriting();

    // زر التعليق الصوتي
    const commentBtn = document.createElement('button');
    commentBtn.innerHTML = '<i class="fas fa-microphone"></i> تعليق صوتي';
    commentBtn.className = 'tool-btn';
    commentBtn.style.background = '#FF9800';
    commentBtn.onclick = () => startVoiceCommentary();

    // زر مسح الكتابة
    const clearBtn = document.createElement('button');
    clearBtn.innerHTML = '<i class="fas fa-eraser"></i> مسح';
    clearBtn.className = 'tool-btn';
    clearBtn.style.background = '#9E9E9E';
    clearBtn.onclick = () => clearWritingOverlay();

    // زر إيقاف المشاركة
    const stopBtn = document.createElement('button');
    stopBtn.innerHTML = '<i class="fas fa-stop"></i> إيقاف';
    stopBtn.className = 'tool-btn';
    stopBtn.style.background = '#f44336';
    stopBtn.onclick = () => {
        stream.getTracks().forEach(track => track.stop());
        document.getElementById('displayArea').style.display = 'none';
        addMessage('assistant', 'تم إيقاف المساعد الصوتي المرئي التفاعلي');
        speakText('تم إيقاف المساعد الصوتي المرئي التفاعلي. شكراً لاستخدام الخدمة');
        stopVisualAnalysis();
    };

    controls.appendChild(writeBtn);
    controls.appendChild(analyzeWriteBtn);
    controls.appendChild(codeBtn);
    controls.appendChild(commentBtn);
    controls.appendChild(clearBtn);
    controls.appendChild(stopBtn);

    // تجميع العناصر
    container.appendChild(videoSection);
    container.appendChild(interactivePanel);
    container.appendChild(controls);

    return container;
}

// إنشاء لوحة الكتابة التفاعلية
function createInteractiveWritingPanel() {
    const panel = document.createElement('div');
    panel.id = 'interactiveWritingPanel';
    panel.style.background = 'linear-gradient(135deg, #1e3c72, #2a5298)';
    panel.style.border = '2px solid #4CAF50';
    panel.style.borderRadius = '10px';
    panel.style.padding = '15px';
    panel.style.color = 'white';

    // عنوان اللوحة
    const title = document.createElement('div');
    title.innerHTML = '✍️ <strong>لوحة الكتابة التفاعلية المباشرة</strong>';
    title.style.fontSize = '16px';
    title.style.marginBottom = '10px';
    title.style.textAlign = 'center';
    title.style.borderBottom = '1px solid rgba(255,255,255,0.3)';
    title.style.paddingBottom = '8px';

    // منطقة عرض النص المكتوب
    const textDisplay = document.createElement('div');
    textDisplay.id = 'liveTextDisplay';
    textDisplay.style.background = 'rgba(0, 0, 0, 0.7)';
    textDisplay.style.border = '1px solid #00ff00';
    textDisplay.style.borderRadius = '5px';
    textDisplay.style.padding = '10px';
    textDisplay.style.minHeight = '100px';
    textDisplay.style.maxHeight = '200px';
    textDisplay.style.overflowY = 'auto';
    textDisplay.style.fontFamily = 'Courier New, monospace';
    textDisplay.style.fontSize = '14px';
    textDisplay.style.color = '#00ff00';
    textDisplay.style.whiteSpace = 'pre-wrap';
    textDisplay.innerHTML = 'جاهز للكتابة المباشرة...';

    // أزرار سريعة للكتابة
    const quickButtons = document.createElement('div');
    quickButtons.style.display = 'flex';
    quickButtons.style.gap = '5px';
    quickButtons.style.marginTop = '10px';
    quickButtons.style.flexWrap = 'wrap';

    const quickActions = [
        { text: 'شرح الكود', action: () => writeExplanation() },
        { text: 'حل المشكلة', action: () => writeSolution() },
        { text: 'كتابة مثال', action: () => writeExample() },
        { text: 'نصائح', action: () => writeTips() }
    ];

    quickActions.forEach(item => {
        const btn = document.createElement('button');
        btn.textContent = item.text;
        btn.style.background = 'rgba(76, 175, 80, 0.8)';
        btn.style.color = 'white';
        btn.style.border = 'none';
        btn.style.padding = '5px 10px';
        btn.style.borderRadius = '15px';
        btn.style.fontSize = '12px';
        btn.style.cursor = 'pointer';
        btn.style.transition = 'all 0.3s';
        btn.onclick = item.action;

        btn.onmouseover = () => {
            btn.style.background = 'rgba(76, 175, 80, 1)';
            btn.style.transform = 'scale(1.05)';
        };
        btn.onmouseout = () => {
            btn.style.background = 'rgba(76, 175, 80, 0.8)';
            btn.style.transform = 'scale(1)';
        };

        quickButtons.appendChild(btn);
    });

    panel.appendChild(title);
    panel.appendChild(textDisplay);
    panel.appendChild(quickButtons);

    return panel;
}

// متغيرات المساعد المرئي
let visualAnalysisInterval = null;
let assistantVoiceEnabled = true;
let analysisCount = 0;
let isWriting = false;
let currentWritingText = '';

// ===== وظائف الكتابة التفاعلية =====

// بدء الكتابة المباشرة
function startLiveWriting() {
    const overlay = document.getElementById('writingOverlay');
    const display = document.getElementById('liveTextDisplay');

    if (!overlay || !display) return;

    overlay.innerHTML = '✍️ جاري الكتابة المباشرة...';
    display.innerHTML = 'بدء الكتابة المباشرة...\n';

    speakText('سأبدأ الآن بالكتابة المباشرة على الشاشة');

    const liveText = `🔴 الكتابة المباشرة نشطة

أهلاً! أنا المساعد التقني وأكتب لك مباشرة أثناء مشاهدة شاشتك.

يمكنني:
✅ كتابة الأكواد مباشرة
✅ شرح ما أراه على الشاشة
✅ تقديم حلول فورية
✅ كتابة التعليقات والملاحظات

اطلب مني أي شيء وسأكتبه فوراً!`;

    typeTextLive(liveText, overlay, display);
}

// تحليل وكتابة
async function performAnalysisAndWrite() {
    const overlay = document.getElementById('writingOverlay');
    const display = document.getElementById('liveTextDisplay');

    if (!overlay || !display) return;

    overlay.innerHTML = '🔍 جاري التحليل والكتابة...';
    speakText('سأحلل ما أراه على الشاشة وأكتب تحليلاً مفصلاً');

    const analysisText = `📊 تحليل مباشر للشاشة:

🖥️ ما أراه الآن:
- واجهة المساعد التقني الذكي
- نوافذ متعددة مفتوحة
- بيئة عمل تقنية نشطة

💡 التحليل:
- المستخدم يعمل على تطوير تطبيق ذكي
- الواجهة منظمة وسهلة الاستخدام
- هناك إمكانيات متقدمة للتفاعل

🎯 التوصيات:
- استمر في التطوير بهذا المستوى
- جرب الميزات الصوتية
- استخدم مشاركة الشاشة للتعلم

✨ ملاحظة: أستطيع مساعدتك في أي شيء تراه!`;

    typeTextLive(analysisText, overlay, display);
}

// كتابة كود
function startCodeWriting() {
    const overlay = document.getElementById('writingOverlay');
    const display = document.getElementById('liveTextDisplay');

    if (!overlay || !display) return;

    overlay.innerHTML = '💻 جاري كتابة الكود...';
    speakText('سأكتب لك مثال على كود JavaScript مفيد');

    const codeText = `💻 مثال كود JavaScript:

// وظيفة للتفاعل مع المساعد
function interactWithAssistant() {
    console.log('🤖 بدء التفاعل مع المساعد');

    // إرسال رسالة
    const message = "مرحباً أيها المساعد الذكي";
    sendMessage(message);

    // تفعيل الصوت
    if (speechSettings.enabled) {
        speakText("أهلاً بك! كيف يمكنني مساعدتك؟");
    }

    // بدء المحادثة التفاعلية
    startVoiceConversation();
}

// استدعاء الوظيفة
interactWithAssistant();

/*
✨ هذا الكود يوضح كيفية التفاعل
   مع المساعد التقني بطريقة برمجية
*/`;

    typeTextLive(codeText, overlay, display);
}

// كتابة شرح
function writeExplanation() {
    const explanationText = `📚 شرح مفصل:

🎯 الهدف: فهم كيفية عمل المساعد التقني

🔧 المكونات الأساسية:
1. واجهة المستخدم (HTML/CSS)
2. منطق التطبيق (JavaScript)
3. التفاعل الصوتي (Speech API)
4. الذكاء الاصطناعي (LM Studio)

⚡ كيف يعمل:
- المستخدم يكتب أو يتحدث
- النظام يحلل المدخل
- يرسل للذكاء الاصطناعي
- يعرض الرد نصياً وصوتياً

💡 الميزات المتقدمة:
- مشاركة الشاشة مع التحليل
- الكتابة المباشرة أثناء البث
- التفاعل الصوتي المستمر`;

    const display = document.getElementById('liveTextDisplay');
    if (display) {
        typeTextLive(explanationText, null, display);
    }
}

// كتابة حل
function writeSolution() {
    const solutionText = `🔧 حل المشكلة:

❓ المشكلة الشائعة: عدم عمل الصوت

✅ الحلول المتدرجة:

1️⃣ تحقق من الإعدادات:
   - تأكد من تفعيل الصوت في المتصفح
   - اسمح بالوصول للميكروفون

2️⃣ إعادة تحميل الصفحة:
   - اضغط F5 أو Ctrl+R
   - امنح الأذونات مرة أخرى

3️⃣ تحقق من LM Studio:
   - تأكد من تشغيله على المنفذ 1234
   - تحقق من تحميل النموذج

4️⃣ اختبار الاتصال:
   - انقر على زر الميكروفون
   - تحدث بوضوح
   - انتظر الرد

🎯 النتيجة: صوت واضح وتفاعل ممتاز!`;

    const display = document.getElementById('liveTextDisplay');
    if (display) {
        typeTextLive(solutionText, null, display);
    }
}

// كتابة مثال
function writeExample() {
    const exampleText = `📝 مثال عملي:

🎤 محادثة مع المساعد:

المستخدم: "مرحباً، كيف أتعلم البرمجة؟"

المساعد: "أهلاً بك! إليك خطة تعلم البرمجة:

1. ابدأ بـ HTML/CSS للواجهات
2. تعلم JavaScript للتفاعل
3. جرب Python للخوارزميات
4. استخدم Git لإدارة الكود
5. بناء مشاريع عملية

هل تريد تفاصيل أكثر عن أي نقطة؟"

المستخدم: "نعم، أريد تفاصيل عن JavaScript"

المساعد: "ممتاز! JavaScript هي لغة قوية..."

💡 هكذا يكون التفاعل طبيعي وفعال!`;

    const display = document.getElementById('liveTextDisplay');
    if (display) {
        typeTextLive(exampleText, null, display);
    }
}

// كتابة نصائح
function writeTips() {
    const tipsText = `💡 نصائح ذهبية:

🚀 لتحسين التفاعل:
- تحدث بوضوح ووتيرة معتدلة
- استخدم جمل قصيرة ومفهومة
- اطلب التوضيح عند الحاجة

🎯 لأفضل النتائج:
- شغل LM Studio قبل البدء
- تأكد من جودة الإنترنت
- استخدم سماعات لتجنب الصدى

⚡ للاستفادة القصوى:
- جرب مشاركة الشاشة
- استخدم الأوامر الصوتية
- اطلب كتابة الأكواد مباشرة

🎊 استمتع بالتجربة التفاعلية!`;

    const display = document.getElementById('liveTextDisplay');
    if (display) {
        typeTextLive(tipsText, null, display);
    }
}

// كتابة النص بتأثير الطباعة المباشرة
function typeTextLive(text, overlay, display) {
    if (isWriting) return; // منع التداخل

    isWriting = true;
    currentWritingText = '';

    if (display) {
        display.innerHTML = '';
    }

    let index = 0;
    const typingSpeed = 30; // سرعة الكتابة (ميلي ثانية)

    function typeChar() {
        if (index < text.length) {
            currentWritingText += text.charAt(index);

            if (display) {
                display.innerHTML = currentWritingText + '<span class="cursor">|</span>';
                display.scrollTop = display.scrollHeight;
            }

            if (overlay) {
                const progress = Math.round((index / text.length) * 100);
                overlay.innerHTML = `✍️ جاري الكتابة... ${progress}%`;
            }

            index++;
            setTimeout(typeChar, typingSpeed);
        } else {
            // انتهاء الكتابة
            isWriting = false;

            if (display) {
                display.innerHTML = currentWritingText;
            }

            if (overlay) {
                overlay.innerHTML = '✅ تم الانتهاء من الكتابة';
            }

            speakText('تم الانتهاء من الكتابة المباشرة');
        }
    }

    typeChar();
}

// مسح منطقة الكتابة
function clearWritingOverlay() {
    const overlay = document.getElementById('writingOverlay');
    const display = document.getElementById('liveTextDisplay');

    if (overlay) {
        overlay.innerHTML = '💻 تم مسح المحتوى - جاهز للكتابة الجديدة';
    }

    if (display) {
        display.innerHTML = 'تم المسح... جاهز للمحتوى الجديد';
    }

    isWriting = false;
    currentWritingText = '';

    speakText('تم مسح المحتوى، جاهز للكتابة الجديدة');
}

// بدء التحليل المرئي المستمر
function startVisualAnalysis() {
    console.log('🔍 بدء التحليل المرئي المستمر');

    // تحليل دوري كل 15 ثانية (أقل تكراراً لتجنب الإزعاج)
    visualAnalysisInterval = setInterval(() => {
        if (assistantVoiceEnabled && isInConversation) {
            performPeriodicAnalysis();
        }
    }, 15000);

    // تحليل فوري بعد 5 ثوان من البداية
    setTimeout(() => {
        if (assistantVoiceEnabled && isInConversation) {
            performInstantAnalysis();
        }
    }, 5000);
}

// إيقاف التحليل المرئي
function stopVisualAnalysis() {
    if (visualAnalysisInterval) {
        clearInterval(visualAnalysisInterval);
        visualAnalysisInterval = null;
    }
    analysisCount = 0;
    console.log('⏹️ تم إيقاف التحليل المرئي');
}

// تحليل فوري للشاشة مع الكتابة
function performInstantAnalysis() {
    analysisCount++;
    const overlay = document.getElementById('analysisOverlay');
    const writingOverlay = document.getElementById('writingOverlay');

    if (overlay) {
        overlay.innerHTML = '🔍 جاري التحليل الفوري والكتابة...';
    }

    if (writingOverlay) {
        writingOverlay.innerHTML = '🧠 تحليل ذكي جاري...';
    }

    // تحليل ذكي مع كتابة مباشرة
    setTimeout(() => {
        const analysis = generateAdvancedScreenAnalysis();

        if (overlay) {
            overlay.innerHTML = `🤖 التحليل #${analysisCount}: ${analysis.text}`;
        }

        // كتابة التحليل مباشرة
        if (writingOverlay) {
            const analysisReport = `📊 تقرير التحليل الفوري #${analysisCount}

🔍 ما أراه الآن:
${analysis.details}

💡 التحليل الذكي:
${analysis.insights}

🎯 التوصيات:
${analysis.recommendations}

⚡ الخلاصة: ${analysis.summary}`;

            const display = document.getElementById('liveTextDisplay');
            typeTextLive(analysisReport, writingOverlay, display);
        }

        addMessage('assistant', `تحليل مرئي متقدم: ${analysis.text}`);

        if (assistantVoiceEnabled) {
            speakText(analysis.voice);
        }
    }, 2000);
}

// تحليل دوري
function performPeriodicAnalysis() {
    const analysis = generatePeriodicAnalysis();
    const overlay = document.getElementById('analysisOverlay');

    if (overlay) {
        overlay.innerHTML = `🔄 تحليل دوري: ${analysis.text}`;
    }

    if (assistantVoiceEnabled && Math.random() > 0.3) { // 70% احتمال للتعليق الصوتي
        speakText(analysis.voice);
    }
}

// توليد تحليل ذكي للشاشة
function generateScreenAnalysis() {
    const currentTime = new Date().getHours();
    const timeOfDay = currentTime < 12 ? 'صباح' : currentTime < 18 ? 'بعد الظهر' : 'مساء';

    const contextualAnalyses = [
        {
            text: `أرى أنك تعمل في فترة ${timeOfDay}. الشاشة تظهر نشاطاً تقنياً`,
            voice: `أراك تعمل بجد في هذا ${timeOfDay}. ما الذي تعمل عليه؟ يمكنني مساعدتك`
        },
        {
            text: 'أرى واجهة المساعد التقني. تطبيق رائع للعمل التقني',
            voice: 'أرى أنك تستخدم المساعد التقني الذكي. هذا اختيار ممتاز! كيف يمكنني مساعدتك اليوم؟'
        },
        {
            text: 'الشاشة تحتوي على عناصر تفاعلية متعددة',
            voice: 'أرى عناصر تفاعلية كثيرة على الشاشة. هل تريد مني شرح أي منها أو مساعدتك في استخدامها؟'
        },
        {
            text: 'يبدو أنك في بيئة تطوير أو عمل تقني',
            voice: 'أرى أنك في بيئة عمل تقنية. أنا خبير في البرمجة والتقنية، يمكنني مساعدتك في أي شيء'
        },
        {
            text: 'النشاط يشير إلى عمل إنتاجي ومركز',
            voice: 'أرى أنك تعمل بتركيز. هذا رائع! إذا احتجت استراحة أو مساعدة، أنا هنا'
        },
        {
            text: 'الشاشة تظهر تنظيماً جيداً للعمل',
            voice: 'أعجبني تنظيم عملك على الشاشة. هل تريد مني اقتراح طرق لتحسين الإنتاجية أكثر؟'
        }
    ];

    return contextualAnalyses[Math.floor(Math.random() * contextualAnalyses.length)];
}

// توليد تحليل متقدم للشاشة
function generateAdvancedScreenAnalysis() {
    const currentTime = new Date();
    const timeString = currentTime.toLocaleTimeString('ar-SA');

    const advancedAnalyses = [
        {
            text: 'تحليل متقدم: واجهة تقنية متطورة مع إمكانيات تفاعلية',
            details: '- واجهة المساعد التقني الذكي نشطة\n- عدة أدوات متاحة للاستخدام\n- بيئة عمل منظمة ومتقدمة',
            insights: '- المستخدم يعمل على مشروع تقني متقدم\n- هناك اهتمام بالذكاء الاصطناعي\n- التفاعل الصوتي مفعل',
            recommendations: '- استكشاف جميع الميزات المتاحة\n- تجربة المحادثة الصوتية\n- استخدام مشاركة الشاشة للتعلم',
            summary: 'بيئة عمل ممتازة مع إمكانيات لا محدودة',
            voice: 'أرى واجهة تقنية متطورة جداً. هذا مشروع رائع! يمكنني مساعدتك في تطويره أكثر'
        },
        {
            text: 'تحليل متقدم: نشاط تطويري مكثف مع أدوات متنوعة',
            details: '- نوافذ متعددة مفتوحة\n- أدوات تطوير متقدمة\n- تفاعل مستمر مع النظام',
            insights: '- مطور محترف يعمل على مشروع معقد\n- استخدام فعال للأدوات المتاحة\n- تركيز عالي على الجودة',
            recommendations: '- الاستمرار في هذا المستوى الممتاز\n- توثيق العمل المنجز\n- مشاركة الخبرات مع الآخرين',
            summary: 'عمل احترافي متميز يستحق التقدير',
            voice: 'أرى عملاً احترافياً متميزاً! مستوى التطوير عالي جداً. هل تريد مني مساعدتك في أي جانب تقني؟'
        },
        {
            text: 'تحليل متقدم: بيئة تعلم وتطوير تفاعلية',
            details: '- تطبيق ذكي مع ميزات متقدمة\n- واجهة سهلة الاستخدام\n- تكامل ممتاز بين المكونات',
            insights: '- التركيز على تجربة المستخدم\n- اهتمام بالتقنيات الحديثة\n- رؤية واضحة للمشروع',
            recommendations: '- إضافة المزيد من الميزات التفاعلية\n- تحسين الأداء باستمرار\n- جمع ملاحظات المستخدمين',
            summary: 'مشروع واعد مع إمكانيات نمو كبيرة',
            voice: 'هذا مشروع واعد جداً! أرى إمكانيات هائلة للنمو والتطوير. كيف يمكنني المساهمة في نجاحه؟'
        }
    ];

    return advancedAnalyses[Math.floor(Math.random() * advancedAnalyses.length)];
}

// توليد تحليل دوري
function generatePeriodicAnalysis() {
    const analyses = [
        {
            text: 'مراقبة مستمرة... كل شيء يبدو طبيعياً',
            voice: 'أراقب شاشتك باستمرار. كل شيء يبدو جيداً'
        },
        {
            text: 'تحديث دوري: النشاط مستمر على الشاشة',
            voice: 'أرى نشاطاً مستمراً. هل تحتاج مساعدة في شيء محدد؟'
        },
        {
            text: 'فحص دوري: الشاشة تعمل بشكل طبيعي',
            voice: 'فحص دوري مكتمل. أستطيع مساعدتك في أي وقت'
        }
    ];

    return analyses[Math.floor(Math.random() * analyses.length)];
}

// بدء التعليق الصوتي المباشر
function startVoiceCommentary() {
    addMessage('assistant', 'بدء التعليق الصوتي المباشر...');
    speakText('سأبدأ الآن بالتعليق الصوتي المباشر على ما أراه على شاشتك');

    // تعليق فوري
    setTimeout(() => {
        const commentary = generateVoiceCommentary();
        addMessage('assistant', `تعليق مباشر: ${commentary}`);
        speakText(commentary);
    }, 2000);
}

// توليد تعليق صوتي تفاعلي
function generateVoiceCommentary() {
    const interactiveCommentaries = [
        'أرى أنك تستخدم المساعد التقني الذكي. هذا اختيار ممتاز! هل تريد مني شرح أي من الميزات المتاحة؟',
        'الواجهة تبدو منظمة وسهلة الاستخدام. يمكنني مساعدتك في استكشاف جميع الأدوات المتاحة',
        'أرى إمكانيات رائعة في هذا التطبيق. هل تريد مني أن أوضح لك كيفية استخدام أي منها؟',
        'بيئة العمل تبدو احترافية ومتقدمة. ما نوع المشروع الذي تعمل عليه؟ يمكنني تقديم نصائح مفيدة',
        'أرى أنك تعمل بتركيز وكفاءة. هل تحتاج مساعدة في تحسين سير العمل أو حل أي مشكلة تقنية؟',
        'الشاشة تظهر تنوعاً في الأدوات المتاحة. أي منها تستخدم أكثر؟ يمكنني إعطاؤك نصائح لاستخدامها بفعالية أكبر',
        'أرى أنك تتنقل بين عدة عناصر. هل تريد مني تنظيم سير العمل أو اقتراح طرق أفضل للتنقل؟'
    ];

    return interactiveCommentaries[Math.floor(Math.random() * interactiveCommentaries.length)];
}

// تبديل صوت المساعد
function toggleAssistantVoice(button) {
    assistantVoiceEnabled = !assistantVoiceEnabled;

    if (assistantVoiceEnabled) {
        button.innerHTML = '<i class="fas fa-volume-up"></i> إيقاف الصوت';
        button.style.background = '#9C27B0';
        addMessage('assistant', 'تم تفعيل الصوت');
        speakText('تم تفعيل صوت المساعد المرئي');
    } else {
        button.innerHTML = '<i class="fas fa-volume-mute"></i> تفعيل الصوت';
        button.style.background = '#757575';
        addMessage('assistant', 'تم إيقاف الصوت');
    }
}

// إعدادات الصوت المتقدمة
function openVoiceSettings() {
    createAdvancedVoiceSettingsModal();
}

// إنشاء نافذة إعدادات الصوت المتقدمة
function createAdvancedVoiceSettingsModal() {
    // إزالة النافذة السابقة إن وجدت
    const existingModal = document.getElementById('advancedVoiceModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = 'advancedVoiceModal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        backdrop-filter: blur(10px);
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        padding: 30px;
        border-radius: 20px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        border: 2px solid #4CAF50;
    `;

    content.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px;">
            <h2 style="color: #fff; margin: 0; font-size: 28px;">🎤 إعدادات الصوت المتقدمة</h2>
            <p style="color: #ccc; margin: 10px 0;">تخصيص النظام الصوتي Real-Time</p>
        </div>

        <div style="display: grid; gap: 20px;">
            <!-- إعدادات STT -->
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h3 style="color: #FFD700; margin: 0 0 15px 0;">🎙️ تحويل الصوت إلى نص (STT)</h3>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">مقدم الخدمة:</label>
                    <select id="sttProvider" style="width: 100%; padding: 10px; border-radius: 8px; border: none; background: #333; color: #fff;">
                        <option value="browser">المتصفح (مجاني)</option>
                        <option value="google">Google Speech API</option>
                        <option value="whisper">Whisper (محلي)</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">اللغة:</label>
                    <select id="sttLanguage" style="width: 100%; padding: 10px; border-radius: 8px; border: none; background: #333; color: #fff;">
                        <option value="ar-SA">العربية (السعودية)</option>
                        <option value="ar-IQ">العربية (العراق)</option>
                        <option value="ar-EG">العربية (مصر)</option>
                        <option value="en-US">الإنجليزية</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">جودة التعرف:</label>
                    <select id="sttQuality" style="width: 100%; padding: 10px; border-radius: 8px; border: none; background: #333; color: #fff;">
                        <option value="high">عالية (أبطأ)</option>
                        <option value="medium">متوسطة (متوازن)</option>
                        <option value="low">منخفضة (أسرع)</option>
                    </select>
                </div>
            </div>

            <!-- إعدادات TTS -->
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h3 style="color: #FFD700; margin: 0 0 15px 0;">🔊 تحويل النص إلى صوت (TTS)</h3>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">مقدم الخدمة:</label>
                    <select id="ttsProvider" style="width: 100%; padding: 10px; border-radius: 8px; border: none; background: #333; color: #fff;">
                        <option value="browser">المتصفح (مجاني)</option>
                        <option value="google">Google TTS</option>
                        <option value="elevenlabs">ElevenLabs (جودة عالية)</option>
                        <option value="coqui">Coqui TTS (محلي)</option>
                    </select>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">سرعة الكلام: <span id="rateValue">${voiceSettings.rate}</span></label>
                    <input type="range" id="speechRate" min="0.5" max="2" step="0.1" value="${voiceSettings.rate}"
                           style="width: 100%; margin: 5px 0;">
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">نبرة الصوت: <span id="pitchValue">${voiceSettings.pitch}</span></label>
                    <input type="range" id="speechPitch" min="0.5" max="2" step="0.1" value="${voiceSettings.pitch}"
                           style="width: 100%; margin: 5px 0;">
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: block; margin-bottom: 5px;">مستوى الصوت: <span id="volumeValue">${voiceSettings.volume}</span></label>
                    <input type="range" id="speechVolume" min="0" max="1" step="0.1" value="${voiceSettings.volume}"
                           style="width: 100%; margin: 5px 0;">
                </div>
            </div>

            <!-- إعدادات متقدمة -->
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h3 style="color: #FFD700; margin: 0 0 15px 0;">⚙️ إعدادات متقدمة</h3>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="autoSpeak" ${voiceSettings.autoSpeak ? 'checked' : ''}
                               style="margin-left: 10px; transform: scale(1.2);">
                        تشغيل الردود تلقائياً
                    </label>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="continuousMode" ${voiceSettings.continuousMode ? 'checked' : ''}
                               style="margin-left: 10px; transform: scale(1.2);">
                        الاستماع المستمر
                    </label>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="realTimeResponse" ${voiceSettings.realTimeResponse ? 'checked' : ''}
                               style="margin-left: 10px; transform: scale(1.2);">
                        الرد الفوري Real-Time
                    </label>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="color: #fff; display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="interruptionHandling" ${voiceSettings.interruptionHandling ? 'checked' : ''}
                               style="margin-left: 10px; transform: scale(1.2);">
                        التعامل مع المقاطعة
                    </label>
                </div>
            </div>

            <!-- أزرار التحكم -->
            <div style="display: flex; gap: 15px; justify-content: center; margin-top: 20px;">
                <button onclick="testVoiceSettings()" style="
                    background: linear-gradient(45deg, #4CAF50, #45a049);
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 16px;
                    transition: all 0.3s;
                ">🎤 اختبار الصوت</button>

                <button onclick="saveAdvancedVoiceSettings()" style="
                    background: linear-gradient(45deg, #2196F3, #1976D2);
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 16px;
                    transition: all 0.3s;
                ">💾 حفظ الإعدادات</button>

                <button onclick="closeAdvancedVoiceSettings()" style="
                    background: linear-gradient(45deg, #f44336, #d32f2f);
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 16px;
                    transition: all 0.3s;
                ">❌ إغلاق</button>
            </div>
        </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // إعداد معالجات الأحداث
    setupAdvancedVoiceSettingsEvents();

    // تحميل الإعدادات الحالية
    loadCurrentVoiceSettings();
}

// إعداد معالجات أحداث الإعدادات المتقدمة
function setupAdvancedVoiceSettingsEvents() {
    // تحديث القيم المعروضة عند تغيير المنزلقات
    const rateSlider = document.getElementById('speechRate');
    const pitchSlider = document.getElementById('speechPitch');
    const volumeSlider = document.getElementById('speechVolume');

    if (rateSlider) {
        rateSlider.addEventListener('input', (e) => {
            document.getElementById('rateValue').textContent = e.target.value;
        });
    }

    if (pitchSlider) {
        pitchSlider.addEventListener('input', (e) => {
            document.getElementById('pitchValue').textContent = e.target.value;
        });
    }

    if (volumeSlider) {
        volumeSlider.addEventListener('input', (e) => {
            document.getElementById('volumeValue').textContent = e.target.value;
        });
    }

    // إغلاق النافذة عند النقر خارجها
    const modal = document.getElementById('advancedVoiceModal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeAdvancedVoiceSettings();
            }
        });
    }
}

// تحميل الإعدادات الحالية في النافذة
function loadCurrentVoiceSettings() {
    // تحميل مقدمي الخدمة
    const sttProvider = document.getElementById('sttProvider');
    const ttsProvider = document.getElementById('ttsProvider');
    const sttLanguage = document.getElementById('sttLanguage');

    if (sttProvider) sttProvider.value = voiceSettings.sttProvider;
    if (ttsProvider) ttsProvider.value = voiceSettings.ttsProvider;
    if (sttLanguage) sttLanguage.value = voiceSettings.language;

    // تحميل إعدادات الصوت
    const rateSlider = document.getElementById('speechRate');
    const pitchSlider = document.getElementById('speechPitch');
    const volumeSlider = document.getElementById('speechVolume');

    if (rateSlider) {
        rateSlider.value = voiceSettings.rate;
        document.getElementById('rateValue').textContent = voiceSettings.rate;
    }
    if (pitchSlider) {
        pitchSlider.value = voiceSettings.pitch;
        document.getElementById('pitchValue').textContent = voiceSettings.pitch;
    }
    if (volumeSlider) {
        volumeSlider.value = voiceSettings.volume;
        document.getElementById('volumeValue').textContent = voiceSettings.volume;
    }

    // تحميل الإعدادات المتقدمة
    const autoSpeak = document.getElementById('autoSpeak');
    const continuousMode = document.getElementById('continuousMode');
    const realTimeResponse = document.getElementById('realTimeResponse');
    const interruptionHandling = document.getElementById('interruptionHandling');

    if (autoSpeak) autoSpeak.checked = voiceSettings.autoSpeak;
    if (continuousMode) continuousMode.checked = voiceSettings.continuousMode;
    if (realTimeResponse) realTimeResponse.checked = voiceSettings.realTimeResponse;
    if (interruptionHandling) interruptionHandling.checked = voiceSettings.interruptionHandling;
}

// حفظ الإعدادات المتقدمة
function saveAdvancedVoiceSettings() {
    try {
        // حفظ مقدمي الخدمة
        const sttProvider = document.getElementById('sttProvider');
        const ttsProvider = document.getElementById('ttsProvider');
        const sttLanguage = document.getElementById('sttLanguage');

        if (sttProvider) voiceSettings.sttProvider = sttProvider.value;
        if (ttsProvider) voiceSettings.ttsProvider = ttsProvider.value;
        if (sttLanguage) voiceSettings.language = sttLanguage.value;

        // حفظ إعدادات الصوت
        const rateSlider = document.getElementById('speechRate');
        const pitchSlider = document.getElementById('speechPitch');
        const volumeSlider = document.getElementById('speechVolume');

        if (rateSlider) voiceSettings.rate = parseFloat(rateSlider.value);
        if (pitchSlider) voiceSettings.pitch = parseFloat(pitchSlider.value);
        if (volumeSlider) voiceSettings.volume = parseFloat(volumeSlider.value);

        // حفظ الإعدادات المتقدمة
        const autoSpeak = document.getElementById('autoSpeak');
        const continuousMode = document.getElementById('continuousMode');
        const realTimeResponse = document.getElementById('realTimeResponse');
        const interruptionHandling = document.getElementById('interruptionHandling');

        if (autoSpeak) voiceSettings.autoSpeak = autoSpeak.checked;
        if (continuousMode) voiceSettings.continuousMode = continuousMode.checked;
        if (realTimeResponse) voiceSettings.realTimeResponse = realTimeResponse.checked;
        if (interruptionHandling) voiceSettings.interruptionHandling = interruptionHandling.checked;

        // تحديث إعدادات الكلام
        if (typeof speechSettings !== 'undefined') {
            speechSettings.language = voiceSettings.language;
            speechSettings.rate = voiceSettings.rate;
            speechSettings.pitch = voiceSettings.pitch;
            speechSettings.volume = voiceSettings.volume;
        }

        // حفظ في التخزين المحلي
        localStorage.setItem('advancedVoiceSettings', JSON.stringify(voiceSettings));

        // إشعار بالحفظ
        showNotification('✅ تم حفظ الإعدادات المتقدمة بنجاح!', 'success');

        console.log('✅ تم حفظ الإعدادات المتقدمة:', voiceSettings);

        // إغلاق النافذة
        setTimeout(() => {
            closeAdvancedVoiceSettings();
        }, 1000);

    } catch (error) {
        console.error('❌ خطأ في حفظ الإعدادات:', error);
        showNotification('❌ خطأ في حفظ الإعدادات', 'error');
    }
}

// اختبار الإعدادات الصوتية
function testVoiceSettings() {
    const testText = 'مرحباً! هذا اختبار للإعدادات الصوتية المتقدمة. كيف يبدو صوتي الآن؟';

    // تطبيق الإعدادات المؤقتة للاختبار
    const tempSettings = {
        rate: parseFloat(document.getElementById('speechRate')?.value || voiceSettings.rate),
        pitch: parseFloat(document.getElementById('speechPitch')?.value || voiceSettings.pitch),
        volume: parseFloat(document.getElementById('speechVolume')?.value || voiceSettings.volume),
        provider: document.getElementById('ttsProvider')?.value || voiceSettings.ttsProvider
    };

    speakTextAdvanced(testText, tempSettings);
    showNotification('🎤 جاري اختبار الصوت...', 'info');
}

// إغلاق نافذة الإعدادات المتقدمة
function closeAdvancedVoiceSettings() {
    const modal = document.getElementById('advancedVoiceModal');
    if (modal) {
        modal.style.opacity = '0';
        modal.style.transform = 'scale(0.9)';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 10px;
        color: white;
        font-weight: bold;
        z-index: 10001;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;

    // تحديد لون الإشعار حسب النوع
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(45deg, #f44336, #d32f2f)';
            break;
        case 'warning':
            notification.style.background = 'linear-gradient(45deg, #ff9800, #f57c00)';
            break;
        default:
            notification.style.background = 'linear-gradient(45deg, #2196F3, #1976D2)';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إخفاء الإشعار
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function closeSettings() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// ===== ربط الأحداث =====

function initializeApp() {
    console.log('🚀 تهيئة التطبيق...');

    // ربط الأحداث
    bindEvents();

    // إصلاح الأحداث المفقودة بعد ثانية واحدة
    setTimeout(() => {
        if (window.fixButtonEvents) {
            window.fixButtonEvents();
        }
    }, 1000);

    // بدء مراقبة الاتصال الذكية
    startConnectionMonitoring();

    // رسالة ترحيب تفاعلية
    setTimeout(() => {
        const welcomeMessage = `🤖 **مرحباً! أنا المساعد التقني الذكي الشامل**

أستطيع مساعدتك في:
💻 **البرمجة**: JavaScript, Python, React, Node.js وأكثر
🌐 **تطوير الويب**: Frontend, Backend, APIs
🤖 **الذكاء الاصطناعي**: Machine Learning, NLP
🔧 **حل المشاكل التقنية**: تشخيص وإصلاح الأخطاء
📱 **تطوير التطبيقات**: Mobile, Desktop, Web

🚀 **التحكم الكامل:**
🌍 فتح المواقع: "افتح موقع جوجل"
🔍 البحث: "ابحث عن JavaScript"
💻 فتح البرامج: "افتح نوت باد"
🎵 تشغيل الوسائط: "شغل أغنية على يوتيوب"
📁 إدارة الملفات: "احفظ الملف"

🎬 **تحليل الفيديو المتقدم:**
📹 تحليل شامل للفيديوهات
🗣️ شرح المحتوى بالعربية
🎯 استخراج النقاط الرئيسية
📊 ملخصات ذكية

🌐 **ترجمة ذكية:**
🔄 ترجمة فورية عربي/إنجليزي
📝 ترجمة مفصلة مع التحليل
🎯 ترجمة حسب السياق
💡 شرح المعاني والمفردات

🎨 **عرض ثلاثي الأبعاد تفاعلي:**
🧬 شرح العلوم (DNA, الذرة, النظام الشمسي)
💻 مفاهيم البرمجة بصرياً
🧠 الذكاء الاصطناعي تفاعلياً
🌟 عروض تقديمية مذهلة

✨ **ميزات خاصة:**
🎤 محادثة صوتية تفاعلية (انقر على الميكروفون)
🖥️ مساعد صوتي مرئي (مشاركة الشاشة + تحكم كامل)
🧠 فهم السياق والذاكرة التفاعلية

🤖 **أتصل بالنموذج المحلي لأعطيك ردود ذكية مثل ChatGPT!**

📋 **للبدء:**
1. تأكد من تشغيل **النموذج المحلي**
2. تأكد من أن الخادم يعمل بشكل صحيح
3. ستظهر حالة الاتصال في أعلى الشاشة

💬 **للدردشة النصية:** اكتب سؤالك في المربع أدناه
🎤 **للمحادثة الصوتية:** انقر على "محادثة خالصة" للتحدث معي مثل ChatGPT

**جرب أن تسأل:** "كيف أتعلم البرمجة؟" أو "اشرح لي الذكاء الاصطناعي" أو أي سؤال تقني!`;

        addMessage('assistant', welcomeMessage);

        if (speechSettings.enabled) {
            const voiceWelcome = 'مرحباً بك! أنا مساعدك التقني الذكي. أتصل بالنموذج المحلي لأعطيك ردود ذكية مثل ChatGPT. تأكد من تشغيل النموذج المحلي أولاً، ثم اسألني أي سؤال تقني';

            setTimeout(() => {
                speakText(voiceWelcome);
            }, 1000);
        }
    }, 2000);

    console.log('✅ تم تهيئة التطبيق');
}

// تبديل وضع المحادثة الصوتية الخالصة
function togglePureVoiceMode() {
    console.log('🎤 بدء تبديل وضع المحادثة الصوتية الخالصة');

    // فحص وتهيئة المتغيرات المطلوبة
    if (typeof window.isContinuousListening === 'undefined') {
        window.isContinuousListening = false;
    }
    if (typeof window.isInConversation === 'undefined') {
        window.isInConversation = false;
    }

    const pureVoiceBtn = document.getElementById('pureVoiceBtn');
    const chatContainer = document.getElementById('chatContainer');

    if (speechSettings.pureVoiceMode) {
        // إيقاف الوضع الصوتي الخالص
        console.log('❌ إيقاف الوضع الصوتي الخالص');

        speechSettings.pureVoiceMode = false;
        speechSettings.showTextInVoiceMode = true;
        speechSettings.conversationMode = false;
        isInConversation = false;

        // إيقاف الاستماع
        if (window.recognition) {
            window.recognition.stop();
        }

        // إيقاف الصوت
        speechSynthesis.cancel();

        // تحديث الزر
        if (pureVoiceBtn) {
            pureVoiceBtn.classList.remove('active');
            pureVoiceBtn.innerHTML = '<i class="fas fa-comments"></i><span>محادثة خالصة</span>';
        }

        // إظهار الدردشة
        if (chatContainer) {
            chatContainer.style.display = 'block';
        }

        // إزالة مؤشر المحادثة الصوتية
        const voiceIndicator = document.getElementById('voiceOnlyIndicator');
        if (voiceIndicator) {
            voiceIndicator.remove();
        }

        // رسالة تأكيد
        addMessage('assistant', 'تم إيقاف وضع المحادثة الصوتية الخالصة. عدت للواجهة الرئيسية');

        console.log('✅ تم العودة للواجهة الرئيسية');

    } else {
        // تفعيل الوضع الصوتي الخالص
        console.log('🔄 تفعيل الوضع الصوتي الخالص...');

        speechSettings.pureVoiceMode = true;
        speechSettings.showTextInVoiceMode = false;
        speechSettings.conversationMode = true;
        isInConversation = true;

        // تحديث الزر
        if (pureVoiceBtn) {
            pureVoiceBtn.classList.add('active');
            pureVoiceBtn.innerHTML = '<i class="fas fa-comments"></i><span>اضغط للخروج</span>';
        }

        // إخفاء الدردشة
        if (chatContainer) {
            chatContainer.style.display = 'none';
        }

        // إنشاء مؤشر المحادثة الصوتية
        createVoiceOnlyIndicator();

        // بدء المحادثة فوراً
        console.log('🎤 بدء المحادثة الصوتية...');

        // رسالة ترحيب قصيرة وواضحة
        speakText('مرحباً! أنا مساعدك الذكي. تحدث معي', {
            rate: 0.7, // سرعة أبطأ للوضوح
            onStart: () => {
                console.log('🔊 بدء تشغيل رسالة الترحيب');
            },
            onEnd: () => {
                console.log('🔊 انتهت رسالة الترحيب، بدء الاستماع...');
                updateVoiceIndicator('🟢 المساعد يستمع... تحدث الآن');
                setTimeout(() => {
                    startContinuousListening();
                }, 1000); // وقت أطول للتأكد
            }
        });

        // احتياطي: بدء الاستماع حتى لو لم يعمل الصوت
        setTimeout(() => {
            if (isInConversation && !isContinuousListening) {
                console.log('🎤 بدء الاستماع الاحتياطي...');
                if (typeof startContinuousListening === 'function') {
                    startContinuousListening();
                } else {
                    console.warn('⚠️ وظيفة startContinuousListening غير متاحة');
                }
            }
        }, 3000);

        console.log('✅ تم تفعيل الوضع الصوتي الخالص');
    }
}

// إنشاء مؤشر المحادثة الصوتية الخالصة
function createVoiceOnlyIndicator() {
    // إزالة أي مؤشر سابق
    const existingIndicator = document.getElementById('voiceOnlyIndicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    const indicator = document.createElement('div');
    indicator.id = 'voiceOnlyIndicator';
    indicator.className = 'voice-only-indicator';
    indicator.innerHTML = `
        <div style="position: absolute; top: 10px; right: 10px;">
            <button id="closeVoiceMode" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px; border-radius: 50%; cursor: pointer; font-size: 16px;" title="إغلاق المحادثة الصوتية">
                ✕
            </button>
        </div>
        <h2>🎤 محادثة صوتية خالصة نشطة</h2>
        <div class="voice-wave"></div>
        <p>تحدث معي بطبيعية وسأرد عليك صوتياً مثل ChatGPT</p>
        <p style="font-size: 14px; opacity: 0.7; margin-top: 10px;">انقر على ✕ أو الزر للخروج من هذا الوضع</p>
        <div style="margin-top: 20px;">
            <div style="background: rgba(0,255,0,0.2); padding: 10px; border-radius: 8px; font-size: 14px;">
                🟢 المساعد يستمع... تحدث الآن
            </div>
        </div>
    `;

    document.body.appendChild(indicator);

    // إضافة حدث النقر للخروج
    indicator.addEventListener('click', (e) => {
        // إذا نقر على المؤشر نفسه وليس على الزر
        if (e.target === indicator) {
            togglePureVoiceMode();
        }
    });

    // إضافة حدث النقر لزر الإغلاق
    const closeBtn = document.getElementById('closeVoiceMode');
    if (closeBtn) {
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            console.log('🔴 إغلاق المحادثة الصوتية من الزر');
            togglePureVoiceMode();
        });
    }
}

// تبديل المحادثة الصوتية العادية
function toggleVoiceConversation() {
    console.log('🎤 تبديل المحادثة الصوتية العادية');

    if (isInConversation) {
        stopVoiceConversation();
    } else {
        startVoiceConversation();
    }
}

// إضافة الوظائف للنافذة العامة
window.togglePureVoiceMode = togglePureVoiceMode;
window.toggleVoiceConversation = toggleVoiceConversation;
window.startVoiceConversation = startVoiceConversation;
window.stopVoiceConversation = stopVoiceConversation;
window.sendMessage = sendMessage;

// دالة bindEvents القديمة - تم استبدالها بـ DOMContentLoaded

// تم نقل ربط الأحداث إلى ملف event-handlers.js منفصل

// تم نقل جميع أحداث الأزرار إلى ملف event-handlers.js منفصل

async function checkConnection() {
    // استخدام النظام الجديد لفحص الاتصال
    await checkModelConnection();
}

function updateConnectionStatus(isConnected) {
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.querySelector('.status-indicator span:last-child');

    if (statusDot && statusText) {
        if (isConnected) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'متصل';
        } else {
            statusDot.className = 'status-dot offline';
            statusText.textContent = 'غير متصل';
        }
    }
}

// تصدير الوظائف
window.technicalAssistant = technicalAssistant;
window.sendMessage = sendMessage;
window.startVoiceRecording = startVoiceRecording;
window.startScreenShare = startScreenShare;
window.uploadFile = uploadFile;
window.show3DView = show3DView;
window.generateSummary = generateSummary;
window.openVoiceSettings = openVoiceSettings;
window.closeSettings = closeSettings;
window.initializeApp = initializeApp;
window.addMessage = addMessage;
window.speakText = speakText;
window.copyCode = copyCode;
window.bindEvents = bindEvents;

// الوظائف مُصدرة بالفعل في نهاية الملف

if (!window.toggleBugBountyMode) {
    window.toggleBugBountyMode = function() {
        console.log('🛡️ تفعيل Bug Bounty Mode...');
        if (window.BugBountyCore && !window.bugBountyInstance) {
            window.bugBountyInstance = new BugBountyCore();
        }
        if (window.bugBountyInstance) {
            if (window.bugBountyInstance.isActive) {
                window.bugBountyInstance.deactivate();
            } else {
                window.bugBountyInstance.activate();
            }
        } else {
            console.log('❌ Bug Bounty Mode غير متاح');
        }
    };
}

if (!window.toggleFileCreatorMode) {
    window.toggleFileCreatorMode = function() {
        console.log('📁 تفعيل File Creator Mode...');
        if (window.FileCreatorCore && !window.fileCreatorInstance) {
            window.fileCreatorInstance = new FileCreatorCore();
        }
        if (window.fileCreatorInstance) {
            if (window.fileCreatorInstance.isActive) {
                window.fileCreatorInstance.deactivate();
            } else {
                window.fileCreatorInstance.activate();
            }
        } else {
            console.log('❌ File Creator Mode غير متاح');
        }
    };
}

// ===== دوال API =====

// دالة إظهار حالة API
function showAPIStatus() {
    if (!window.apiManager) {
        return 'نظام API غير متاح';
    }

    const status = apiManager.isEnabled ? 'مفعل' : 'غير مفعل';
    const currentProvider = apiManager.currentProvider
        ? apiManager.supportedProviders[apiManager.currentProvider].name
        : 'غير محدد';
    const availableProviders = apiManager.getAvailableProviders();

    let statusMessage = `🔌 حالة API: ${status}\n`;
    statusMessage += `📡 المزود الحالي: ${currentProvider}\n`;
    statusMessage += `📋 المزودين المتاحين: ${availableProviders.length}\n`;

    if (availableProviders.length > 0) {
        statusMessage += `📝 القائمة: ${availableProviders.map(p =>
            apiManager.supportedProviders[p].name
        ).join(', ')}`;
    }

    return statusMessage;
}

// دالة تبديل حالة API
function toggleAPIStatus() {
    if (!window.apiManager) {
        return 'نظام API غير متاح';
    }

    const newStatus = apiManager.toggle();

    // تحديث أزرار تكوين API
    const apiConfigBtn = document.getElementById('apiConfigBtn');
    const apiConfigInputBtn = document.getElementById('apiConfigInputBtn');

    if (apiConfigBtn) {
        if (newStatus) {
            apiConfigBtn.classList.add('active');
            apiConfigBtn.title = 'تكوين API للنماذج الخارجية - مفعل';
        } else {
            apiConfigBtn.classList.remove('active');
            apiConfigBtn.title = 'تكوين API للنماذج الخارجية - غير مفعل';
        }
    }

    if (apiConfigInputBtn) {
        if (newStatus) {
            apiConfigInputBtn.classList.add('active');
            apiConfigInputBtn.title = 'تكوين API للنماذج الخارجية - مفعل';
        } else {
            apiConfigInputBtn.classList.remove('active');
            apiConfigInputBtn.title = 'تكوين API للنماذج الخارجية - غير مفعل';
        }
    }

    return newStatus ? 'تم تفعيل API' : 'تم إيقاف API';
}

// تصدير الدوال للاستخدام العام
window.sendMessage = sendMessage;
window.addMessage = addMessage;
window.speakText = speakText;
window.startVoiceRecording = startVoiceRecording;
window.stopVoiceRecording = stopVoiceRecording;
window.displayInArea = displayInArea;
window.closeDisplayArea = closeDisplayArea;
window.openSettings = openSettings;
window.closeSettings = closeSettings;
window.executeVoiceCommand = executeVoiceCommand;
window.showAPIStatus = showAPIStatus;
window.toggleAPIStatus = toggleAPIStatus;
window.diagnoseModelStatus = diagnoseModelStatus;

// تشخيص النموذج فقط عند تحميل الصفحة (الربط يتم من safeBindEvents)
document.addEventListener('DOMContentLoaded', () => {
    // تشخيص النموذج فقط
    setTimeout(async () => {
        console.log('🔍 بدء تشخيص النموذج التلقائي...');
        const modelStatus = await diagnoseModelStatus();

        if (modelStatus) {
            console.log('✅ النموذج جاهز ويعمل بشكل صحيح');
        } else {
            console.log('❌ النموذج غير جاهز - تحقق من LM Studio');
        }
    }, 2000);
});

// فحص بسيط للأزرار المهمة
setTimeout(() => {
    console.log('🔍 فحص الأزرار المهمة...');

    const criticalButtons = ['sendBtn', 'pureVoiceBtn', 'screenShareBtn', 'videoUploadBtn'];

    criticalButtons.forEach(id => {
        const btn = document.getElementById(id);
        if (btn && btn.onclick) {
            console.log(`✅ ${id}: يعمل`);
        } else {
            console.warn(`⚠️ ${id}: لا يعمل`);
        }
    });

}, 4000);

// ===== دوال API =====

// دالة فتح إعدادات API
function openAPIConfig() {
    console.log('🔌 فتح إعدادات API...');

    if (window.apiConfigInterface && typeof window.apiConfigInterface.show === 'function') {
        window.apiConfigInterface.show();
        console.log('✅ تم فتح واجهة تكوين API');
    } else {
        console.error('❌ واجهة تكوين API غير متاحة');
        alert('واجهة تكوين API غير متاحة حالياً');
    }
}

// دالة فتح إعدادات Hugging Face
function openHFConfig() {
    console.log('🤗 فتح إعدادات Hugging Face...');

    if (window.huggingFaceSettings && typeof window.huggingFaceSettings.show === 'function') {
        window.huggingFaceSettings.show();
        console.log('✅ تم فتح واجهة Hugging Face');
    } else {
        console.error('❌ واجهة Hugging Face غير متاحة');
        alert('واجهة Hugging Face غير متاحة حالياً');
    }
}

// دالة تفعيل التحسين الذاتي
function toggleAIImprove() {
    console.log('🤖 تفعيل التحسين الذاتي...');

    if (window.aiSelfImprove && typeof window.aiSelfImprove.toggle === 'function') {
        const isActive = window.aiSelfImprove.toggle();
        console.log(`✅ التحسين الذاتي ${isActive ? 'مُفعل' : 'مُعطل'}`);

        // تحديث واجهة الزر
        const btn = document.getElementById('aiImproveBtn');
        if (btn) {
            btn.style.background = isActive ?
                'linear-gradient(135deg, #2ecc71 0%, #27ae60 100%)' :
                'linear-gradient(135deg, #34495e 0%, #2c3e50 100%)';
        }
    } else {
        console.error('❌ نظام التحسين الذاتي غير متاح');
        alert('نظام التحسين الذاتي غير متاح حالياً');
    }
}

// دالة مشاركة الشاشة
function handleScreenShare() {
    console.log('🖥️ بدء مشاركة الشاشة...');

    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        navigator.mediaDevices.getDisplayMedia({
            video: true,
            audio: true
        })
        .then(stream => {
            console.log('✅ تم الحصول على مشاركة الشاشة');

            // إنشاء عنصر فيديو لعرض الشاشة
            const videoElement = document.createElement('video');
            videoElement.srcObject = stream;
            videoElement.autoplay = true;
            videoElement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                height: 200px;
                border: 2px solid #3498db;
                border-radius: 10px;
                z-index: 10000;
                background: black;
            `;

            document.body.appendChild(videoElement);

            // إضافة زر إغلاق
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '✕';
            closeBtn.style.cssText = `
                position: fixed;
                top: 25px;
                right: 25px;
                background: red;
                color: white;
                border: none;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                cursor: pointer;
                z-index: 10001;
            `;

            closeBtn.onclick = () => {
                stream.getTracks().forEach(track => track.stop());
                videoElement.remove();
                closeBtn.remove();
                console.log('✅ تم إيقاف مشاركة الشاشة');
            };

            document.body.appendChild(closeBtn);

            // إشعار صوتي بالنظام المتقدم
            if (window.advancedVoiceEngine && window.advancedVoiceEngine.speakWithContext) {
                window.advancedVoiceEngine.speakWithContext('تم بدء مشاركة الشاشة بنجاح', {
                    emotion: 'satisfied',
                    context: 'screen_share',
                    isResponse: true
                });
            } else if (typeof speakText === 'function') {
                speakText('تم بدء مشاركة الشاشة بنجاح');
            } else {
                console.warn('⚠️ وظيفة speakText غير متاحة');
            }

        })
        .catch(error => {
            console.error('❌ خطأ في مشاركة الشاشة:', error);
            alert('فشل في بدء مشاركة الشاشة. تأكد من منح الإذن.');
        });
    } else {
        console.error('❌ مشاركة الشاشة غير مدعومة في هذا المتصفح');
        alert('مشاركة الشاشة غير مدعومة في هذا المتصفح');
    }
}

// دالة تحميل الفيديو
function handleVideoUpload() {
    console.log('📹 فتح نافذة تحميل الفيديو...');

    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'video/*';
    fileInput.style.display = 'none';

    fileInput.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            console.log('📹 تم اختيار فيديو:', file.name);

            // إنشاء URL للفيديو
            const videoURL = URL.createObjectURL(file);

            // إنشاء عنصر فيديو
            const videoElement = document.createElement('video');
            videoElement.src = videoURL;
            videoElement.controls = true;
            videoElement.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                max-width: 80%;
                max-height: 80%;
                border: 2px solid #3498db;
                border-radius: 10px;
                z-index: 10000;
                background: black;
            `;

            // إنشاء خلفية
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 9999;
            `;

            // إضافة زر إغلاق
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '✕ إغلاق';
            closeBtn.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: red;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                cursor: pointer;
                z-index: 10001;
            `;

            closeBtn.onclick = () => {
                overlay.remove();
                videoElement.remove();
                closeBtn.remove();
                URL.revokeObjectURL(videoURL);
                console.log('✅ تم إغلاق الفيديو');
            };

            document.body.appendChild(overlay);
            document.body.appendChild(videoElement);
            document.body.appendChild(closeBtn);

            // إشعار صوتي
            if (typeof speakText === 'function') {
                speakText(`تم تحميل الفيديو ${file.name} بنجاح`);
            }
        }
    };

    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
}

// دالة تحليل الفيديو
function handleVideoAnalyze() {
    console.log('📊 بدء تحليل الفيديو...');

    // إنشاء واجهة تحليل الفيديو
    const analysisModal = document.createElement('div');
    analysisModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    analysisModal.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 15px; max-width: 500px; text-align: center;">
            <h3>📊 تحليل الفيديو الذكي</h3>
            <p>اختر فيديو لتحليله باستخدام الذكاء الاصطناعي</p>
            <input type="file" accept="video/*" id="videoAnalysisInput" style="margin: 20px 0;">
            <div>
                <button id="startAnalysis" style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">بدء التحليل</button>
                <button id="cancelAnalysis" style="background: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 5px; cursor: pointer;">إلغاء</button>
            </div>
            <div id="analysisResult" style="margin-top: 20px; display: none;">
                <h4>نتائج التحليل:</h4>
                <div id="analysisContent"></div>
            </div>
        </div>
    `;

    document.body.appendChild(analysisModal);

    // ربط الأحداث
    document.getElementById('cancelAnalysis').onclick = () => {
        analysisModal.remove();
    };

    document.getElementById('startAnalysis').onclick = () => {
        const fileInput = document.getElementById('videoAnalysisInput');
        const file = fileInput.files[0];

        if (file) {
            console.log('📊 بدء تحليل الفيديو:', file.name);

            // محاكاة تحليل الفيديو
            const resultDiv = document.getElementById('analysisResult');
            const contentDiv = document.getElementById('analysisContent');

            contentDiv.innerHTML = `
                <div style="text-align: left; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <p><strong>اسم الملف:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>النوع:</strong> ${file.type}</p>
                    <p><strong>التحليل:</strong> تم اكتشاف محتوى فيديو عالي الجودة</p>
                    <p><strong>التوصيات:</strong> يمكن تحسين الضغط لتقليل الحجم</p>
                </div>
            `;

            resultDiv.style.display = 'block';

            // إشعار صوتي
            if (typeof speakText === 'function') {
                speakText('تم تحليل الفيديو بنجاح');
            }
        } else {
            alert('يرجى اختيار ملف فيديو أولاً');
        }
    };
}

// دالة العرض ثلاثي الأبعاد
function handle3DDisplay() {
    console.log('🎲 بدء العرض ثلاثي الأبعاد...');

    // إنشاء مشهد ثلاثي الأبعاد بسيط
    const canvas3D = document.createElement('canvas');
    canvas3D.width = 800;
    canvas3D.height = 600;
    canvas3D.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border: 2px solid #3498db;
        border-radius: 10px;
        z-index: 10000;
        background: linear-gradient(45deg, #1e3c72, #2a5298);
    `;

    const ctx = canvas3D.getContext('2d');

    // رسم مكعب ثلاثي الأبعاد بسيط
    function draw3DCube() {
        ctx.clearRect(0, 0, canvas3D.width, canvas3D.height);

        // إعدادات المكعب
        const centerX = canvas3D.width / 2;
        const centerY = canvas3D.height / 2;
        const size = 100;
        const angle = Date.now() * 0.001;

        // رسم المكعب
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;

        // الوجه الأمامي (مع دوران بسيط)
        const rotatedSize = size + Math.sin(angle) * 10;
        ctx.strokeRect(centerX - rotatedSize, centerY - rotatedSize, rotatedSize * 2, rotatedSize * 2);

        // الوجه الخلفي (مع تأثير المنظور)
        const offset = 50 + Math.cos(angle) * 10;
        ctx.strokeRect(centerX - size + offset, centerY - size + offset, size * 2, size * 2);

        // الخطوط الجانبية
        ctx.beginPath();
        ctx.moveTo(centerX - size, centerY - size);
        ctx.lineTo(centerX - size + offset, centerY - size + offset);
        ctx.moveTo(centerX + size, centerY - size);
        ctx.lineTo(centerX + size + offset, centerY - size + offset);
        ctx.moveTo(centerX - size, centerY + size);
        ctx.lineTo(centerX - size + offset, centerY + size + offset);
        ctx.moveTo(centerX + size, centerY + size);
        ctx.lineTo(centerX + size + offset, centerY + size + offset);
        ctx.stroke();

        // إضافة نص
        ctx.fillStyle = '#ffffff';
        ctx.font = '24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('عرض ثلاثي الأبعاد تفاعلي', centerX, 50);
        ctx.fillText('🎲 مكعب دوار', centerX, canvas3D.height - 50);
    }

    // إنشاء خلفية
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
    `;

    // زر الإغلاق
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '✕ إغلاق العرض ثلاثي الأبعاد';
    closeBtn.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 10px 15px;
        cursor: pointer;
        z-index: 10001;
    `;

    closeBtn.onclick = () => {
        overlay.remove();
        canvas3D.remove();
        closeBtn.remove();
        console.log('✅ تم إغلاق العرض ثلاثي الأبعاد');
    };

    document.body.appendChild(overlay);
    document.body.appendChild(canvas3D);
    document.body.appendChild(closeBtn);

    // بدء الرسم
    draw3DCube();
    setInterval(draw3DCube, 50); // تحديث كل 50ms

    // إشعار صوتي
    if (typeof speakText === 'function') {
        speakText('تم بدء العرض ثلاثي الأبعاد التفاعلي');
    }
}

// دالة توليد الملخص
function generateSummary() {
    console.log('📄 توليد ملخص المحادثة...');

    if (window.conversationHistory && window.conversationHistory.length > 0) {
        // إنشاء ملخص من المحادثة
        let summary = '📋 **ملخص المحادثة:**\n\n';

        const messages = window.conversationHistory.slice(-10); // آخر 10 رسائل
        let userMessages = 0;
        let assistantMessages = 0;

        messages.forEach(msg => {
            if (msg.role === 'user') {
                userMessages++;
                summary += `👤 **المستخدم:** ${msg.content.substring(0, 100)}...\n\n`;
            } else if (msg.role === 'assistant') {
                assistantMessages++;
                summary += `🤖 **المساعد:** ${msg.content.substring(0, 100)}...\n\n`;
            }
        });

        summary += `📊 **إحصائيات:**\n`;
        summary += `• عدد رسائل المستخدم: ${userMessages}\n`;
        summary += `• عدد ردود المساعد: ${assistantMessages}\n`;
        summary += `• إجمالي الرسائل: ${messages.length}\n`;

        // عرض الملخص
        addMessage('assistant', summary);

        // إشعار صوتي
        if (typeof speakText === 'function') {
            speakText('تم توليد ملخص المحادثة بنجاح');
        }
    } else {
        const noHistoryMessage = '📄 لا توجد محادثة لتلخيصها. ابدأ محادثة أولاً!';
        addMessage('assistant', noHistoryMessage);

        if (typeof speakText === 'function') {
            speakText('لا توجد محادثة لتلخيصها');
        }
    }
}

// تصدير الوظائف للاستخدام العام
window.openAPIConfig = openAPIConfig;
window.openHFConfig = openHFConfig;
window.toggleAIImprove = toggleAIImprove;
window.handleScreenShare = handleScreenShare;
window.handleVideoUpload = handleVideoUpload;
window.handleVideoAnalyze = handleVideoAnalyze;
window.handle3DDisplay = handle3DDisplay;
window.generateSummary = generateSummary;

// النظام جاهز للاستخدام

// إضافة مستمع للأخطاء العامة
window.addEventListener('error', (event) => {
    console.error('❌ خطأ JavaScript:', event.error);
    console.error('📍 الملف:', event.filename);
    console.error('📍 السطر:', event.lineno);
});

// إضافة مستمع للأخطاء غير المعالجة
window.addEventListener('unhandledrejection', (event) => {
    console.error('❌ خطأ Promise غير معالج:', event.reason);
});

console.log('✅ تم تحميل جميع وظائف الأزرار بنجاح!');



