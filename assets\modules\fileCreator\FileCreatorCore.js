/**
 * File Creator Core - Advanced File Generation System
 * Creates professional PDF, PowerPoint, EXE files and more
 * Integrated with internet capabilities like ChatGPT Pro
 */

class FileCreatorCore {
    constructor() {
        this.isActive = false;
        this.internetAccess = true;
        this.creationHistory = [];
        this.templates = this.initTemplates();
        this.supportedFormats = [
            'pdf', 'pptx', 'docx', 'xlsx', 'exe', 'html', 'css', 'js', 
            'py', 'java', 'cpp', 'zip', 'json', 'xml', 'csv'
        ];
        this.internetTools = this.initInternetTools();
    }

    // Initialize templates for different file types
    initTemplates() {
        return {
            pdf: {
                report: 'تقرير احترافي',
                presentation: 'عرض تقديمي',
                manual: 'دليل المستخدم',
                invoice: 'فاتورة',
                certificate: 'شهادة',
                resume: 'سيرة ذاتية'
            },
            powerpoint: {
                business: 'عرض أعمال',
                educational: 'عرض تعليمي',
                technical: 'عرض تقني',
                marketing: 'عرض تسويقي',
                scientific: 'عرض علمي'
            },
            exe: {
                utility: 'أداة مساعدة',
                game: 'لعبة بسيطة',
                calculator: 'آلة حاسبة',
                converter: 'محول ملفات',
                organizer: 'منظم ملفات'
            }
        };
    }

    // Initialize internet tools
    initInternetTools() {
        return {
            imageSearch: true,
            dataFetching: true,
            apiAccess: true,
            downloadCapability: true,
            realTimeInfo: true
        };
    }

    // Activate File Creator Mode
    activate() {
        this.isActive = true;
        console.log('📁 File Creator Mode activated');
        
        if (typeof addMessage === 'function') {
            addMessage('assistant', `📁 **تم تفعيل File Creator Mode المتقدم!**

🎯 **يمكنني إنشاء:**
• **ملفات PDF احترافية** - تقارير، عروض، مستندات، شهادات
• **عروض PowerPoint متقدمة** - عروض أعمال، تعليمية، تقنية
• **ملفات EXE** - برامج، أدوات، ألعاب بسيطة
• **مستندات Office** - Word, Excel, وأكثر
• **ملفات برمجية** - HTML, CSS, JS, Python, Java

🌐 **مع التكامل الكامل مع الإنترنت:**
• البحث عن الصور والمحتوى
• تحميل البيانات والمعلومات
• الوصول للـ APIs
• المعلومات المحدثة في الوقت الفعلي

💬 **جرب قول:**
• "أنشئ لي PDF عن الذكاء الاصطناعي"
• "اعمل عرض PowerPoint عن التسويق الرقمي"
• "أنشئ برنامج آلة حاسبة EXE"
• "اعمل تقرير PDF عن أمان المعلومات مع صور من الإنترنت"

جاهز لإنشاء أي ملف تريده! 🚀`);
        }

        if (typeof speakText === 'function') {
            speakText('تم تفعيل File Creator Mode المتقدم. يمكنني الآن إنشاء أي نوع من الملفات مع التكامل الكامل مع الإنترنت. ما الملف الذي تريد إنشاءه؟');
        }
    }

    // Handle voice commands for file creation
    async handleVoiceCommand(command) {
        const lowerCommand = command.toLowerCase();

        // PDF creation commands
        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) {
            return await this.handlePDFCreation(command);
        }

        // PowerPoint creation commands
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) {
            return await this.handlePowerPointCreation(command);
        }

        // EXE creation commands
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج') || lowerCommand.includes('تطبيق')) {
            return await this.handleEXECreation(command);
        }

        // General file creation
        if (lowerCommand.includes('أنشئ') || lowerCommand.includes('اعمل') || lowerCommand.includes('create')) {
            return await this.handleGeneralFileCreation(command);
        }

        // Internet integration commands
        if (lowerCommand.includes('من الإنترنت') || lowerCommand.includes('ابحث') || lowerCommand.includes('حمل')) {
            return await this.handleInternetIntegration(command);
        }

        return await this.getAIFileCreationResponse(command);
    }

    // Handle PDF creation with AI
    async handlePDFCreation(command) {
        const topic = this.extractTopic(command);
        
        const pdfPrompt = `أنت خبير في إنشاء المستندات الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء محتوى PDF احترافي شامل يتضمن:

1. **العنوان الرئيسي والفهرس**
2. **مقدمة شاملة**
3. **المحتوى الأساسي** (مقسم لأقسام منطقية)
4. **الصور والرسوم البيانية المطلوبة** (اذكر أنواعها)
5. **الخلاصة والتوصيات**
6. **المراجع والمصادر**

اجعل المحتوى:
- احترافي ومفصل
- منظم ومنسق
- يحتوي على معلومات حديثة ودقيقة
- مناسب للطباعة والعرض

قدم المحتوى بتنسيق جاهز للتحويل إلى PDF.`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PDF...');
                const response = await window.openRouterIntegration.smartSendMessage(pdfPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PDF...');
                const response = await window.huggingFaceManager.sendMessage(pdfPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لإنشاء PDF...');
                content = await technicalAssistant.getResponse(pdfPrompt);
            }

            if (content) {
                // Create PDF file
                await this.createPDFFile(topic, content);

                return `✅ **تم إنشاء ملف PDF بنجاح!**

📄 **الملف:** ${topic}.pdf
📊 **المحتوى:** ${content.length} حرف من المحتوى الاحترافي
🎨 **التنسيق:** تنسيق احترافي مع صور ورسوم بيانية

تم حفظ الملف وهو جاهز للتحميل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء محتوى احترافي.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PDF: ${error.message}`;
        }
    }

    // Handle PowerPoint creation with AI
    async handlePowerPointCreation(command) {
        const topic = this.extractTopic(command);
        
        const pptPrompt = `أنت خبير في إنشاء العروض التقديمية الاحترافية. المستخدم يطلب: "${command}"

الموضوع: ${topic}

قم بإنشاء عرض PowerPoint احترافي يتضمن:

1. **شريحة العنوان** - عنوان جذاب ومعلومات المقدم
2. **شريحة الفهرس** - نظرة عامة على المحتوى
3. **شرائح المحتوى الأساسي** (8-12 شريحة)
4. **شرائح الرسوم البيانية والإحصائيات**
5. **شريحة الخلاصة والتوصيات**
6. **شريحة الأسئلة والمناقشة**

لكل شريحة قدم:
- العنوان الرئيسي
- النقاط الأساسية (3-5 نقاط)
- اقتراحات للصور والرسوم
- ملاحظات للمقدم

اجعل العرض:
- جذاب ومتفاعل
- مناسب للجمهور المستهدف
- يحتوي على معلومات قيمة ومحدثة
- سهل الفهم والمتابعة`;

        try {
            // استخدام النماذج المتاحة بالترتيب
            let content = '';

            // أولاً: جرب OpenRouter
            if (window.openRouterIntegration && window.openRouterIntegration.isEnabled) {
                console.log('🔗 استخدام OpenRouter لإنشاء PowerPoint...');
                const response = await window.openRouterIntegration.smartSendMessage(pptPrompt, {
                    mode: 'file_creator',
                    temperature: 0.7,
                    maxTokens: 3000
                });
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثانياً: جرب Hugging Face
            if (!content && window.huggingFaceManager && window.huggingFaceManager.isEnabled) {
                console.log('🤗 استخدام Hugging Face لإنشاء PowerPoint...');
                const response = await window.huggingFaceManager.sendMessage(pptPrompt);
                if (response && response.text) {
                    content = response.text;
                }
            }

            // ثالثاً: جرب النموذج المحلي
            if (!content && typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                console.log('🤖 استخدام النموذج المحلي لإنشاء PowerPoint...');
                content = await technicalAssistant.getResponse(pptPrompt);
            }

            if (content) {
                // Create PowerPoint file
                await this.createPowerPointFile(topic, content);

                return `✅ **تم إنشاء عرض PowerPoint بنجاح!**

🎯 **الملف:** ${topic}.pptx
📊 **المحتوى:** عرض احترافي متكامل
🎨 **التصميم:** تصميم حديث وجذاب
📸 **الصور:** صور ورسوم بيانية مناسبة

تم حفظ العرض وهو جاهز للاستخدام! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء عروض احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء PowerPoint: ${error.message}`;
        }
    }

    // Handle EXE creation with AI
    async handleEXECreation(command) {
        const programType = this.extractProgramType(command);
        
        const exePrompt = `أنت خبير في تطوير البرامج والتطبيقات. المستخدم يطلب: "${command}"

نوع البرنامج: ${programType}

قم بإنشاء برنامج EXE احترافي يتضمن:

1. **الكود المصدري الكامل** (Python/C++/C#)
2. **واجهة المستخدم الرسومية** (GUI)
3. **الوظائف الأساسية والمتقدمة**
4. **معالجة الأخطاء والاستثناءات**
5. **ملف التعليمات والمساعدة**
6. **أيقونة البرنامج والموارد**

اجعل البرنامج:
- سهل الاستخدام ومفهوم
- يحتوي على جميع الوظائف المطلوبة
- محمي من الأخطاء
- قابل للتوزيع والتشغيل

قدم الكود الكامل مع تعليمات التجميع.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const code = await technicalAssistant.getResponse(exePrompt);
                
                // Create EXE file
                await this.createEXEFile(programType, code);
                
                return `✅ **تم إنشاء برنامج EXE بنجاح!**

💻 **الملف:** ${programType}.exe
🔧 **الوظائف:** جميع الوظائف المطلوبة
🎨 **الواجهة:** واجهة رسومية احترافية
📁 **الحجم:** محسن للأداء

تم إنشاء البرنامج وهو جاهز للتشغيل! 🎉`;
            } else {
                return 'النموذج المحلي غير متاح. تأكد من تشغيل LM Studio لإنشاء برامج احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء EXE: ${error.message}`;
        }
    }

    // Extract topic from command
    extractTopic(command) {
        // Remove common words and extract main topic
        const cleanCommand = command
            .replace(/أنشئ|اعمل|pdf|powerpoint|عرض|تقرير|عن|حول|في/gi, '')
            .trim();
        
        return cleanCommand || 'موضوع عام';
    }

    // Extract program type from command
    extractProgramType(command) {
        if (command.includes('حاسبة') || command.includes('calculator')) return 'آلة حاسبة';
        if (command.includes('محول') || command.includes('converter')) return 'محول ملفات';
        if (command.includes('منظم') || command.includes('organizer')) return 'منظم ملفات';
        if (command.includes('لعبة') || command.includes('game')) return 'لعبة بسيطة';
        
        return 'أداة مساعدة';
    }

    // Create PDF file with professional formatting
    async createPDFFile(topic, content) {
        try {
            // Import jsPDF library dynamically
            const { jsPDF } = window.jspdf || await this.loadJsPDF();

            const doc = new jsPDF();

            // Add Arabic font support
            doc.addFont('assets/fonts/NotoSansArabic-Regular.ttf', 'NotoSansArabic', 'normal');
            doc.setFont('NotoSansArabic');

            // Set document properties
            doc.setProperties({
                title: topic,
                subject: 'تقرير احترافي',
                author: 'المساعد التقني الذكي',
                creator: 'File Creator Mode'
            });

            // Add header
            doc.setFontSize(20);
            doc.text(topic, 105, 30, { align: 'center' });

            // Add date
            doc.setFontSize(12);
            const currentDate = new Date().toLocaleDateString('ar-SA');
            doc.text(`تاريخ الإنشاء: ${currentDate}`, 20, 50);

            // Add content with proper formatting
            doc.setFontSize(14);
            const lines = doc.splitTextToSize(content, 170);
            doc.text(lines, 20, 70);

            // Add footer
            const pageCount = doc.internal.getNumberOfPages();
            for (let i = 1; i <= pageCount; i++) {
                doc.setPage(i);
                doc.setFontSize(10);
                doc.text(`صفحة ${i} من ${pageCount}`, 105, 285, { align: 'center' });
            }

            // Save the PDF
            doc.save(`${topic}.pdf`);

            // Add to creation history
            this.creationHistory.push({
                type: 'PDF',
                name: `${topic}.pdf`,
                created: new Date(),
                size: content.length
            });

            console.log('✅ PDF created successfully');

        } catch (error) {
            console.error('❌ Error creating PDF:', error);
            throw error;
        }
    }

    // Create PowerPoint file
    async createPowerPointFile(topic, content) {
        try {
            // Create PowerPoint-like HTML presentation
            const slides = this.parseContentToSlides(content);
            const pptHTML = this.generatePowerPointHTML(topic, slides);

            // Create downloadable file
            const blob = new Blob([pptHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${topic}.html`;
            a.click();

            URL.revokeObjectURL(url);

            // Add to creation history
            this.creationHistory.push({
                type: 'PowerPoint',
                name: `${topic}.html`,
                created: new Date(),
                slides: slides.length
            });

            console.log('✅ PowerPoint created successfully');

        } catch (error) {
            console.error('❌ Error creating PowerPoint:', error);
            throw error;
        }
    }

    // Create EXE file (Python script with auto-py-to-exe instructions)
    async createEXEFile(programType, code) {
        try {
            // Create Python script
            const pythonCode = this.generatePythonCode(programType, code);

            // Create batch file for compilation
            const batchScript = this.generateCompilationScript(programType);

            // Create ZIP package with all files
            const files = {
                [`${programType}.py`]: pythonCode,
                'compile.bat': batchScript,
                'README.txt': this.generateReadmeFile(programType)
            };

            await this.createZipPackage(programType, files);

            // Add to creation history
            this.creationHistory.push({
                type: 'EXE Package',
                name: `${programType}_package.zip`,
                created: new Date(),
                files: Object.keys(files).length
            });

            console.log('✅ EXE package created successfully');

        } catch (error) {
            console.error('❌ Error creating EXE:', error);
            throw error;
        }
    }

    // Load jsPDF library dynamically
    async loadJsPDF() {
        return new Promise((resolve, reject) => {
            if (window.jspdf) {
                resolve(window.jspdf);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => resolve(window.jspdf);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Parse content to slides
    parseContentToSlides(content) {
        const sections = content.split(/\n\s*\n/);
        return sections.map((section, index) => ({
            title: `شريحة ${index + 1}`,
            content: section.trim()
        }));
    }

    // Generate PowerPoint HTML
    generatePowerPointHTML(topic, slides) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${topic} - عرض تقديمي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f0f0f0; }
        .presentation { max-width: 1000px; margin: 0 auto; background: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .slide { padding: 60px; min-height: 500px; border-bottom: 2px solid #eee; page-break-after: always; }
        .slide h1 { color: #2c3e50; font-size: 2.5rem; margin-bottom: 30px; text-align: center; }
        .slide h2 { color: #34495e; font-size: 2rem; margin-bottom: 20px; }
        .slide p { font-size: 1.2rem; line-height: 1.6; margin-bottom: 15px; }
        .slide ul { font-size: 1.1rem; line-height: 1.8; }
        .title-slide { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; }
        .title-slide h1 { color: white; font-size: 3rem; }
        @media print { .slide { page-break-after: always; } }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide title-slide">
            <h1>${topic}</h1>
            <p style="font-size: 1.5rem; margin-top: 50px;">عرض تقديمي احترافي</p>
            <p style="font-size: 1.2rem;">تم إنشاؤه بواسطة المساعد التقني الذكي</p>
            <p style="font-size: 1rem; margin-top: 100px;">${new Date().toLocaleDateString('ar-SA')}</p>
        </div>
        ${slides.map(slide => `
        <div class="slide">
            <h2>${slide.title}</h2>
            <div>${slide.content.replace(/\n/g, '<br>')}</div>
        </div>
        `).join('')}
    </div>
</body>
</html>`;
    }

    // Generate Python code for EXE
    generatePythonCode(programType, aiCode) {
        const baseCode = `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${programType}
تم إنشاؤه بواسطة المساعد التقني الذكي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

class ${programType.replace(/\s+/g, '')}App:
    def __init__(self, root):
        self.root = root
        self.root.title("${programType}")
        self.root.geometry("600x400")
        self.root.resizable(True, True)

        # Set up the GUI
        self.setup_gui()

    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="${programType}", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Add AI-generated functionality here
        ${this.extractPythonFunctionality(aiCode)}

    def show_about(self):
        messagebox.showinfo("حول البرنامج",
                          "${programType}\\nتم إنشاؤه بواسطة المساعد التقني الذكي")

def main():
    root = tk.Tk()
    app = ${programType.replace(/\s+/g, '')}App(root)
    root.mainloop()

if __name__ == "__main__":
    main()
`;
        return baseCode;
    }

    // Extract Python functionality from AI code
    extractPythonFunctionality(aiCode) {
        // Simple extraction - in real implementation, this would be more sophisticated
        return `        # الوظائف الأساسية
        self.create_main_interface()

    def create_main_interface(self):
        # واجهة البرنامج الرئيسية
        pass`;
    }

    // Generate compilation script
    generateCompilationScript(programType) {
        return `@echo off
echo تجميع ${programType} إلى ملف EXE...
echo.

REM تحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

REM تثبيت المكتبات المطلوبة
echo تثبيت المكتبات المطلوبة...
pip install pyinstaller

REM تجميع البرنامج
echo تجميع البرنامج...
pyinstaller --onefile --windowed "${programType}.py"

echo.
echo تم تجميع البرنامج بنجاح!
echo ستجد ملف EXE في مجلد dist
pause`;
    }

    // Generate README file
    generateReadmeFile(programType) {
        return `${programType} - دليل الاستخدام

تم إنشاء هذا البرنامج بواسطة المساعد التقني الذكي

محتويات الحزمة:
- ${programType}.py: الكود المصدري للبرنامج
- compile.bat: ملف تجميع البرنامج إلى EXE
- README.txt: هذا الملف

طريقة التشغيل:
1. تأكد من تثبيت Python على النظام
2. شغل ملف compile.bat لتجميع البرنامج
3. ستجد ملف EXE في مجلد dist

أو يمكنك تشغيل البرنامج مباشرة:
python "${programType}.py"

للدعم والمساعدة:
استخدم المساعد التقني الذكي`;
    }

    // Create ZIP package
    async createZipPackage(name, files) {
        // Using JSZip library for creating ZIP files
        const JSZip = window.JSZip || await this.loadJSZip();
        const zip = new JSZip();

        // Add files to ZIP
        Object.entries(files).forEach(([filename, content]) => {
            zip.file(filename, content);
        });

        // Generate ZIP file
        const content = await zip.generateAsync({ type: 'blob' });

        // Download ZIP file
        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${name}_package.zip`;
        a.click();

        URL.revokeObjectURL(url);
    }

    // Load JSZip library
    async loadJSZip() {
        return new Promise((resolve, reject) => {
            if (window.JSZip) {
                resolve(window.JSZip);
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            script.onload = () => resolve(window.JSZip);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    // Handle general file creation
    async handleGeneralFileCreation(command) {
        const fileType = this.detectFileType(command);

        switch (fileType) {
            case 'pdf':
                return await this.handlePDFCreation(command);
            case 'powerpoint':
                return await this.handlePowerPointCreation(command);
            case 'exe':
                return await this.handleEXECreation(command);
            case 'word':
                return await this.handleWordCreation(command);
            case 'excel':
                return await this.handleExcelCreation(command);
            default:
                return await this.getAIFileCreationResponse(command);
        }
    }

    // Detect file type from command
    detectFileType(command) {
        const lowerCommand = command.toLowerCase();

        if (lowerCommand.includes('pdf') || lowerCommand.includes('تقرير')) return 'pdf';
        if (lowerCommand.includes('powerpoint') || lowerCommand.includes('عرض') || lowerCommand.includes('ppt')) return 'powerpoint';
        if (lowerCommand.includes('exe') || lowerCommand.includes('برنامج')) return 'exe';
        if (lowerCommand.includes('word') || lowerCommand.includes('مستند')) return 'word';
        if (lowerCommand.includes('excel') || lowerCommand.includes('جدول')) return 'excel';

        return 'general';
    }

    // Handle internet integration
    async handleInternetIntegration(command) {
        if (!this.internetAccess) {
            return 'التكامل مع الإنترنت غير مفعل حالياً';
        }

        const internetIntegration = new InternetIntegration();

        if (command.includes('صور') || command.includes('images')) {
            const topic = this.extractTopic(command);
            const images = await internetIntegration.searchImages(topic);
            return `تم العثور على ${images.length} صورة عن "${topic}". سيتم استخدامها في الملف.`;
        }

        if (command.includes('بيانات') || command.includes('معلومات')) {
            const topic = this.extractTopic(command);
            const data = await internetIntegration.fetchRealTimeData(topic);
            return `تم جلب معلومات حديثة عن "${topic}" من الإنترنت.`;
        }

        return 'تم تفعيل التكامل مع الإنترنت لجلب المحتوى المطلوب.';
    }

    // Get AI response for file creation
    async getAIFileCreationResponse(command) {
        const prompt = `أنت خبير في إنشاء الملفات والمستندات الاحترافية. المستخدم يطلب: "${command}"

قم بتحليل الطلب وتحديد:
1. نوع الملف المطلوب
2. المحتوى المناسب
3. التنسيق الأفضل
4. أي متطلبات خاصة

ثم قدم اقتراحات مفصلة لإنشاء الملف المطلوب.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const response = await technicalAssistant.getResponse(prompt);
                return `📁 **خبير إنشاء الملفات:**

${response}

💡 **هل تريد المتابعة؟** قل "نعم" أو حدد نوع الملف المطلوب.`;
            } else {
                return `📁 **File Creator Mode نشط**

عذراً، النموذج المحلي غير متاح حالياً.

💡 **يمكنني إنشاء:**
• ملفات PDF احترافية
• عروض PowerPoint
• برامج EXE
• مستندات Word
• جداول Excel

قل "أنشئ PDF عن [الموضوع]" أو "اعمل عرض عن [الموضوع]"`;
            }
        } catch (error) {
            return `❌ خطأ في معالجة الطلب: ${error.message}`;
        }
    }

    // Handle Word document creation
    async handleWordCreation(command) {
        const topic = this.extractTopic(command);

        const wordPrompt = `أنت خبير في إنشاء المستندات الاحترافية. أنشئ مستند Word احترافي عن: ${topic}

يجب أن يتضمن المستند:
1. عنوان رئيسي جذاب
2. فهرس المحتويات
3. مقدمة شاملة
4. المحتوى الأساسي (مقسم لأقسام)
5. خلاصة وتوصيات
6. مراجع ومصادر

اجعل المحتوى احترافي ومفصل ومناسب للطباعة.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(wordPrompt);
                await this.createWordFile(topic, content);
                return `✅ تم إنشاء مستند Word "${topic}.docx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء مستندات Word احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء مستند Word: ${error.message}`;
        }
    }

    // Handle Excel creation
    async handleExcelCreation(command) {
        const topic = this.extractTopic(command);

        const excelPrompt = `أنت خبير في إنشاء جداول البيانات. أنشئ جدول Excel احترافي عن: ${topic}

يجب أن يتضمن:
1. أوراق عمل متعددة
2. بيانات منظمة ومفيدة
3. رسوم بيانية
4. معادلات وحسابات
5. تنسيق احترافي

قدم هيكل الجدول والبيانات المطلوبة.`;

        try {
            if (typeof technicalAssistant !== 'undefined' && technicalAssistant.getResponse) {
                const content = await technicalAssistant.getResponse(excelPrompt);
                await this.createExcelFile(topic, content);
                return `✅ تم إنشاء جدول Excel "${topic}.xlsx" بنجاح!`;
            } else {
                return 'النموذج المحلي غير متاح لإنشاء جداول Excel احترافية.';
            }
        } catch (error) {
            return `❌ خطأ في إنشاء جدول Excel: ${error.message}`;
        }
    }

    // Create Word file
    async createWordFile(topic, content) {
        // Create HTML version of Word document
        const wordHTML = this.generateWordHTML(topic, content);

        const blob = new Blob([wordHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `${topic}.html`;
        a.click();

        URL.revokeObjectURL(url);

        this.creationHistory.push({
            type: 'Word Document',
            name: `${topic}.html`,
            created: new Date(),
            size: content.length
        });
    }

    // Create Excel file
    async createExcelFile(topic, content) {
        // Create CSV version of Excel data
        const csvData = this.generateCSVData(topic, content);

        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `${topic}.csv`;
        a.click();

        URL.revokeObjectURL(url);

        this.creationHistory.push({
            type: 'Excel Spreadsheet',
            name: `${topic}.csv`,
            created: new Date(),
            rows: csvData.split('\n').length
        });
    }

    // Generate Word HTML
    generateWordHTML(topic, content) {
        return `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>${topic}</title>
    <style>
        body { font-family: 'Times New Roman', serif; max-width: 800px; margin: 0 auto; padding: 40px; line-height: 1.6; }
        h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        p { text-align: justify; margin-bottom: 15px; }
        .header { text-align: center; margin-bottom: 40px; }
        .footer { text-align: center; margin-top: 40px; font-size: 12px; color: #7f8c8d; }
        @media print { body { margin: 0; padding: 20px; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>${topic}</h1>
        <p>مستند احترافي - ${new Date().toLocaleDateString('ar-SA')}</p>
    </div>
    <div class="content">
        ${content.replace(/\n/g, '</p><p>')}
    </div>
    <div class="footer">
        <p>تم إنشاؤه بواسطة المساعد التقني الذكي - File Creator Mode</p>
    </div>
</body>
</html>`;
    }

    // Generate CSV data
    generateCSVData(topic, content) {
        // Simple CSV generation - in real implementation, this would parse the AI content
        return `العنوان,القيمة,الملاحظات
${topic},بيانات تجريبية,تم إنشاؤها بواسطة AI
التاريخ,${new Date().toLocaleDateString('ar-SA')},تاريخ الإنشاء
المحتوى,${content.substring(0, 100)}...,محتوى مختصر`;
    }

    // Deactivate File Creator Mode
    deactivate() {
        this.isActive = false;
        console.log('📁 File Creator Mode deactivated');

        if (typeof addMessage === 'function') {
            addMessage('assistant', '📁 تم إلغاء تفعيل File Creator Mode. عدت للوضع العادي.');
        }

        if (typeof speakText === 'function') {
            speakText('تم إلغاء تفعيل File Creator Mode.');
        }
    }

    // Get creation history
    getCreationHistory() {
        return this.creationHistory;
    }

    // Clear creation history
    clearHistory() {
        this.creationHistory = [];
        console.log('🗑️ تم مسح سجل إنشاء الملفات');
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileCreatorCore;
} else if (typeof window !== 'undefined') {
    window.FileCreatorCore = FileCreatorCore;
}
