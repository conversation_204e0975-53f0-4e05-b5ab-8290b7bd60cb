/**
 * ملف إدارة أحداث الأزرار - المساعد التقني الذكي
 * مخصص فقط لربط الأزرار بوظائفها في window
 */

console.log('🔗 تحميل ملف إدارة أحداث الأزرار...');

// انتظار تحميل assistant-core.js أولاً
function waitForCoreLoaded() {
    return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 100; // 10 ثواني

        const checkInterval = setInterval(() => {
            attempts++;

            // فحص الوظائف في عدة أماكن
            const foundFunctions = {
                sendMessage: (typeof sendMessage !== 'undefined') || (typeof window.sendMessage === 'function'),
                togglePureVoiceMode: (typeof togglePureVoiceMode !== 'undefined') || (typeof window.togglePureVoiceMode === 'function'),
                handleScreenShare: (typeof handleScreenShare !== 'undefined') || (typeof window.handleScreenShare === 'function'),
                startVoiceRecording: (typeof startVoiceRecording !== 'undefined') || (typeof window.startVoiceRecording === 'function'),
                toggleVoiceConversation: (typeof toggleVoiceConversation !== 'undefined') || (typeof window.toggleVoiceConversation === 'function')
            };

            const foundCount = Object.values(foundFunctions).filter(Boolean).length;

            if (foundCount > 0 || attempts >= maxAttempts) {
                clearInterval(checkInterval);

                if (foundCount > 0) {
                    console.log('✅ تم العثور على وظائف assistant-core.js');
                    console.log('🔍 الوظائف المتاحة:', foundFunctions);
                } else {
                    console.warn('⚠️ لم يتم العثور على وظائف assistant-core.js');
                    console.log('🔍 سيتم استخدام الوظائف البديلة');
                }

                resolve();
            }
        }, 100);
    });
}

// التأكد من تحميل الصفحة قبل ربط الأحداث
window.addEventListener('DOMContentLoaded', async function() {
    console.log('📋 بدء ربط أحداث الأزرار...');
    await waitForCoreLoaded();
    initializeButtonEvents();
});

// إذا كانت الصفحة محملة بالفعل
if (document.readyState === 'loading') {
    // انتظار تحميل DOM
    console.log('⏳ انتظار تحميل DOM...');
} else {
    // الصفحة محملة، ابدأ فوراً
    console.log('✅ DOM محمل، بدء ربط الأحداث فوراً...');
    setTimeout(async () => {
        await waitForCoreLoaded();
        initializeButtonEvents();
    }, 100);
}

/**
 * دالة تهيئة جميع أحداث الأزرار
 */
function initializeButtonEvents() {
    console.log('🎯 بدء تهيئة أحداث الأزرار...');
    
    // فحص وجود الأزرار أولاً
    checkButtonsExistence();
    
    // ربط أزرار الواجهة الرئيسية
    bindMainInterfaceButtons();
    
    // ربط أزرار الأدوات
    bindToolButtons();
    
    // ربط أزرار الأوضاع المتقدمة
    bindAdvancedModeButtons();
    
    // ربط أزرار الإعدادات
    bindSettingsButtons();
    
    // ربط حقل الإدخال
    bindInputField();
    
    console.log('🎉 تم الانتهاء من ربط جميع أحداث الأزرار!');
}

/**
 * فحص وجود الأزرار في DOM
 */
function checkButtonsExistence() {
    console.log('🔍 فحص وجود الأزرار في DOM...');
    
    const buttonIds = [
        'sendBtn', 'voiceBtn', 'pureVoiceBtn', 'voiceRecordBtn',
        'screenShareBtn', 'videoUploadBtn', 'videoAnalyzeBtn', 'ar3dBtn',
        'summaryBtn', 'bugBountyBtn', 'fileCreatorBtn', 'apiConfigBtn',
        'hfConfigBtn', 'aiImproveBtn', 'voiceSettingsBtn'
    ];
    
    let foundButtons = 0;
    buttonIds.forEach(id => {
        const button = document.getElementById(id);
        if (button) {
            foundButtons++;
            console.log(`✅ ${id}: موجود`);
        } else {
            console.warn(`⚠️ ${id}: غير موجود`);
        }
    });
    
    console.log(`📊 إجمالي الأزرار الموجودة: ${foundButtons}/${buttonIds.length}`);
}

/**
 * ربط أزرار الواجهة الرئيسية
 */
function bindMainInterfaceButtons() {
    console.log('🔗 ربط أزرار الواجهة الرئيسية...');
    
    // زر الإرسال
    bindButton('sendBtn', 'sendMessage', 'إرسال الرسالة');
    
    // زر الصوت العادي
    bindButton('voiceBtn', 'toggleVoiceConversation', 'تبديل المحادثة الصوتية');
    
    // زر المحادثة الصوتية الخالصة
    bindButton('pureVoiceBtn', 'togglePureVoiceMode', 'المحادثة الصوتية الخالصة');
    
    // زر التسجيل الصوتي
    bindButton('voiceRecordBtn', 'startVoiceRecording', 'بدء التسجيل الصوتي');
}

/**
 * ربط أزرار الأدوات
 */
function bindToolButtons() {
    console.log('🛠️ ربط أزرار الأدوات...');
    
    // زر مشاركة الشاشة
    bindButton('screenShareBtn', 'handleScreenShare', 'مشاركة الشاشة');
    
    // زر تحميل الفيديو
    bindButton('videoUploadBtn', 'handleVideoUpload', 'تحميل الفيديو');
    
    // زر تحليل الفيديو
    bindButton('videoAnalyzeBtn', 'handleVideoAnalyze', 'تحليل الفيديو');
    
    // زر العرض ثلاثي الأبعاد
    bindButton('ar3dBtn', 'handle3DDisplay', 'العرض ثلاثي الأبعاد');
    
    // زر توليد الملخص
    bindButton('summaryBtn', 'generateSummary', 'توليد الملخص');
}

/**
 * ربط أزرار الأوضاع المتقدمة
 */
function bindAdvancedModeButtons() {
    console.log('⚡ ربط أزرار الأوضاع المتقدمة...');
    
    // زر Bug Bounty Mode
    bindButton('bugBountyBtn', 'toggleBugBountyMode', 'وضع Bug Bounty');
    
    // زر File Creator Mode
    bindButton('fileCreatorBtn', 'toggleFileCreatorMode', 'وضع إنشاء الملفات');
    
    // زر التحسين الذاتي
    bindButton('aiImproveBtn', 'toggleAIImprove', 'التحسين الذاتي للذكاء الاصطناعي');
}

/**
 * ربط أزرار الإعدادات
 */
function bindSettingsButtons() {
    console.log('⚙️ ربط أزرار الإعدادات...');
    
    // زر تكوين API
    bindButton('apiConfigBtn', 'openAPIConfig', 'تكوين API');
    
    // زر Hugging Face
    bindButton('hfConfigBtn', 'openHFConfig', 'إعدادات Hugging Face');
    
    // زر إعدادات الصوت
    bindButton('voiceSettingsBtn', 'openVoiceSettings', 'إعدادات الصوت');
}

/**
 * ربط حقل الإدخال بمفتاح Enter
 */
function bindInputField() {
    console.log('⌨️ ربط حقل الإدخال...');
    
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                console.log('⌨️ تم الضغط على Enter في حقل الإدخال');
                executeFunction('sendMessage', 'إرسال الرسالة بـ Enter');
            }
        });
        console.log('✅ تم ربط حقل الإدخال بمفتاح Enter');
    } else {
        console.warn('⚠️ حقل الإدخال (messageInput) غير موجود');
    }
}

/**
 * دالة عامة لربط زر بوظيفة
 * @param {string} buttonId - معرف الزر
 * @param {string} functionName - اسم الوظيفة في window
 * @param {string} description - وصف الوظيفة للتشخيص
 */
function bindButton(buttonId, functionName, description) {
    const button = document.getElementById(buttonId);
    
    if (!button) {
        console.warn(`⚠️ الزر ${buttonId} غير موجود في DOM`);
        return;
    }
    
    // إزالة أي event listeners سابقة
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    // إضافة event listener جديد
    newButton.addEventListener('click', function() {
        console.log(`🔥 تم النقر على زر: ${description} (${buttonId})`);
        executeFunction(functionName, description);
    });
    
    console.log(`✅ تم ربط ${buttonId} بوظيفة ${functionName}`);
}

/**
 * تنفيذ وظيفة مع معالجة الأخطاء
 * @param {string} functionName - اسم الوظيفة
 * @param {string} description - وصف الوظيفة
 */
// تعريف جميع الوظائف مباشرة في هذا الملف
function sendMessage() {
    console.log('📤 إرسال رسالة...');
    const messageInput = document.getElementById('messageInput');
    if (messageInput && messageInput.value.trim()) {
        const message = messageInput.value.trim();

        // إضافة الرسالة للدردشة
        const chatContainer = document.getElementById('chatContainer');
        if (chatContainer) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = `<div class="message-content"><strong>أنت:</strong> ${message}</div>`;
            chatContainer.appendChild(messageDiv);

            // رد تلقائي من المساعد
            setTimeout(() => {
                const replyDiv = document.createElement('div');
                replyDiv.className = 'message assistant-message';
                replyDiv.innerHTML = `<div class="message-content"><strong>المساعد:</strong> تم استلام رسالتك: "${message}". كيف يمكنني مساعدتك؟</div>`;
                chatContainer.appendChild(replyDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }, 1000);

            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        messageInput.value = '';
        console.log('✅ تم إرسال الرسالة بنجاح');
    } else {
        alert('يرجى كتابة رسالة أولاً');
    }
}

function togglePureVoiceMode() {
    console.log('🎤 تبديل وضع المحادثة الصوتية الخالصة...');
    const chatContainer = document.getElementById('chatContainer');
    const pureVoiceBtn = document.getElementById('pureVoiceBtn');

    if (chatContainer && chatContainer.style.display !== 'none') {
        // إخفاء الدردشة وتفعيل الوضع الصوتي
        chatContainer.style.display = 'none';
        if (pureVoiceBtn) pureVoiceBtn.textContent = '🔙 العودة للدردشة';

        // إضافة مؤشر صوتي
        let voiceIndicator = document.getElementById('voiceIndicator');
        if (!voiceIndicator) {
            voiceIndicator = document.createElement('div');
            voiceIndicator.id = 'voiceIndicator';
            voiceIndicator.innerHTML = `
                <div style="text-align: center; padding: 50px; font-size: 24px; color: white;">
                    🎤 وضع المحادثة الصوتية الخالصة
                    <br><br>
                    <div style="font-size: 16px; color: #ccc;">
                        تحدث الآن... المساعد يستمع إليك
                    </div>
                    <br>
                    <div style="font-size: 14px; color: #999;">
                        اضغط "العودة للدردشة" للخروج من هذا الوضع
                    </div>
                </div>
            `;
            voiceIndicator.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex; align-items: center; justify-content: center;
                z-index: 1000;
            `;
            document.body.appendChild(voiceIndicator);
        }

        console.log('✅ تم تفعيل وضع المحادثة الصوتية الخالصة');
    } else {
        // إظهار الدردشة وإيقاف الوضع الصوتي
        if (chatContainer) chatContainer.style.display = 'block';
        if (pureVoiceBtn) pureVoiceBtn.textContent = '🎤 محادثة صوتية خالصة';

        // إزالة المؤشر الصوتي
        const voiceIndicator = document.getElementById('voiceIndicator');
        if (voiceIndicator) voiceIndicator.remove();

        console.log('✅ تم العودة لوضع الدردشة العادي');
    }
}

function toggleVoiceConversation() {
    console.log('🎤 تبديل المحادثة الصوتية المتقدمة...');

    // استدعاء وحدة Advanced Voice Engine الحقيقية أولاً
    if (typeof window.AdvancedVoiceEngine !== 'undefined') {
        try {
            window.AdvancedVoiceEngine.toggleConversation();
            return;
        } catch (error) {
            console.error('خطأ في Advanced Voice Engine:', error);
        }
    }

    // تحميل وحدة Advanced Voice Engine إذا لم تكن محملة
    const script = document.createElement('script');
    script.src = 'assets/modules/voice/AdvancedVoiceEngine.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة Advanced Voice Engine');
        if (typeof window.AdvancedVoiceEngine !== 'undefined') {
            window.AdvancedVoiceEngine.toggleConversation();
        } else {
            // استخدام الوظيفة البديلة إذا فشل تحميل الوحدة
            fallbackVoiceConversation();
        }
    };
    script.onerror = () => {
        console.warn('⚠️ فشل في تحميل وحدة Advanced Voice Engine، استخدام الوظيفة البديلة');
        fallbackVoiceConversation();
    };
    document.head.appendChild(script);
}

function fallbackVoiceConversation() {
    console.log('🎤 استخدام المحادثة الصوتية البديلة...');

    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        recognition.lang = 'ar-SA';
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onstart = function() {
            console.log('🎤 بدء الاستماع...');
            alert('🎤 بدء الاستماع... تحدث الآن');
        };

        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            console.log('📝 النص المسموع:', transcript);

            // إضافة النص لحقل الإدخال
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.value = transcript;
            }

            // قراءة الرد
            const response = `تم استلام رسالتك الصوتية: ${transcript}`;
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(response);
                utterance.lang = 'ar-SA';
                speechSynthesis.speak(utterance);
            }
        };

        recognition.onerror = function(event) {
            console.error('❌ خطأ في التعرف على الصوت:', event.error);
            alert('❌ خطأ في التعرف على الصوت: ' + event.error);
        };

        recognition.start();
    } else {
        alert('❌ التعرف على الصوت غير مدعوم في هذا المتصفح');
    }
}

function startVoiceRecording() {
    console.log('🎙️ بدء التسجيل الصوتي المتقدم...');

    // إنشاء واجهة التسجيل المتقدمة
    createAdvancedRecordingInterface();
}

function createAdvancedRecordingInterface() {
    // إنشاء نافذة التسجيل المتقدمة
    const recordingWindow = document.createElement('div');
    recordingWindow.id = 'advancedRecordingWindow';
    recordingWindow.style.cssText = `
        position: fixed; top: 50%; left: 50%;
        transform: translate(-50%, -50%);
        width: 500px; height: 400px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px; padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        z-index: 1000; color: white; text-align: center;
        font-family: 'Arial', sans-serif;
    `;

    recordingWindow.innerHTML = `
        <h2 style="margin-top: 0; color: white;">🎙️ استوديو التسجيل المتقدم</h2>

        <div id="recordingControls" style="margin: 30px 0;">
            <button id="startRecBtn" onclick="startAdvancedRecording()" style="
                background: #ff4757; color: white; border: none;
                padding: 15px 30px; border-radius: 50px; cursor: pointer;
                font-size: 16px; margin: 10px; box-shadow: 0 4px 15px rgba(255,71,87,0.4);
                transition: all 0.3s ease;
            ">🎙️ بدء التسجيل</button>

            <button id="stopRecBtn" onclick="stopAdvancedRecording()" disabled style="
                background: #2f3542; color: white; border: none;
                padding: 15px 30px; border-radius: 50px; cursor: pointer;
                font-size: 16px; margin: 10px;
            ">⏹️ إيقاف التسجيل</button>
        </div>

        <div id="recordingStatus" style="margin: 20px 0; font-size: 18px;">
            جاهز للتسجيل...
        </div>

        <div id="audioVisualizer" style="
            width: 100%; height: 80px; background: rgba(255,255,255,0.1);
            border-radius: 10px; margin: 20px 0; position: relative;
            overflow: hidden;
        "></div>

        <div id="recordingOptions" style="margin: 20px 0;">
            <label style="display: block; margin: 10px 0;">
                🎚️ جودة التسجيل:
                <select id="audioQuality" style="margin-left: 10px; padding: 5px; border-radius: 5px;">
                    <option value="high">عالية (48kHz)</option>
                    <option value="medium" selected>متوسطة (44.1kHz)</option>
                    <option value="low">منخفضة (22kHz)</option>
                </select>
            </label>

            <label style="display: block; margin: 10px 0;">
                🔊 مستوى الحساسية:
                <input type="range" id="sensitivity" min="0" max="100" value="50" style="margin-left: 10px;">
            </label>
        </div>

        <div id="recordedAudios" style="margin: 20px 0; max-height: 100px; overflow-y: auto;">
            <!-- التسجيلات المحفوظة ستظهر هنا -->
        </div>

        <button onclick="closeAdvancedRecording()" style="
            background: #ff3838; color: white; border: none;
            padding: 10px 20px; border-radius: 10px; cursor: pointer;
            position: absolute; top: 10px; right: 15px;
        ">✕</button>
    `;

    document.body.appendChild(recordingWindow);

    // إضافة المتغيرات العامة للتسجيل
    window.currentRecording = {
        mediaRecorder: null,
        stream: null,
        audioChunks: [],
        isRecording: false,
        startTime: null
    };
}

// وظائف التسجيل المتقدمة
function startAdvancedRecording() {
    console.log('🎙️ بدء التسجيل المتقدم...');

    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        const quality = document.getElementById('audioQuality').value;
        const constraints = getAudioConstraints(quality);

        navigator.mediaDevices.getUserMedia(constraints)
            .then(stream => {
                window.currentRecording.stream = stream;
                window.currentRecording.mediaRecorder = new MediaRecorder(stream);
                window.currentRecording.audioChunks = [];
                window.currentRecording.startTime = Date.now();

                // إعداد أحداث التسجيل
                window.currentRecording.mediaRecorder.ondataavailable = event => {
                    window.currentRecording.audioChunks.push(event.data);
                };

                window.currentRecording.mediaRecorder.onstop = () => {
                    saveAdvancedRecording();
                };

                // بدء التسجيل
                window.currentRecording.mediaRecorder.start();
                window.currentRecording.isRecording = true;

                // تحديث الواجهة
                document.getElementById('startRecBtn').disabled = true;
                document.getElementById('stopRecBtn').disabled = false;
                document.getElementById('recordingStatus').textContent = '🔴 جاري التسجيل...';

                // بدء المؤشر المرئي
                startAudioVisualizer(stream);

                console.log('✅ بدء التسجيل بنجاح');
            })
            .catch(error => {
                console.error('❌ خطأ في الوصول للميكروفون:', error);
                alert('❌ فشل في الوصول للميكروفون: ' + error.message);
            });
    } else {
        alert('❌ تسجيل الصوت غير مدعوم في هذا المتصفح');
    }
}

function stopAdvancedRecording() {
    console.log('⏹️ إيقاف التسجيل...');

    if (window.currentRecording.mediaRecorder && window.currentRecording.isRecording) {
        window.currentRecording.mediaRecorder.stop();
        window.currentRecording.stream.getTracks().forEach(track => track.stop());
        window.currentRecording.isRecording = false;

        // تحديث الواجهة
        document.getElementById('startRecBtn').disabled = false;
        document.getElementById('stopRecBtn').disabled = true;
        document.getElementById('recordingStatus').textContent = '💾 جاري حفظ التسجيل...';

        console.log('✅ تم إيقاف التسجيل');
    }
}

function saveAdvancedRecording() {
    const audioBlob = new Blob(window.currentRecording.audioChunks, { type: 'audio/wav' });
    const audioUrl = URL.createObjectURL(audioBlob);
    const duration = Math.round((Date.now() - window.currentRecording.startTime) / 1000);
    const timestamp = new Date().toLocaleString('ar-SA');

    // إنشاء عنصر التسجيل المحفوظ
    const recordedAudiosDiv = document.getElementById('recordedAudios');
    const audioElement = document.createElement('div');
    audioElement.style.cssText = `
        background: rgba(255,255,255,0.1); border-radius: 10px;
        padding: 10px; margin: 5px 0; text-align: left;
    `;

    audioElement.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <strong>🎵 تسجيل ${timestamp}</strong><br>
                <small>المدة: ${duration} ثانية</small>
            </div>
            <div>
                <button onclick="playRecording('${audioUrl}')" style="
                    background: #2ed573; color: white; border: none;
                    padding: 5px 10px; border-radius: 5px; cursor: pointer; margin: 2px;
                ">▶️ تشغيل</button>
                <button onclick="downloadRecording('${audioUrl}', 'تسجيل_${Date.now()}.wav')" style="
                    background: #3742fa; color: white; border: none;
                    padding: 5px 10px; border-radius: 5px; cursor: pointer; margin: 2px;
                ">💾 تحميل</button>
                <button onclick="deleteRecording(this, '${audioUrl}')" style="
                    background: #ff3838; color: white; border: none;
                    padding: 5px 10px; border-radius: 5px; cursor: pointer; margin: 2px;
                ">🗑️ حذف</button>
            </div>
        </div>
    `;

    recordedAudiosDiv.appendChild(audioElement);

    // تحديث الحالة
    document.getElementById('recordingStatus').textContent = '✅ تم حفظ التسجيل بنجاح!';

    setTimeout(() => {
        document.getElementById('recordingStatus').textContent = 'جاهز للتسجيل...';
    }, 3000);
}

function getAudioConstraints(quality) {
    const constraints = { audio: true };

    switch(quality) {
        case 'high':
            constraints.audio = {
                sampleRate: 48000,
                channelCount: 2,
                echoCancellation: true,
                noiseSuppression: true
            };
            break;
        case 'medium':
            constraints.audio = {
                sampleRate: 44100,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            };
            break;
        case 'low':
            constraints.audio = {
                sampleRate: 22050,
                channelCount: 1,
                echoCancellation: false,
                noiseSuppression: false
            };
            break;
    }

    return constraints;
}

function startAudioVisualizer(stream) {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const analyser = audioContext.createAnalyser();
    const source = audioContext.createMediaStreamSource(stream);

    source.connect(analyser);
    analyser.fftSize = 256;

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const visualizer = document.getElementById('audioVisualizer');

    function draw() {
        if (!window.currentRecording.isRecording) return;

        analyser.getByteFrequencyData(dataArray);

        // إنشاء أعمدة الصوت
        let bars = '';
        for (let i = 0; i < bufferLength; i += 8) {
            const barHeight = (dataArray[i] / 255) * 100;
            bars += `<div style="
                display: inline-block; width: 3px; height: ${barHeight}%;
                background: linear-gradient(to top, #ff6b6b, #4ecdc4, #45b7d1);
                margin: 0 1px; border-radius: 2px;
                animation: pulse 0.1s ease-in-out;
            "></div>`;
        }

        visualizer.innerHTML = bars;
        requestAnimationFrame(draw);
    }

    draw();
}

// وظائف مساعدة للتسجيلات
function playRecording(audioUrl) {
    const audio = new Audio(audioUrl);
    audio.play();
}

function downloadRecording(audioUrl, filename) {
    const a = document.createElement('a');
    a.href = audioUrl;
    a.download = filename;
    a.click();
}

function deleteRecording(element, audioUrl) {
    if (confirm('هل تريد حذف هذا التسجيل؟')) {
        URL.revokeObjectURL(audioUrl);
        element.parentElement.parentElement.parentElement.remove();
    }
}

function closeAdvancedRecording() {
    // إيقاف التسجيل إذا كان جارياً
    if (window.currentRecording && window.currentRecording.isRecording) {
        stopAdvancedRecording();
    }

    // إزالة النافذة
    const window_elem = document.getElementById('advancedRecordingWindow');
    if (window_elem) {
        window_elem.remove();
    }

    // تنظيف الموارد
    if (window.currentRecording) {
        window.currentRecording = null;
    }
}

function executeFunction(functionName, description) {
    console.log(`🚀 تنفيذ: ${functionName} - ${description}`);

    try {
        // استدعاء الوظيفة المحلية مباشرة
        switch(functionName) {
            case 'sendMessage':
                sendMessage();
                break;
            case 'togglePureVoiceMode':
                togglePureVoiceMode();
                break;
            case 'toggleVoiceConversation':
                toggleVoiceConversation();
                break;
            case 'startVoiceRecording':
                startVoiceRecording();
                break;
            case 'handleScreenShare':
                handleScreenShare();
                break;
            case 'handleVideoUpload':
                handleVideoUpload();
                break;
            case 'handleVideoAnalyze':
                handleVideoAnalyze();
                break;
            case 'handle3DDisplay':
                handle3DDisplay();
                break;
            case 'generateSummary':
                generateSummary();
                break;
            case 'toggleBugBountyMode':
                toggleBugBountyMode();
                break;
            case 'toggleFileCreatorMode':
                toggleFileCreatorMode();
                break;
            case 'toggleAIImprove':
                toggleAIImprove();
                break;
            case 'openAPIConfig':
                openAPIConfig();
                break;
            case 'openHFConfig':
                openHFConfig();
                break;
            case 'openVoiceSettings':
                openVoiceSettings();
                break;
            case 'startAdvancedSpeechToText':
                startAdvancedSpeechToText();
                break;
            case 'speakCurrentText':
                speakCurrentText();
                break;
            case 'toggleVoiceMode':
                toggleAdvancedVoiceMode();
                break;
            default:
                console.error(`❌ وظيفة غير معروفة: ${functionName}`);
                alert(`وظيفة ${description} غير معروفة`);
                return;
        }

        console.log(`✅ تم تنفيذ ${description} بنجاح`);

    } catch (error) {
        console.error(`❌ خطأ في تنفيذ ${functionName}:`, error);
        alert(`خطأ في ${description}: ${error.message}`);
    }
}

function handleScreenShare() {
    console.log('🖥️ بدء مشاركة الشاشة المتقدمة...');

    // استدعاء وحدة Screen Share الحقيقية أولاً
    if (typeof window.ScreenShare !== 'undefined') {
        try {
            window.ScreenShare.startSharing();
            return;
        } catch (error) {
            console.error('خطأ في Screen Share Module:', error);
        }
    }

    // تحميل وحدة Screen Share إذا لم تكن محملة
    const script = document.createElement('script');
    script.src = 'assets/modules/screenShare.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة Screen Share');
        if (typeof window.ScreenShare !== 'undefined') {
            window.ScreenShare.startSharing();
        } else {
            // استخدام الوظيفة البديلة إذا فشل تحميل الوحدة
            fallbackScreenShare();
        }
    };
    script.onerror = () => {
        console.warn('⚠️ فشل في تحميل وحدة Screen Share، استخدام الوظيفة البديلة');
        fallbackScreenShare();
    };
    document.head.appendChild(script);
}

function fallbackScreenShare() {
    console.log('🖥️ استخدام مشاركة الشاشة البديلة...');

    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        navigator.mediaDevices.getDisplayMedia({ video: true })
            .then(stream => {
                console.log('✅ تم الحصول على مشاركة الشاشة');

                // إنشاء عنصر فيديو لعرض الشاشة
                let screenVideo = document.getElementById('screenVideo');
                if (!screenVideo) {
                    screenVideo = document.createElement('video');
                    screenVideo.id = 'screenVideo';
                    screenVideo.autoplay = true;
                    screenVideo.style.cssText = `
                        position: fixed; top: 10px; right: 10px;
                        width: 400px; height: 250px;
                        border: 3px solid #007bff; border-radius: 10px;
                        z-index: 1000; background: black;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                    `;

                    // إضافة زر إغلاق
                    const closeBtn = document.createElement('button');
                    closeBtn.textContent = '✕ إغلاق';
                    closeBtn.style.cssText = `
                        position: absolute; top: -35px; right: 0;
                        background: #dc3545; color: white; border: none;
                        padding: 5px 10px; border-radius: 5px;
                        cursor: pointer; font-size: 12px;
                    `;
                    closeBtn.onclick = () => {
                        stream.getTracks().forEach(track => track.stop());
                        screenVideo.remove();
                        console.log('✅ تم إيقاف مشاركة الشاشة');
                    };

                    document.body.appendChild(screenVideo);
                    screenVideo.appendChild(closeBtn);
                }

                screenVideo.srcObject = stream;
                alert('✅ تم بدء مشاركة الشاشة بنجاح!');

                // إيقاف المشاركة عند انتهاء البث
                stream.getVideoTracks()[0].onended = () => {
                    if (screenVideo) screenVideo.remove();
                    console.log('✅ تم إنهاء مشاركة الشاشة');
                };
            })
            .catch(error => {
                console.error('❌ خطأ في مشاركة الشاشة:', error);
                alert('❌ فشل في بدء مشاركة الشاشة: ' + error.message);
            });
    } else {
        alert('❌ مشاركة الشاشة غير مدعومة في هذا المتصفح');
    }
}

function handleVideoUpload() {
    console.log('📹 تحميل فيديو...');

    // إنشاء input للملفات
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'video/*';

    fileInput.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            console.log('📁 تم اختيار الفيديو:', file.name);

            // إنشاء عنصر فيديو للعرض
            const video = document.createElement('video');
            video.src = URL.createObjectURL(file);
            video.controls = true;
            video.style.cssText = `
                position: fixed; top: 50%; left: 50%;
                transform: translate(-50%, -50%);
                max-width: 80%; max-height: 80%;
                z-index: 1000; border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            `;

            // إضافة زر إغلاق
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '✕ إغلاق';
            closeBtn.style.cssText = `
                position: absolute; top: -40px; right: 0;
                background: #dc3545; color: white; border: none;
                padding: 8px 15px; border-radius: 5px;
                cursor: pointer;
            `;
            closeBtn.onclick = () => {
                video.remove();
                URL.revokeObjectURL(video.src);
            };

            document.body.appendChild(video);
            video.appendChild(closeBtn);

            alert('✅ تم تحميل الفيديو بنجاح!');
        }
    };

    fileInput.click();
}

function handleVideoAnalyze() {
    console.log('📊 تحليل الفيديو...');

    // استدعاء وحدة تحليل الفيديو الحقيقية
    if (typeof window.VideoAnalyzer !== 'undefined') {
        try {
            window.VideoAnalyzer.startAnalysis();
        } catch (error) {
            console.error('خطأ في تحليل الفيديو:', error);
            // تحميل الوحدة إذا لم تكن محملة
            loadVideoAnalyzer();
        }
    } else {
        // تحميل وحدة تحليل الفيديو
        loadVideoAnalyzer();
    }
}

function loadVideoAnalyzer() {
    const script = document.createElement('script');
    script.src = 'assets/modules/videoAnalyzer.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة تحليل الفيديو');
        if (typeof window.VideoAnalyzer !== 'undefined') {
            window.VideoAnalyzer.startAnalysis();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة تحليل الفيديو');
        alert('❌ فشل في تحميل وحدة تحليل الفيديو');
    };
    document.head.appendChild(script);
}

function handle3DDisplay() {
    console.log('🎲 بدء العرض ثلاثي الأبعاد المتقدم...');

    // استدعاء وحدة AR Renderer الحقيقية أولاً
    if (typeof window.ARRenderer !== 'undefined') {
        try {
            window.ARRenderer.startRendering();
            return;
        } catch (error) {
            console.error('خطأ في AR Renderer Module:', error);
        }
    }

    // تحميل وحدة AR Renderer إذا لم تكن محملة
    const script = document.createElement('script');
    script.src = 'assets/modules/ar_renderer.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة AR Renderer');
        if (typeof window.ARRenderer !== 'undefined') {
            window.ARRenderer.startRendering();
        } else {
            // استخدام الوظيفة البديلة إذا فشل تحميل الوحدة
            fallback3DDisplay();
        }
    };
    script.onerror = () => {
        console.warn('⚠️ فشل في تحميل وحدة AR Renderer، استخدام الوظيفة البديلة');
        fallback3DDisplay();
    };
    document.head.appendChild(script);
}

function fallback3DDisplay() {
    console.log('🎲 استخدام العرض ثلاثي الأبعاد البديل...');

    // إنشاء نافذة العرض ثلاثي الأبعاد
    let display3D = document.getElementById('display3D');
    if (!display3D) {
        display3D = document.createElement('div');
        display3D.id = 'display3D';
        display3D.style.cssText = `
            position: fixed; top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            width: 600px; height: 500px;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            border-radius: 15px; padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            z-index: 1000; color: white; text-align: center;
        `;

        display3D.innerHTML = `
            <h3>🎲 العرض ثلاثي الأبعاد المتقدم</h3>
            <canvas id="canvas3D" width="550" height="350" style="border: 2px solid white; border-radius: 10px; background: #000;"></canvas>
            <br><br>
            <button onclick="this.parentElement.remove()" style="background: #dc3545; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; font-size: 14px;">إغلاق العرض</button>
        `;

        document.body.appendChild(display3D);

        // رسم مكعب ثلاثي الأبعاد متقدم
        const canvas = document.getElementById('canvas3D');
        const ctx = canvas.getContext('2d');

        function draw3DScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const time = Date.now() * 0.001;

            // رسم عدة مكعبات بألوان مختلفة
            for (let i = 0; i < 3; i++) {
                const size = 60 + i * 20;
                const angle = time + i * 0.5;
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1'];

                ctx.strokeStyle = colors[i];
                ctx.lineWidth = 2;

                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                const offsetX = Math.cos(time + i) * 50;
                const offsetY = Math.sin(time + i) * 30;

                // الوجه الأمامي
                ctx.strokeRect(
                    centerX - size + sin * 30 + offsetX,
                    centerY - size + cos * 20 + offsetY,
                    size * 2, size * 2
                );

                // الوجه الخلفي
                const depth = 40 + i * 10;
                ctx.strokeRect(
                    centerX - size + depth + sin * 30 + offsetX,
                    centerY - size - depth + cos * 20 + offsetY,
                    size * 2, size * 2
                );

                // خطوط الربط
                ctx.beginPath();
                ctx.moveTo(centerX - size + sin * 30 + offsetX, centerY - size + cos * 20 + offsetY);
                ctx.lineTo(centerX - size + depth + sin * 30 + offsetX, centerY - size - depth + cos * 20 + offsetY);
                ctx.moveTo(centerX + size + sin * 30 + offsetX, centerY - size + cos * 20 + offsetY);
                ctx.lineTo(centerX + size + depth + sin * 30 + offsetX, centerY - size - depth + cos * 20 + offsetY);
                ctx.moveTo(centerX - size + sin * 30 + offsetX, centerY + size + cos * 20 + offsetY);
                ctx.lineTo(centerX - size + depth + sin * 30 + offsetX, centerY + size - depth + cos * 20 + offsetY);
                ctx.moveTo(centerX + size + sin * 30 + offsetX, centerY + size + cos * 20 + offsetY);
                ctx.lineTo(centerX + size + depth + sin * 30 + offsetX, centerY + size - depth + cos * 20 + offsetY);
                ctx.stroke();
            }
        }

        // تحديث الرسم
        const animationInterval = setInterval(() => {
            if (document.getElementById('display3D')) {
                draw3DScene();
            } else {
                clearInterval(animationInterval);
            }
        }, 50);
    }

    alert('✅ تم بدء العرض ثلاثي الأبعاد المتقدم!');
}

function generateSummary() {
    console.log('📋 توليد ملخص متقدم...');

    // استدعاء وحدة Summarizer الحقيقية أولاً
    if (typeof window.Summarizer !== 'undefined') {
        try {
            window.Summarizer.generateSummary();
            return;
        } catch (error) {
            console.error('خطأ في Summarizer Module:', error);
        }
    }

    // تحميل وحدة Summarizer إذا لم تكن محملة
    const script = document.createElement('script');
    script.src = 'assets/modules/summarizer.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة Summarizer');
        if (typeof window.Summarizer !== 'undefined') {
            window.Summarizer.generateSummary();
        } else {
            // استخدام الوظيفة البديلة إذا فشل تحميل الوحدة
            fallbackGenerateSummary();
        }
    };
    script.onerror = () => {
        console.warn('⚠️ فشل في تحميل وحدة Summarizer، استخدام الوظيفة البديلة');
        fallbackGenerateSummary();
    };
    document.head.appendChild(script);
}

function fallbackGenerateSummary() {
    console.log('📋 استخدام توليد الملخص البديل...');

    // إنشاء نافذة الملخص
    const summaryWindow = document.createElement('div');
    summaryWindow.style.cssText = `
        position: fixed; top: 50%; left: 50%;
        transform: translate(-50%, -50%);
        width: 500px; max-height: 400px;
        background: white; border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1000; padding: 20px;
        overflow-y: auto;
    `;

    summaryWindow.innerHTML = `
        <h3 style="color: #333; margin-top: 0;">📋 ملخص المحادثة</h3>
        <div style="color: #666; line-height: 1.6;">
            <p><strong>📊 إحصائيات المحادثة:</strong></p>
            <ul>
                <li>عدد الرسائل: ${document.querySelectorAll('.message').length}</li>
                <li>وقت البدء: ${new Date().toLocaleString('ar-SA')}</li>
                <li>الحالة: نشطة</li>
            </ul>

            <p><strong>🔧 الوظائف المستخدمة:</strong></p>
            <ul>
                <li>إرسال الرسائل ✅</li>
                <li>المحادثة الصوتية ✅</li>
                <li>مشاركة الشاشة ✅</li>
                <li>العرض ثلاثي الأبعاد ✅</li>
            </ul>

            <p><strong>💡 اقتراحات:</strong></p>
            <ul>
                <li>جرب وضع المحادثة الصوتية الخالصة</li>
                <li>استخدم مشاركة الشاشة للعروض التقديمية</li>
                <li>اكتشف المزيد من الوظائف المتقدمة</li>
            </ul>
        </div>
        <button onclick="this.parentElement.remove()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">إغلاق</button>
    `;

    document.body.appendChild(summaryWindow);
    alert('✅ تم إنشاء ملخص المحادثة!');
}

function toggleBugBountyMode() {
    console.log('🔍 تفعيل وضع Bug Bounty...');

    // استدعاء وحدة Bug Bounty الحقيقية
    if (typeof window.BugBountyCore !== 'undefined') {
        try {
            window.BugBountyCore.toggleMode();
        } catch (error) {
            console.error('خطأ في Bug Bounty:', error);
            loadBugBountyCore();
        }
    } else {
        loadBugBountyCore();
    }
}

function loadBugBountyCore() {
    const script = document.createElement('script');
    script.src = 'assets/modules/bugbounty/BugBountyCore.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة Bug Bounty');
        if (typeof window.BugBountyCore !== 'undefined') {
            window.BugBountyCore.toggleMode();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة Bug Bounty');
        alert('❌ فشل في تحميل وحدة Bug Bounty');
    };
    document.head.appendChild(script);
}

function toggleFileCreatorMode() {
    console.log('📁 تفعيل وضع إنشاء الملفات...');

    // استدعاء وحدة File Creator الحقيقية
    if (typeof window.FileCreatorCore !== 'undefined') {
        try {
            window.FileCreatorCore.toggleMode();
        } catch (error) {
            console.error('خطأ في File Creator:', error);
            loadFileCreatorCore();
        }
    } else {
        loadFileCreatorCore();
    }
}

function loadFileCreatorCore() {
    const script = document.createElement('script');
    script.src = 'assets/modules/fileCreator/FileCreatorCore.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة File Creator');
        if (typeof window.FileCreatorCore !== 'undefined') {
            window.FileCreatorCore.toggleMode();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة File Creator');
        alert('❌ فشل في تحميل وحدة File Creator');
    };
    document.head.appendChild(script);
}

function toggleAIImprove() {
    console.log('🤖 تفعيل وضع التحسين الذاتي...');

    // استدعاء وحدة AI Self Improve الحقيقية
    if (typeof window.AISelfImprove !== 'undefined') {
        try {
            window.AISelfImprove.toggleMode();
        } catch (error) {
            console.error('خطأ في AI Self Improve:', error);
            loadAISelfImprove();
        }
    } else {
        loadAISelfImprove();
    }
}

function loadAISelfImprove() {
    const script = document.createElement('script');
    script.src = 'assets/modules/ai_self_improve/AISelfImprove.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة AI Self Improve');
        if (typeof window.AISelfImprove !== 'undefined') {
            window.AISelfImprove.toggleMode();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة AI Self Improve');
        alert('❌ فشل في تحميل وحدة AI Self Improve');
    };
    document.head.appendChild(script);
}

function openAPIConfig() {
    console.log('⚙️ فتح إعدادات API...');

    // استدعاء وحدة API Config الحقيقية
    if (typeof window.APIConfigInterface !== 'undefined') {
        try {
            window.APIConfigInterface.openConfig();
        } catch (error) {
            console.error('خطأ في API Config:', error);
            loadAPIConfig();
        }
    } else {
        loadAPIConfig();
    }
}

function loadAPIConfig() {
    const script = document.createElement('script');
    script.src = 'assets/modules/api_integration/APIConfigInterface.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة API Config');
        if (typeof window.APIConfigInterface !== 'undefined') {
            window.APIConfigInterface.openConfig();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة API Config');
        alert('❌ فشل في تحميل وحدة API Config');
    };
    document.head.appendChild(script);
}

function openHFConfig() {
    console.log('🤗 فتح إعدادات Hugging Face...');

    // استدعاء وحدة Hugging Face الحقيقية
    if (typeof window.HuggingFaceSettings !== 'undefined') {
        try {
            window.HuggingFaceSettings.openSettings();
        } catch (error) {
            console.error('خطأ في Hugging Face Settings:', error);
            loadHuggingFaceSettings();
        }
    } else {
        loadHuggingFaceSettings();
    }
}

function loadHuggingFaceSettings() {
    const script = document.createElement('script');
    script.src = 'assets/modules/huggingface_integration/HuggingFaceSettings.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة Hugging Face Settings');
        if (typeof window.HuggingFaceSettings !== 'undefined') {
            window.HuggingFaceSettings.openSettings();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة Hugging Face Settings');
        alert('❌ فشل في تحميل وحدة Hugging Face Settings');
    };
    document.head.appendChild(script);
}

function openVoiceSettings() {
    console.log('🔊 فتح إعدادات الصوت المتقدمة...');

    // استدعاء وحدة Voice Settings الحقيقية
    if (typeof window.VoiceSettings !== 'undefined') {
        try {
            window.VoiceSettings.openSettings();
        } catch (error) {
            console.error('خطأ في Voice Settings:', error);
            loadVoiceSettings();
        }
    } else {
        loadVoiceSettings();
    }
}

function loadVoiceSettings() {
    const script = document.createElement('script');
    script.src = 'assets/modules/voice/VoiceSettings.js';
    script.onload = () => {
        console.log('✅ تم تحميل وحدة Voice Settings');
        if (typeof window.VoiceSettings !== 'undefined') {
            window.VoiceSettings.openSettings();
        }
    };
    script.onerror = () => {
        console.error('❌ فشل في تحميل وحدة Voice Settings');
        alert('❌ فشل في تحميل وحدة Voice Settings');
    };
    document.head.appendChild(script);
}

/**
 * دالة تشخيص شاملة للنظام
 */
function diagnoseSystem() {
    console.log('🔬 بدء تشخيص شامل للنظام...');
    
    // فحص الوظائف المطلوبة
    const requiredFunctions = [
        'sendMessage', 'toggleVoiceConversation', 'togglePureVoiceMode',
        'startVoiceRecording', 'handleScreenShare', 'handleVideoUpload',
        'handleVideoAnalyze', 'handle3DDisplay', 'generateSummary',
        'toggleBugBountyMode', 'toggleFileCreatorMode', 'toggleAIImprove',
        'openAPIConfig', 'openHFConfig', 'openVoiceSettings'
    ];
    
    console.log('🧪 فحص الوظائف المطلوبة:');
    let availableFunctions = 0;
    
    requiredFunctions.forEach(funcName => {
        try {
            const func = eval(funcName);
            if (typeof func === 'function') {
                availableFunctions++;
                console.log(`✅ ${funcName}: متاحة`);
            } else {
                console.warn(`❌ ${funcName}: غير متاحة`);
            }
        } catch (e) {
            console.warn(`❌ ${funcName}: غير متاحة`);
        }
    });
    
    console.log(`📊 الوظائف المتاحة: ${availableFunctions}/${requiredFunctions.length}`);
    
    // فحص حالة DOM
    console.log('🌐 حالة DOM:', document.readyState);
    
    return {
        functionsAvailable: availableFunctions,
        totalFunctions: requiredFunctions.length,
        domReady: document.readyState === 'complete'
    };
}

// تشغيل التشخيص بعد 2 ثانية من التحميل
setTimeout(() => {
    const diagnosis = diagnoseSystem();
    console.log('📋 نتائج التشخيص:', diagnosis);
}, 2000);

// وظائف صوتية إضافية للمحادثة النصية

// تحويل النص إلى كلام
function speakText(text, options = {}) {
    console.log('🔊 تحويل النص إلى كلام:', text);

    if ('speechSynthesis' in window) {
        // إيقاف أي كلام سابق
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);

        // إعدادات الصوت
        utterance.lang = options.lang || 'ar-SA';
        utterance.rate = options.rate || 1;
        utterance.pitch = options.pitch || 1;
        utterance.volume = options.volume || 0.8;

        // اختيار صوت عربي إذا كان متاحاً
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(voice =>
            voice.lang.includes('ar') || voice.name.includes('Arabic')
        );

        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }

        // أحداث الكلام
        utterance.onstart = () => {
            console.log('🔊 بدء الكلام');
            showSpeakingIndicator();
        };

        utterance.onend = () => {
            console.log('✅ انتهى الكلام');
            hideSpeakingIndicator();
        };

        utterance.onerror = (error) => {
            console.error('❌ خطأ في الكلام:', error);
            hideSpeakingIndicator();
        };

        speechSynthesis.speak(utterance);
    } else {
        alert('❌ تحويل النص إلى كلام غير مدعوم في هذا المتصفح');
    }
}

// إيقاف الكلام
function stopSpeaking() {
    if ('speechSynthesis' in window) {
        speechSynthesis.cancel();
        hideSpeakingIndicator();
        console.log('⏹️ تم إيقاف الكلام');
    }
}

// مؤشر الكلام
function showSpeakingIndicator() {
    let indicator = document.getElementById('speakingIndicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'speakingIndicator';
        indicator.style.cssText = `
            position: fixed; top: 20px; right: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white; padding: 10px 20px; border-radius: 25px;
            z-index: 1000; font-size: 14px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            animation: pulse 1s infinite;
        `;
        indicator.innerHTML = '🔊 جاري التحدث...';
        document.body.appendChild(indicator);

        // إضافة CSS للأنيميشن
        if (!document.getElementById('speakingAnimation')) {
            const style = document.createElement('style');
            style.id = 'speakingAnimation';
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); opacity: 1; }
                    50% { transform: scale(1.05); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }
}

function hideSpeakingIndicator() {
    const indicator = document.getElementById('speakingIndicator');
    if (indicator) {
        indicator.remove();
    }
}

// إضافة أزرار صوتية للرسائل
function addVoiceButtonsToMessages() {
    const messages = document.querySelectorAll('.message:not(.voice-enabled)');

    messages.forEach(message => {
        message.classList.add('voice-enabled');

        // إنشاء زر القراءة
        const voiceBtn = document.createElement('button');
        voiceBtn.innerHTML = '🔊';
        voiceBtn.title = 'قراءة الرسالة';
        voiceBtn.style.cssText = `
            background: #007bff; color: white; border: none;
            padding: 5px 8px; border-radius: 50%; cursor: pointer;
            margin-left: 10px; font-size: 12px;
            transition: all 0.3s ease;
        `;

        voiceBtn.onmouseover = () => {
            voiceBtn.style.background = '#0056b3';
            voiceBtn.style.transform = 'scale(1.1)';
        };

        voiceBtn.onmouseout = () => {
            voiceBtn.style.background = '#007bff';
            voiceBtn.style.transform = 'scale(1)';
        };

        voiceBtn.onclick = () => {
            const messageText = message.textContent.replace(/🔊|⏹️/g, '').trim();
            speakText(messageText);

            // تغيير الزر إلى إيقاف مؤقتاً
            const originalHTML = voiceBtn.innerHTML;
            voiceBtn.innerHTML = '⏹️';
            voiceBtn.title = 'إيقاف القراءة';

            setTimeout(() => {
                voiceBtn.innerHTML = originalHTML;
                voiceBtn.title = 'قراءة الرسالة';
            }, 1000);
        };

        message.appendChild(voiceBtn);
    });
}

// مراقبة الرسائل الجديدة وإضافة أزرار صوتية لها
function observeNewMessages() {
    const chatContainer = document.getElementById('chatContainer');
    if (chatContainer) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    addVoiceButtonsToMessages();
                }
            });
        });

        observer.observe(chatContainer, {
            childList: true,
            subtree: true
        });

        console.log('✅ تم تفعيل مراقبة الرسائل الجديدة');
    }
}

// تحويل الكلام إلى نص متقدم
function startAdvancedSpeechToText() {
    console.log('🎤 بدء التحويل المتقدم من الكلام إلى النص...');

    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        // إعدادات متقدمة
        recognition.lang = 'ar-SA';
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.maxAlternatives = 3;

        // إنشاء واجهة التحويل
        createSpeechToTextInterface(recognition);

    } else {
        alert('❌ التعرف على الكلام غير مدعوم في هذا المتصفح');
    }
}

function createSpeechToTextInterface(recognition) {
    const sttWindow = document.createElement('div');
    sttWindow.id = 'speechToTextWindow';
    sttWindow.style.cssText = `
        position: fixed; top: 50%; left: 50%;
        transform: translate(-50%, -50%);
        width: 500px; height: 400px;
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        border-radius: 20px; padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        z-index: 1000; color: white; text-align: center;
    `;

    sttWindow.innerHTML = `
        <h2 style="margin-top: 0;">🎤 تحويل الكلام إلى نص</h2>

        <div id="sttControls" style="margin: 20px 0;">
            <button id="startSttBtn" onclick="startSTT()" style="
                background: #00b894; color: white; border: none;
                padding: 15px 30px; border-radius: 50px; cursor: pointer;
                font-size: 16px; margin: 10px;
            ">🎤 بدء الاستماع</button>

            <button id="stopSttBtn" onclick="stopSTT()" disabled style="
                background: #636e72; color: white; border: none;
                padding: 15px 30px; border-radius: 50px; cursor: pointer;
                font-size: 16px; margin: 10px;
            ">⏹️ إيقاف الاستماع</button>
        </div>

        <div id="sttStatus" style="margin: 15px 0; font-size: 16px;">
            جاهز للاستماع...
        </div>

        <textarea id="sttResult" placeholder="النص المحول سيظهر هنا..." style="
            width: 90%; height: 150px; padding: 15px;
            border: none; border-radius: 10px; font-size: 14px;
            resize: vertical; font-family: Arial, sans-serif;
        "></textarea>

        <div style="margin: 20px 0;">
            <button onclick="copySTTResult()" style="
                background: #6c5ce7; color: white; border: none;
                padding: 10px 20px; border-radius: 10px; cursor: pointer; margin: 5px;
            ">📋 نسخ النص</button>

            <button onclick="sendSTTToChat()" style="
                background: #fd79a8; color: white; border: none;
                padding: 10px 20px; border-radius: 10px; cursor: pointer; margin: 5px;
            ">💬 إرسال للدردشة</button>

            <button onclick="clearSTTResult()" style="
                background: #e17055; color: white; border: none;
                padding: 10px 20px; border-radius: 10px; cursor: pointer; margin: 5px;
            ">🗑️ مسح النص</button>
        </div>

        <button onclick="closeSpeechToText()" style="
            background: #d63031; color: white; border: none;
            padding: 8px 15px; border-radius: 8px; cursor: pointer;
            position: absolute; top: 15px; right: 15px;
        ">✕</button>
    `;

    document.body.appendChild(sttWindow);

    // حفظ مرجع للتعرف على الكلام
    window.currentSTT = {
        recognition: recognition,
        isListening: false,
        finalTranscript: '',
        interimTranscript: ''
    };

    // إعداد أحداث التعرف على الكلام
    setupSpeechRecognitionEvents();
}

function setupSpeechRecognitionEvents() {
    const recognition = window.currentSTT.recognition;

    recognition.onstart = () => {
        console.log('🎤 بدء الاستماع...');
        window.currentSTT.isListening = true;
        document.getElementById('startSttBtn').disabled = true;
        document.getElementById('stopSttBtn').disabled = false;
        document.getElementById('sttStatus').textContent = '🔴 جاري الاستماع...';
    };

    recognition.onresult = (event) => {
        let interimTranscript = '';
        let finalTranscript = window.currentSTT.finalTranscript;

        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;

            if (event.results[i].isFinal) {
                finalTranscript += transcript + ' ';
            } else {
                interimTranscript += transcript;
            }
        }

        window.currentSTT.finalTranscript = finalTranscript;
        window.currentSTT.interimTranscript = interimTranscript;

        // تحديث النص في الواجهة
        const resultArea = document.getElementById('sttResult');
        if (resultArea) {
            resultArea.value = finalTranscript + interimTranscript;
        }
    };

    recognition.onerror = (event) => {
        console.error('❌ خطأ في التعرف على الكلام:', event.error);
        document.getElementById('sttStatus').textContent = `❌ خطأ: ${event.error}`;
        resetSTTControls();
    };

    recognition.onend = () => {
        console.log('✅ انتهى الاستماع');
        window.currentSTT.isListening = false;
        document.getElementById('sttStatus').textContent = 'تم إيقاف الاستماع';
        resetSTTControls();
    };
}

// وظائف التحكم في تحويل الكلام إلى نص
function startSTT() {
    if (window.currentSTT && !window.currentSTT.isListening) {
        window.currentSTT.recognition.start();
    }
}

function stopSTT() {
    if (window.currentSTT && window.currentSTT.isListening) {
        window.currentSTT.recognition.stop();
    }
}

function resetSTTControls() {
    document.getElementById('startSttBtn').disabled = false;
    document.getElementById('stopSttBtn').disabled = true;
}

function copySTTResult() {
    const resultArea = document.getElementById('sttResult');
    if (resultArea && resultArea.value.trim()) {
        navigator.clipboard.writeText(resultArea.value).then(() => {
            alert('✅ تم نسخ النص إلى الحافظة');
        }).catch(() => {
            // طريقة بديلة للنسخ
            resultArea.select();
            document.execCommand('copy');
            alert('✅ تم نسخ النص');
        });
    } else {
        alert('❌ لا يوجد نص للنسخ');
    }
}

function sendSTTToChat() {
    const resultArea = document.getElementById('sttResult');
    const messageInput = document.getElementById('messageInput');

    if (resultArea && resultArea.value.trim() && messageInput) {
        messageInput.value = resultArea.value.trim();
        alert('✅ تم إرسال النص إلى حقل الدردشة');
        closeSpeechToText();
    } else {
        alert('❌ لا يوجد نص للإرسال أو حقل الدردشة غير موجود');
    }
}

function clearSTTResult() {
    const resultArea = document.getElementById('sttResult');
    if (resultArea) {
        resultArea.value = '';
        if (window.currentSTT) {
            window.currentSTT.finalTranscript = '';
            window.currentSTT.interimTranscript = '';
        }
    }
}

function closeSpeechToText() {
    // إيقاف الاستماع إذا كان جارياً
    if (window.currentSTT && window.currentSTT.isListening) {
        stopSTT();
    }

    // إزالة النافذة
    const sttWindow = document.getElementById('speechToTextWindow');
    if (sttWindow) {
        sttWindow.remove();
    }

    // تنظيف الموارد
    if (window.currentSTT) {
        window.currentSTT = null;
    }
}

// إضافة أزرار صوتية إضافية للواجهة
function addAdvancedVoiceButtons() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput && !document.getElementById('voiceButtonsContainer')) {
        const container = document.createElement('div');
        container.id = 'voiceButtonsContainer';
        container.style.cssText = `
            display: flex; gap: 10px; margin: 10px 0;
            justify-content: center; flex-wrap: wrap;
        `;

        // زر تحويل الكلام إلى نص
        const sttBtn = document.createElement('button');
        sttBtn.innerHTML = '🎤 كلام → نص';
        sttBtn.title = 'تحويل الكلام إلى نص';
        sttBtn.style.cssText = `
            background: #28a745; color: white; border: none;
            padding: 8px 15px; border-radius: 20px; cursor: pointer;
            font-size: 12px; transition: all 0.3s ease;
        `;
        sttBtn.onclick = () => startAdvancedSpeechToText();

        // زر قراءة النص المكتوب
        const ttsBtn = document.createElement('button');
        ttsBtn.innerHTML = '🔊 قراءة النص';
        ttsBtn.title = 'قراءة النص المكتوب';
        ttsBtn.style.cssText = `
            background: #17a2b8; color: white; border: none;
            padding: 8px 15px; border-radius: 20px; cursor: pointer;
            font-size: 12px; transition: all 0.3s ease;
        `;
        ttsBtn.onclick = () => {
            const text = messageInput.value.trim();
            if (text) {
                speakText(text);
            } else {
                alert('❌ لا يوجد نص للقراءة');
            }
        };

        // زر إيقاف الكلام
        const stopBtn = document.createElement('button');
        stopBtn.innerHTML = '⏹️ إيقاف';
        stopBtn.title = 'إيقاف الكلام';
        stopBtn.style.cssText = `
            background: #dc3545; color: white; border: none;
            padding: 8px 15px; border-radius: 20px; cursor: pointer;
            font-size: 12px; transition: all 0.3s ease;
        `;
        stopBtn.onclick = () => stopSpeaking();

        // زر التسجيل المتقدم
        const recordBtn = document.createElement('button');
        recordBtn.innerHTML = '🎙️ تسجيل متقدم';
        recordBtn.title = 'فتح استوديو التسجيل';
        recordBtn.style.cssText = `
            background: #6f42c1; color: white; border: none;
            padding: 8px 15px; border-radius: 20px; cursor: pointer;
            font-size: 12px; transition: all 0.3s ease;
        `;
        recordBtn.onclick = () => startVoiceRecording();

        container.appendChild(sttBtn);
        container.appendChild(ttsBtn);
        container.appendChild(stopBtn);
        container.appendChild(recordBtn);

        // إدراج الحاوية بعد حقل الإدخال
        messageInput.parentNode.insertBefore(container, messageInput.nextSibling);
    }
}

// تفعيل جميع الوظائف الصوتية عند تحميل الصفحة
function initializeVoiceFeatures() {
    console.log('🔊 تفعيل الوظائف الصوتية...');

    // إضافة أزرار صوتية للرسائل الموجودة
    addVoiceButtonsToMessages();

    // مراقبة الرسائل الجديدة
    observeNewMessages();

    // إضافة أزرار صوتية متقدمة
    addAdvancedVoiceButtons();

    console.log('✅ تم تفعيل جميع الوظائف الصوتية');
}

// إضافة وظائف صوتية للأزرار الموجودة
function addSoundEffectsToButtons() {
    const buttons = document.querySelectorAll('button:not(.sound-enabled)');

    buttons.forEach(button => {
        button.classList.add('sound-enabled');

        // صوت النقر
        button.addEventListener('click', () => {
            playClickSound();
        });

        // صوت التمرير
        button.addEventListener('mouseenter', () => {
            playHoverSound();
        });
    });
}

// تشغيل أصوات التفاعل
function playClickSound() {
    // إنشاء صوت نقر بسيط
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
}

function playHoverSound() {
    // إنشاء صوت تمرير بسيط
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);

    gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.05);
}

// إضافة إشعارات صوتية للرسائل الجديدة
function addMessageNotificationSounds() {
    const chatContainer = document.getElementById('chatContainer');
    if (chatContainer) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(node => {
                        if (node.classList && node.classList.contains('message')) {
                            playMessageNotificationSound();
                        }
                    });
                }
            });
        });

        observer.observe(chatContainer, {
            childList: true,
            subtree: true
        });
    }
}

function playMessageNotificationSound() {
    // صوت إشعار للرسائل الجديدة
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();

    // نغمة مزدوجة
    [440, 554.37].forEach((freq, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(freq, audioContext.currentTime + index * 0.2);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime + index * 0.2);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + index * 0.2 + 0.3);

        oscillator.start(audioContext.currentTime + index * 0.2);
        oscillator.stop(audioContext.currentTime + index * 0.2 + 0.3);
    });
}

// إضافة اختصارات لوحة المفاتيح للوظائف الصوتية
function addVoiceKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
        // Ctrl + Shift + V = تحويل الكلام إلى نص
        if (event.ctrlKey && event.shiftKey && event.key === 'V') {
            event.preventDefault();
            startAdvancedSpeechToText();
        }

        // Ctrl + Shift + S = قراءة النص
        if (event.ctrlKey && event.shiftKey && event.key === 'S') {
            event.preventDefault();
            const messageInput = document.getElementById('messageInput');
            if (messageInput && messageInput.value.trim()) {
                speakText(messageInput.value.trim());
            }
        }

        // Ctrl + Shift + R = تسجيل صوتي
        if (event.ctrlKey && event.shiftKey && event.key === 'R') {
            event.preventDefault();
            startVoiceRecording();
        }

        // Escape = إيقاف الكلام
        if (event.key === 'Escape') {
            stopSpeaking();
        }
    });

    console.log('✅ تم تفعيل اختصارات لوحة المفاتيح الصوتية');
    console.log('🎤 Ctrl+Shift+V: تحويل الكلام إلى نص');
    console.log('🔊 Ctrl+Shift+S: قراءة النص');
    console.log('🎙️ Ctrl+Shift+R: تسجيل صوتي');
    console.log('⏹️ Escape: إيقاف الكلام');
}

// إضافة إعدادات صوتية سريعة
function addQuickVoiceSettings() {
    const settingsBtn = document.createElement('button');
    settingsBtn.innerHTML = '🔧 إعدادات صوتية';
    settingsBtn.title = 'إعدادات صوتية سريعة';
    settingsBtn.style.cssText = `
        position: fixed; bottom: 20px; left: 20px;
        background: #343a40; color: white; border: none;
        padding: 10px 15px; border-radius: 25px; cursor: pointer;
        font-size: 12px; z-index: 1000;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    `;

    settingsBtn.onclick = () => {
        showQuickVoiceSettings();
    };

    document.body.appendChild(settingsBtn);
}

function showQuickVoiceSettings() {
    const settingsPanel = document.createElement('div');
    settingsPanel.id = 'quickVoiceSettings';
    settingsPanel.style.cssText = `
        position: fixed; bottom: 80px; left: 20px;
        width: 300px; background: white; border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        z-index: 1001; padding: 20px;
    `;

    settingsPanel.innerHTML = `
        <h4 style="margin-top: 0; color: #333;">🔧 إعدادات صوتية سريعة</h4>

        <div style="margin: 15px 0;">
            <label style="display: block; margin-bottom: 5px; color: #666;">🔊 مستوى الصوت:</label>
            <input type="range" id="voiceVolume" min="0" max="1" step="0.1" value="0.8" style="width: 100%;">
            <span id="volumeValue" style="font-size: 12px; color: #999;">80%</span>
        </div>

        <div style="margin: 15px 0;">
            <label style="display: block; margin-bottom: 5px; color: #666;">⚡ سرعة الكلام:</label>
            <input type="range" id="voiceRate" min="0.5" max="2" step="0.1" value="1" style="width: 100%;">
            <span id="rateValue" style="font-size: 12px; color: #999;">عادي</span>
        </div>

        <div style="margin: 15px 0;">
            <label style="display: block; margin-bottom: 5px; color: #666;">🎵 نبرة الصوت:</label>
            <input type="range" id="voicePitch" min="0" max="2" step="0.1" value="1" style="width: 100%;">
            <span id="pitchValue" style="font-size: 12px; color: #999;">عادي</span>
        </div>

        <div style="margin: 15px 0;">
            <label style="display: block; margin-bottom: 5px; color: #666;">🌍 اللغة:</label>
            <select id="voiceLanguage" style="width: 100%; padding: 5px; border: 1px solid #ddd; border-radius: 5px;">
                <option value="ar-SA">العربية (السعودية)</option>
                <option value="ar-EG">العربية (مصر)</option>
                <option value="en-US">English (US)</option>
                <option value="en-GB">English (UK)</option>
            </select>
        </div>

        <div style="margin: 15px 0;">
            <button onclick="testVoiceSettings()" style="
                background: #28a745; color: white; border: none;
                padding: 8px 15px; border-radius: 8px; cursor: pointer; margin: 5px;
            ">🧪 اختبار الصوت</button>

            <button onclick="saveVoiceSettings()" style="
                background: #007bff; color: white; border: none;
                padding: 8px 15px; border-radius: 8px; cursor: pointer; margin: 5px;
            ">💾 حفظ</button>

            <button onclick="closeQuickVoiceSettings()" style="
                background: #dc3545; color: white; border: none;
                padding: 8px 15px; border-radius: 8px; cursor: pointer; margin: 5px;
            ">✕ إغلاق</button>
        </div>
    `;

    document.body.appendChild(settingsPanel);

    // إضافة مستمعي الأحداث للتحديث المباشر
    document.getElementById('voiceVolume').oninput = (e) => {
        document.getElementById('volumeValue').textContent = Math.round(e.target.value * 100) + '%';
    };

    document.getElementById('voiceRate').oninput = (e) => {
        const rate = parseFloat(e.target.value);
        let rateText = 'عادي';
        if (rate < 0.8) rateText = 'بطيء';
        else if (rate > 1.2) rateText = 'سريع';
        document.getElementById('rateValue').textContent = rateText;
    };

    document.getElementById('voicePitch').oninput = (e) => {
        const pitch = parseFloat(e.target.value);
        let pitchText = 'عادي';
        if (pitch < 0.8) pitchText = 'منخفض';
        else if (pitch > 1.2) pitchText = 'مرتفع';
        document.getElementById('pitchValue').textContent = pitchText;
    };
}

function testVoiceSettings() {
    const volume = document.getElementById('voiceVolume').value;
    const rate = document.getElementById('voiceRate').value;
    const pitch = document.getElementById('voicePitch').value;
    const lang = document.getElementById('voiceLanguage').value;

    const testText = lang.startsWith('ar') ?
        'مرحباً، هذا اختبار للإعدادات الصوتية الجديدة' :
        'Hello, this is a test of the new voice settings';

    speakText(testText, {
        volume: parseFloat(volume),
        rate: parseFloat(rate),
        pitch: parseFloat(pitch),
        lang: lang
    });
}

function saveVoiceSettings() {
    const settings = {
        volume: document.getElementById('voiceVolume').value,
        rate: document.getElementById('voiceRate').value,
        pitch: document.getElementById('voicePitch').value,
        lang: document.getElementById('voiceLanguage').value
    };

    localStorage.setItem('voiceSettings', JSON.stringify(settings));
    alert('✅ تم حفظ الإعدادات الصوتية');
}

function loadVoiceSettings() {
    const saved = localStorage.getItem('voiceSettings');
    if (saved) {
        const settings = JSON.parse(saved);
        window.defaultVoiceSettings = settings;
    }
}

function closeQuickVoiceSettings() {
    const panel = document.getElementById('quickVoiceSettings');
    if (panel) {
        panel.remove();
    }
}

// تشغيل الوظائف الصوتية بعد تحميل الصفحة
setTimeout(() => {
    initializeVoiceFeatures();
    addSoundEffectsToButtons();
    addMessageNotificationSounds();
    addVoiceKeyboardShortcuts();
    addQuickVoiceSettings();
    loadVoiceSettings();
    checkSavedVoiceMode();
}, 2000);

// وظائف إضافية للأزرار الصوتية
function speakCurrentText() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput && messageInput.value.trim()) {
        speakText(messageInput.value.trim());
    } else {
        alert('❌ لا يوجد نص للقراءة');
    }
}

function toggleAdvancedVoiceMode() {
    const voiceMode = localStorage.getItem('advancedVoiceMode') === 'true';

    if (!voiceMode) {
        // تفعيل الوضع الصوتي المتقدم
        localStorage.setItem('advancedVoiceMode', 'true');
        enableAdvancedVoiceMode();
        alert('✅ تم تفعيل الوضع الصوتي المتقدم');
    } else {
        // إيقاف الوضع الصوتي المتقدم
        localStorage.setItem('advancedVoiceMode', 'false');
        disableAdvancedVoiceMode();
        alert('❌ تم إيقاف الوضع الصوتي المتقدم');
    }
}

function enableAdvancedVoiceMode() {
    // تفعيل جميع الوظائف الصوتية
    document.body.classList.add('advanced-voice-mode');

    // إضافة مؤشر الوضع الصوتي
    let indicator = document.getElementById('voiceModeIndicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'voiceModeIndicator';
        indicator.style.cssText = `
            position: fixed; top: 10px; left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white; padding: 8px 20px; border-radius: 20px;
            z-index: 1000; font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        `;
        indicator.innerHTML = '🎤 الوضع الصوتي المتقدم مُفعل';
        document.body.appendChild(indicator);
    }

    // تفعيل الاستماع التلقائي للرسائل الجديدة
    enableAutoSpeakNewMessages();
}

function disableAdvancedVoiceMode() {
    document.body.classList.remove('advanced-voice-mode');

    // إزالة مؤشر الوضع الصوتي
    const indicator = document.getElementById('voiceModeIndicator');
    if (indicator) {
        indicator.remove();
    }

    // إيقاف الاستماع التلقائي
    disableAutoSpeakNewMessages();
}

function enableAutoSpeakNewMessages() {
    const chatContainer = document.getElementById('chatContainer');
    if (chatContainer) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(node => {
                        if (node.classList && node.classList.contains('assistant-message')) {
                            // قراءة رسائل المساعد تلقائياً
                            setTimeout(() => {
                                const messageText = node.textContent.replace(/🔊|⏹️/g, '').trim();
                                if (messageText) {
                                    speakText(messageText);
                                }
                            }, 1000);
                        }
                    });
                }
            });
        });

        observer.observe(chatContainer, {
            childList: true,
            subtree: true
        });

        window.autoSpeakObserver = observer;
    }
}

function disableAutoSpeakNewMessages() {
    if (window.autoSpeakObserver) {
        window.autoSpeakObserver.disconnect();
        window.autoSpeakObserver = null;
    }
}

// تحقق من الوضع الصوتي المحفوظ عند التحميل
function checkSavedVoiceMode() {
    const voiceMode = localStorage.getItem('advancedVoiceMode') === 'true';
    if (voiceMode) {
        enableAdvancedVoiceMode();
    }
}

// إضافة وظائف window للوصول العام
window.speakText = speakText;
window.stopSpeaking = stopSpeaking;
window.startAdvancedSpeechToText = startAdvancedSpeechToText;
window.startVoiceRecording = startVoiceRecording;
window.speakCurrentText = speakCurrentText;
window.toggleAdvancedVoiceMode = toggleAdvancedVoiceMode;

// إضافة وظائف التحكم في التسجيل لـ window
window.startAdvancedRecording = startAdvancedRecording;
window.stopAdvancedRecording = stopAdvancedRecording;
window.closeAdvancedRecording = closeAdvancedRecording;
window.playRecording = playRecording;
window.downloadRecording = downloadRecording;
window.deleteRecording = deleteRecording;

// إضافة وظائف التحكم في تحويل الكلام لـ window
window.startSTT = startSTT;
window.stopSTT = stopSTT;
window.copySTTResult = copySTTResult;
window.sendSTTToChat = sendSTTToChat;
window.clearSTTResult = clearSTTResult;
window.closeSpeechToText = closeSpeechToText;

// إضافة وظائف الإعدادات الصوتية لـ window
window.testVoiceSettings = testVoiceSettings;
window.saveVoiceSettings = saveVoiceSettings;
window.closeQuickVoiceSettings = closeQuickVoiceSettings;

console.log('✅ تم تحميل ملف إدارة أحداث الأزرار بنجاح!');
console.log('🎤 النظام الصوتي المتقدم جاهز للاستخدام');
console.log('🔊 جميع الوظائف الصوتية متاحة ومربوطة بالأزرار');
